{"type": "record", "name": "FinancialEvent", "namespace": "com.quantum.market.events", "doc": "Financial market event with cryptographic validation for quantum market intelligence", "fields": [{"name": "event_id", "type": "string", "doc": "Unique identifier for the event (UUID v4)"}, {"name": "timestamp", "type": "long", "logicalType": "timestamp-millis", "doc": "Event timestamp in milliseconds since epoch (UTC)"}, {"name": "event_type", "type": {"type": "enum", "name": "EventType", "symbols": ["PRICE_UPDATE", "VOLUME_SPIKE", "WHALE_MOVEMENT", "SOCIAL_SENTIMENT", "TECHNICAL_INDICATOR", "NEWS_EVENT", "REGULATORY_UPDATE"]}, "doc": "Type of financial event"}, {"name": "symbol", "type": "string", "doc": "Trading symbol (e.g., BTC, ETH, AAPL)"}, {"name": "exchange", "type": "string", "doc": "Exchange or data source identifier"}, {"name": "price_data", "type": ["null", {"type": "record", "name": "PriceData", "fields": [{"name": "price", "type": "double", "doc": "Current price in USD"}, {"name": "volume", "type": "double", "doc": "Trading volume"}, {"name": "market_cap", "type": ["null", "double"], "doc": "Market capitalization in USD"}, {"name": "change_24h", "type": ["null", "double"], "doc": "24-hour price change percentage"}, {"name": "bid", "type": ["null", "double"], "doc": "Bid price"}, {"name": "ask", "type": ["null", "double"], "doc": "Ask price"}]}], "default": null, "doc": "Price-related data for PRICE_UPDATE events"}, {"name": "sentiment_data", "type": ["null", {"type": "record", "name": "SentimentData", "fields": [{"name": "sentiment_score", "type": "double", "doc": "Sentiment score from -1.0 (negative) to 1.0 (positive)"}, {"name": "confidence", "type": "double", "doc": "Confidence level from 0.0 to 1.0"}, {"name": "platform", "type": "string", "doc": "Social media platform (reddit, twitter, etc.)"}, {"name": "post_count", "type": "int", "doc": "Number of posts analyzed"}, {"name": "engagement_score", "type": ["null", "double"], "doc": "Engagement score based on likes, shares, comments"}]}], "default": null, "doc": "Sentiment analysis data for SOCIAL_SENTIMENT events"}, {"name": "onchain_data", "type": ["null", {"type": "record", "name": "OnchainData", "fields": [{"name": "chain", "type": "string", "doc": "Blockchain network (ethereum, polygon, bsc, etc.)"}, {"name": "whale_movements", "type": ["null", "double"], "doc": "Large wallet movements in USD value"}, {"name": "large_transactions", "type": ["null", "int"], "doc": "Number of large transactions (>$100k)"}, {"name": "active_addresses", "type": ["null", "int"], "doc": "Number of active addresses in the last 24h"}, {"name": "transaction_volume", "type": ["null", "double"], "doc": "Total transaction volume in USD"}, {"name": "gas_usage", "type": ["null", "long"], "doc": "Total gas usage"}, {"name": "contract_interactions", "type": ["null", "int"], "doc": "Number of smart contract interactions"}]}], "default": null, "doc": "On-chain metrics for WHALE_MOVEMENT events"}, {"name": "technical_indicators", "type": ["null", {"type": "record", "name": "TechnicalIndicators", "fields": [{"name": "rsi", "type": ["null", "double"], "doc": "Relative Strength Index (0-100)"}, {"name": "macd", "type": ["null", "double"], "doc": "MACD value"}, {"name": "bollinger_upper", "type": ["null", "double"], "doc": "Bollinger Bands upper value"}, {"name": "bollinger_lower", "type": ["null", "double"], "doc": "Bollinger Bands lower value"}, {"name": "moving_average_20", "type": ["null", "double"], "doc": "20-period moving average"}, {"name": "moving_average_50", "type": ["null", "double"], "doc": "50-period moving average"}, {"name": "volume_profile", "type": ["null", "double"], "doc": "Volume profile indicator"}]}], "default": null, "doc": "Technical analysis indicators"}, {"name": "metadata", "type": {"type": "record", "name": "EventMetadata", "fields": [{"name": "source", "type": "string", "doc": "Data source identifier"}, {"name": "version", "type": "string", "doc": "Schema version"}, {"name": "processing_time", "type": "long", "logicalType": "timestamp-millis", "doc": "When the event was processed"}, {"name": "correlation_id", "type": ["null", "string"], "doc": "Correlation ID for tracking related events"}, {"name": "tags", "type": {"type": "array", "items": "string"}, "default": [], "doc": "Additional tags for categorization"}]}, "doc": "Event metadata for tracking and processing"}, {"name": "security", "type": {"type": "record", "name": "SecurityData", "fields": [{"name": "data_hash", "type": "string", "doc": "SHA-256 hash of the event data for integrity verification"}, {"name": "signature", "type": ["null", "string"], "doc": "Cryptographic signature for authenticity verification"}, {"name": "public_key_id", "type": ["null", "string"], "doc": "ID of the public key used for signature verification"}, {"name": "encryption_algorithm", "type": ["null", "string"], "doc": "Encryption algorithm used (if any)"}]}, "doc": "Security and integrity validation data"}, {"name": "quality_metrics", "type": {"type": "record", "name": "QualityMetrics", "fields": [{"name": "confidence_score", "type": "double", "doc": "Overall confidence in the data quality (0.0 to 1.0)"}, {"name": "latency_ms", "type": ["null", "int"], "doc": "Data latency in milliseconds from source"}, {"name": "anomaly_score", "type": ["null", "double"], "doc": "Anomaly detection score (0.0 to 1.0, higher = more anomalous)"}, {"name": "validation_status", "type": {"type": "enum", "name": "ValidationStatus", "symbols": ["VALID", "WARNING", "INVALID", "PENDING"]}, "doc": "Data validation status"}]}, "doc": "Data quality and validation metrics"}]}