{"type": "record", "name": "RiskAnalysisEvent", "namespace": "com.quantum.market.risk", "doc": "Risk analysis event containing quantum finance model outputs", "fields": [{"name": "analysis_id", "type": "string", "doc": "Unique identifier for the risk analysis (UUID v4)"}, {"name": "timestamp", "type": "long", "logicalType": "timestamp-millis", "doc": "Analysis timestamp in milliseconds since epoch (UTC)"}, {"name": "symbol", "type": "string", "doc": "Trading symbol being analyzed"}, {"name": "analysis_type", "type": {"type": "enum", "name": "AnalysisType", "symbols": ["MUTUAL_INFORMATION", "QUANTUM_TUNNELING", "VOLATILITY_PREDICTION", "CORRELATION_ANALYSIS", "ANOMALY_DETECTION", "RISK_ASSESSMENT"]}, "doc": "Type of risk analysis performed"}, {"name": "mutual_information", "type": ["null", {"type": "record", "name": "MutualInformationData", "fields": [{"name": "mi_score", "type": "double", "doc": "Mutual information score (0.0 to 1.0)"}, {"name": "threshold", "type": "double", "doc": "Threshold value used (typically 0.25)"}, {"name": "threshold_exceeded", "type": "boolean", "doc": "Whether the MI score exceeded the threshold"}, {"name": "symbol_pairs", "type": {"type": "array", "items": "string"}, "doc": "Other symbols included in the MI calculation"}, {"name": "window_size", "type": "int", "doc": "Time window size in minutes"}, {"name": "denoising_method", "type": "string", "doc": "Denoising method used (e.g., 'wavelet')"}, {"name": "network_topology", "type": ["null", "string"], "doc": "JSON representation of the MI network topology"}]}], "default": null, "doc": "Mutual information network analysis results"}, {"name": "quantum_tunneling", "type": ["null", {"type": "record", "name": "QuantumTunnelingData", "fields": [{"name": "tunneling_probability", "type": "double", "doc": "Quantum tunneling probability (0.0 to 1.0)"}, {"name": "volatility_window", "type": "int", "doc": "Volatility calculation window in days"}, {"name": "probability_threshold", "type": "double", "doc": "Probability threshold (typically 0.65)"}, {"name": "barrier_height", "type": "double", "doc": "Quantum barrier height calculation"}, {"name": "energy_levels", "type": {"type": "array", "items": "double"}, "doc": "Calculated energy levels for the quantum system"}, {"name": "prediction_horizon", "type": "int", "doc": "Prediction horizon in days"}]}], "default": null, "doc": "Quantum tunneling model predictions"}, {"name": "volatility_analysis", "type": ["null", {"type": "record", "name": "VolatilityAnalysis", "fields": [{"name": "current_volatility", "type": "double", "doc": "Current volatility measure"}, {"name": "predicted_volatility", "type": "double", "doc": "Predicted volatility"}, {"name": "volatility_regime", "type": {"type": "enum", "name": "VolatilityRegime", "symbols": ["LOW", "MEDIUM", "HIGH", "EXTREME"]}, "doc": "Current volatility regime classification"}, {"name": "garch_parameters", "type": ["null", "string"], "doc": "GARCH model parameters as JSON"}, {"name": "var_95", "type": ["null", "double"], "doc": "Value at Risk at 95% confidence level"}, {"name": "var_99", "type": ["null", "double"], "doc": "Value at Risk at 99% confidence level"}]}], "default": null, "doc": "Volatility analysis and predictions"}, {"name": "correlation_matrix", "type": ["null", {"type": "record", "name": "CorrelationMatrix", "fields": [{"name": "correlations", "type": {"type": "map", "values": "double"}, "doc": "Correlation coefficients with other symbols"}, {"name": "eigen_values", "type": {"type": "array", "items": "double"}, "doc": "Eigenvalues of the correlation matrix"}, {"name": "principal_components", "type": ["null", "string"], "doc": "Principal components analysis results as JSON"}, {"name": "market_beta", "type": ["null", "double"], "doc": "Beta coefficient relative to market"}]}], "default": null, "doc": "Correlation analysis with other assets"}, {"name": "anomaly_detection", "type": ["null", {"type": "record", "name": "AnomalyDetection", "fields": [{"name": "anomaly_score", "type": "double", "doc": "Anomaly score (0.0 to 1.0, higher = more anomalous)"}, {"name": "isolation_forest_score", "type": ["null", "double"], "doc": "Isolation Forest anomaly score"}, {"name": "statistical_outlier", "type": "boolean", "doc": "Whether the data point is a statistical outlier"}, {"name": "anomaly_type", "type": ["null", "string"], "doc": "Type of anomaly detected"}, {"name": "contributing_factors", "type": {"type": "array", "items": "string"}, "default": [], "doc": "Factors contributing to the anomaly"}]}], "default": null, "doc": "Anomaly detection results"}, {"name": "combined_risk_score", "type": {"type": "record", "name": "CombinedRiskScore", "fields": [{"name": "overall_risk", "type": "double", "doc": "Combined risk score (0.0 to 1.0)"}, {"name": "risk_level", "type": {"type": "enum", "name": "RiskLevel", "symbols": ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "VERY_HIGH", "EXTREME"]}, "doc": "Risk level classification"}, {"name": "entropy_weights", "type": {"type": "map", "values": "double"}, "doc": "Entropy-based weights used in combination"}, {"name": "confidence_interval", "type": {"type": "record", "name": "ConfidenceInterval", "fields": [{"name": "lower_bound", "type": "double"}, {"name": "upper_bound", "type": "double"}, {"name": "confidence_level", "type": "double"}]}, "doc": "Confidence interval for the risk score"}]}, "doc": "Combined risk assessment using entropy weighting"}, {"name": "model_metadata", "type": {"type": "record", "name": "ModelMetadata", "fields": [{"name": "model_versions", "type": {"type": "map", "values": "string"}, "doc": "Versions of models used in the analysis"}, {"name": "computation_time_ms", "type": "int", "doc": "Time taken for computation in milliseconds"}, {"name": "data_points_used", "type": "int", "doc": "Number of data points used in the analysis"}, {"name": "feature_importance", "type": ["null", "string"], "doc": "Feature importance scores as JSON"}]}, "doc": "<PERSON><PERSON><PERSON> about the models and computation"}, {"name": "validation", "type": {"type": "record", "name": "ValidationData", "fields": [{"name": "backtesting_score", "type": ["null", "double"], "doc": "Backtesting validation score"}, {"name": "cross_validation_score", "type": ["null", "double"], "doc": "Cross-validation score"}, {"name": "model_accuracy", "type": ["null", "double"], "doc": "Model accuracy on validation set"}, {"name": "validation_status", "type": {"type": "enum", "name": "ValidationStatus", "symbols": ["PASSED", "WARNING", "FAILED", "NOT_VALIDATED"]}, "doc": "Overall validation status"}]}, "doc": "Model validation and performance metrics"}, {"name": "security", "type": {"type": "record", "name": "SecurityData", "fields": [{"name": "data_hash", "type": "string", "doc": "SHA-256 hash of the analysis data"}, {"name": "signature", "type": ["null", "string"], "doc": "Cryptographic signature"}, {"name": "public_key_id", "type": ["null", "string"], "doc": "Public key identifier for signature verification"}]}, "doc": "Security and integrity validation"}]}