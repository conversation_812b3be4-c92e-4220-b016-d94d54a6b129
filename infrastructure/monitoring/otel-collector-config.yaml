# OpenTelemetry Collector Configuration for Quantum Market Intelligence Hub
# Financial-grade observability with comprehensive telemetry collection

receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
  
  prometheus:
    config:
      scrape_configs:
        - job_name: 'quantum-backend'
          static_configs:
            - targets: ['backend:8000']
          metrics_path: '/metrics'
          scrape_interval: 15s
        
        - job_name: 'quantum-timescaledb'
          static_configs:
            - targets: ['timescaledb:5432']
          scrape_interval: 30s
        
        - job_name: 'quantum-kafka'
          static_configs:
            - targets: ['kafka:9092']
          scrape_interval: 30s
        
        - job_name: 'quantum-redis'
          static_configs:
            - targets: ['redis:6379']
          scrape_interval: 30s

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  
  memory_limiter:
    limit_mib: 512
  
  resource:
    attributes:
      - key: service.name
        value: quantum-market-intelligence
        action: upsert
      - key: service.version
        value: "1.0.0"
        action: upsert
      - key: deployment.environment
        value: development
        action: upsert
  
  # Add financial-specific attributes
  attributes:
    actions:
      - key: financial.compliance
        value: "true"
        action: upsert
      - key: financial.data_classification
        value: "market_data"
        action: upsert

exporters:
  prometheus:
    endpoint: "0.0.0.0:8889"
    namespace: quantum_hub
    const_labels:
      environment: development
      service: quantum-market-intelligence
  
  jaeger:
    endpoint: jaeger:14250
    tls:
      insecure: true
  
  logging:
    loglevel: info
  
  # Export to Prometheus for long-term storage
  prometheusremotewrite:
    endpoint: "http://prometheus:9090/api/v1/write"
    headers:
      X-Prometheus-Remote-Write-Version: "0.1.0"

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch]
      exporters: [jaeger, logging]
    
    metrics:
      receivers: [otlp, prometheus]
      processors: [memory_limiter, resource, attributes, batch]
      exporters: [prometheus, logging]
    
    logs:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch]
      exporters: [logging]

  extensions: [health_check, pprof, zpages]

extensions:
  health_check:
    endpoint: 0.0.0.0:13133
  
  pprof:
    endpoint: 0.0.0.0:1777
  
  zpages:
    endpoint: 0.0.0.0:55679
