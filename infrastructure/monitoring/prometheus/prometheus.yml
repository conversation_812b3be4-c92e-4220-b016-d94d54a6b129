# Prometheus Configuration for Quantum Market Intelligence Hub
# Financial-grade metrics collection and alerting

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'quantum-market-intelligence'
    environment: 'development'

rule_files:
  - "rules/*.yml"

scrape_configs:
  # Quantum Market Intelligence Backend
  - job_name: 'quantum-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # OpenTelemetry Collector
  - job_name: 'otel-collector'
    static_configs:
      - targets: ['otel-collector:8888', 'otel-collector:8889']
    scrape_interval: 15s

  # System Metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

  # Prometheus Self-Monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s

  # TimescaleDB Metrics (if postgres_exporter is added)
  - job_name: 'timescaledb'
    static_configs:
      - targets: ['timescaledb:5432']
    scrape_interval: 30s
    metrics_path: '/metrics'
    params:
      format: ['prometheus']

  # Kafka Metrics (if JMX exporter is configured)
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9092']
    scrape_interval: 30s
    honor_labels: true

  # Redis Metrics (if redis_exporter is added)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

# Alertmanager configuration (if needed)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093

# Remote write configuration for long-term storage
# remote_write:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/write"
#     headers:
#       Authorization: "Bearer your-token"

# Remote read configuration
# remote_read:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/read"
#     headers:
#       Authorization: "Bearer your-token"
