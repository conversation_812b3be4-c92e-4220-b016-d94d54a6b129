-- Quantum Market Intelligence Database Initialization
-- Financial-grade TimescaleDB setup with hypertables and security

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Enable cryptographic functions
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create schemas for organized data management
CREATE SCHEMA IF NOT EXISTS market_data;
CREATE SCHEMA IF NOT EXISTS risk_analysis;
CREATE SCHEMA IF NOT EXISTS audit_logs;
CREATE SCHEMA IF NOT EXISTS quantum_models;

-- Set timezone to UTC for financial data consistency
SET timezone = 'UTC';

-- Market Data Tables
CREATE TABLE market_data.price_events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    exchange VARCHAR(50) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(20, 8) NOT NULL,
    market_cap DECIMAL(20, 2),
    source VARCHAR(50) NOT NULL,
    data_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for integrity
    signature VARCHAR(128), -- Cryptographic signature
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT price_positive CHECK (price > 0),
    CONSTRAINT volume_positive CHECK (volume >= 0)
);

-- Create hypertable for time-series optimization
SELECT create_hypertable('market_data.price_events', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- Create indexes for efficient querying
CREATE INDEX idx_price_events_symbol_time ON market_data.price_events (symbol, timestamp DESC);
CREATE INDEX idx_price_events_exchange_time ON market_data.price_events (exchange, timestamp DESC);
CREATE INDEX idx_price_events_hash ON market_data.price_events (data_hash);

-- Social sentiment data
CREATE TABLE market_data.social_sentiment (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    platform VARCHAR(50) NOT NULL, -- reddit, twitter, etc.
    sentiment_score DECIMAL(5, 4) NOT NULL, -- -1.0 to 1.0
    confidence DECIMAL(5, 4) NOT NULL, -- 0.0 to 1.0
    post_count INTEGER NOT NULL,
    engagement_score DECIMAL(10, 4),
    data_hash VARCHAR(64) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT sentiment_range CHECK (sentiment_score >= -1.0 AND sentiment_score <= 1.0),
    CONSTRAINT confidence_range CHECK (confidence >= 0.0 AND confidence <= 1.0)
);

SELECT create_hypertable('market_data.social_sentiment', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- On-chain metrics from thirdweb Insight
CREATE TABLE market_data.onchain_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    chain VARCHAR(50) NOT NULL,
    whale_movements DECIMAL(20, 8),
    large_transactions INTEGER,
    active_addresses INTEGER,
    transaction_volume DECIMAL(20, 8),
    gas_usage BIGINT,
    data_hash VARCHAR(64) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

SELECT create_hypertable('market_data.onchain_metrics', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- Risk Analysis Tables
CREATE TABLE risk_analysis.mutual_information_scores (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    symbol_pair VARCHAR(50) NOT NULL, -- e.g., "BTC-ETH"
    mi_score DECIMAL(10, 8) NOT NULL,
    threshold_exceeded BOOLEAN NOT NULL,
    window_size INTEGER NOT NULL, -- in minutes
    calculation_method VARCHAR(50) NOT NULL,
    data_hash VARCHAR(64) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT mi_score_positive CHECK (mi_score >= 0)
);

SELECT create_hypertable('risk_analysis.mutual_information_scores', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

CREATE TABLE risk_analysis.quantum_predictions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    prediction_horizon INTEGER NOT NULL, -- in days
    volatility_prediction DECIMAL(10, 8) NOT NULL,
    quantum_tunneling_prob DECIMAL(5, 4) NOT NULL,
    confidence_score DECIMAL(5, 4) NOT NULL,
    model_version VARCHAR(20) NOT NULL,
    data_hash VARCHAR(64) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT volatility_positive CHECK (volatility_prediction >= 0),
    CONSTRAINT quantum_prob_range CHECK (quantum_tunneling_prob >= 0.0 AND quantum_tunneling_prob <= 1.0)
);

SELECT create_hypertable('risk_analysis.quantum_predictions', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- Audit and Security Tables
CREATE TABLE audit_logs.data_integrity_checks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    check_type VARCHAR(50) NOT NULL, -- hash_validation, signature_verification, etc.
    status VARCHAR(20) NOT NULL, -- PASS, FAIL, WARNING
    details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

SELECT create_hypertable('audit_logs.data_integrity_checks', 'timestamp', chunk_time_interval => INTERVAL '1 day');

CREATE TABLE audit_logs.api_access_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL,
    user_id VARCHAR(100),
    endpoint VARCHAR(200) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER,
    ip_address INET,
    user_agent TEXT,
    request_hash VARCHAR(64),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

SELECT create_hypertable('audit_logs.api_access_logs', 'timestamp', chunk_time_interval => INTERVAL '1 day');

-- Quantum Models Configuration
CREATE TABLE quantum_models.model_parameters (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    version VARCHAR(20) NOT NULL,
    parameters JSONB NOT NULL,
    performance_metrics JSONB,
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(model_name, version)
);

-- Continuous Aggregates for Real-time Analytics
CREATE MATERIALIZED VIEW market_data.hourly_price_summary
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', timestamp) AS hour,
    symbol,
    exchange,
    FIRST(price, timestamp) AS open_price,
    MAX(price) AS high_price,
    MIN(price) AS low_price,
    LAST(price, timestamp) AS close_price,
    SUM(volume) AS total_volume,
    COUNT(*) AS tick_count
FROM market_data.price_events
GROUP BY hour, symbol, exchange;

-- Refresh policy for continuous aggregates
SELECT add_continuous_aggregate_policy('market_data.hourly_price_summary',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

-- Data retention policies for cost optimization
SELECT add_retention_policy('market_data.price_events', INTERVAL '1 year');
SELECT add_retention_policy('market_data.social_sentiment', INTERVAL '6 months');
SELECT add_retention_policy('audit_logs.api_access_logs', INTERVAL '3 months');

-- Compression policies for historical data
SELECT add_compression_policy('market_data.price_events', INTERVAL '7 days');
SELECT add_compression_policy('market_data.social_sentiment', INTERVAL '7 days');

-- Create roles for security
CREATE ROLE quantum_readonly;
CREATE ROLE quantum_readwrite;
CREATE ROLE quantum_admin;

-- Grant permissions
GRANT USAGE ON SCHEMA market_data TO quantum_readonly, quantum_readwrite;
GRANT USAGE ON SCHEMA risk_analysis TO quantum_readonly, quantum_readwrite;
GRANT USAGE ON SCHEMA audit_logs TO quantum_admin;
GRANT USAGE ON SCHEMA quantum_models TO quantum_readwrite, quantum_admin;

GRANT SELECT ON ALL TABLES IN SCHEMA market_data TO quantum_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA risk_analysis TO quantum_readonly;

GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA market_data TO quantum_readwrite;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA risk_analysis TO quantum_readwrite;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA quantum_models TO quantum_readwrite;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA audit_logs TO quantum_admin;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA quantum_models TO quantum_admin;

-- Function for data integrity validation
CREATE OR REPLACE FUNCTION validate_data_hash(
    data_content TEXT,
    expected_hash VARCHAR(64)
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN encode(digest(data_content, 'sha256'), 'hex') = expected_hash;
END;
$$ LANGUAGE plpgsql;

-- Function for cryptographic signature verification
CREATE OR REPLACE FUNCTION verify_signature(
    data_content TEXT,
    signature VARCHAR(128),
    public_key TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    -- Placeholder for actual signature verification
    -- In production, implement proper cryptographic verification
    RETURN LENGTH(signature) = 128 AND LENGTH(public_key) > 0;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic hash generation
CREATE OR REPLACE FUNCTION generate_data_hash() RETURNS TRIGGER AS $$
BEGIN
    NEW.data_hash = encode(digest(
        CONCAT(NEW.timestamp, NEW.symbol, NEW.price, NEW.volume), 
        'sha256'
    ), 'hex');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply hash generation trigger to price events
CREATE TRIGGER price_events_hash_trigger
    BEFORE INSERT ON market_data.price_events
    FOR EACH ROW EXECUTE FUNCTION generate_data_hash();

-- Initial model parameters for quantum risk engine
INSERT INTO quantum_models.model_parameters (model_name, version, parameters, is_active) VALUES
('mutual_information_network', '1.0', '{"threshold": 0.25, "denoising": "wavelet", "window_size": 30}', true),
('quantum_tunneling_model', '1.0', '{"volatility_window": 30, "probability_threshold": 0.65}', true),
('entropy_weighting', '1.0', '{"weights": {"mi_score": 0.4, "quantum_prob": 0.6}}', true);

-- Create indexes for performance optimization
CREATE INDEX idx_mi_scores_symbol_time ON risk_analysis.mutual_information_scores (symbol_pair, timestamp DESC);
CREATE INDEX idx_quantum_pred_symbol_time ON risk_analysis.quantum_predictions (symbol, timestamp DESC);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs.data_integrity_checks (timestamp DESC);

COMMIT;
