# PostgreSQL Client Authentication Configuration File
# For Quantum Market Intelligence Hub
#
# This file controls: which hosts are allowed to connect, how clients
# are authenticated, which PostgreSQL user names they can use, which
# databases they can access.

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     trust

# IPv4 local connections:
host    all             all             127.0.0.1/32            scram-sha-256

# IPv6 local connections:
host    all             all             ::1/128                 scram-sha-256

# Allow connections from Docker containers (Docker default bridge network)
host    all             all             **********/12           trust

# Allow connections from Docker Compose networks
host    all             all             ***********/16          trust

# Allow connections from any Docker network (for development)
host    all             all             10.0.0.0/8              trust

# Replication connections
host    replication     all             127.0.0.1/32            scram-sha-256
host    replication     all             ::1/128                 scram-sha-256

# Allow replication connections from Docker networks
host    replication     all             **********/12           scram-sha-256
host    replication     all             ***********/16          scram-sha-256
host    replication     all             10.0.0.0/8              scram-sha-256
