# PostgreSQL Configuration for Quantum Market Intelligence
# Optimized for financial-grade time-series data processing

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings (optimized for time-series workloads)
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 16MB
maintenance_work_mem = 128MB
dynamic_shared_memory_type = posix

# WAL Settings (for high-throughput financial data)
wal_level = replica
wal_buffers = 16MB
checkpoint_completion_target = 0.9
max_wal_size = 2GB
min_wal_size = 512MB
checkpoint_timeout = 15min

# Query Planner Settings
random_page_cost = 1.1
effective_io_concurrency = 200
default_statistics_target = 100

# Logging Settings (for audit compliance)
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000  # Log slow queries
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_statement = 'ddl'

# TimescaleDB Specific Settings
timescaledb.max_background_workers = 8
timescaledb.last_updated_threshold = '1min'

# Security Settings
ssl = off  # Disabled for development, enable in production
password_encryption = scram-sha-256

# Locale Settings
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# Time Zone (critical for financial data)
timezone = 'UTC'
log_timezone = 'UTC'

# Performance Tuning for Financial Data
# Optimize for INSERT-heavy workloads with time-series data
synchronous_commit = off  # For better insert performance (acceptable for market data)
commit_delay = 10000  # Microseconds
commit_siblings = 5

# Parallel Query Settings
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2

# Background Writer Settings
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Autovacuum Settings (important for time-series data)
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1

# Statement Timeout (prevent runaway queries)
statement_timeout = 300000  # 5 minutes

# Lock Timeout
lock_timeout = 30000  # 30 seconds

# Deadlock Timeout
deadlock_timeout = 1s

# Archive Settings (for backup and recovery)
archive_mode = off  # Enable in production
archive_command = ''

# Replication Settings
max_wal_senders = 3
wal_keep_size = 1GB

# Error Reporting and Monitoring
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
stats_temp_directory = 'pg_stat_tmp'

# Shared Preload Libraries
shared_preload_libraries = 'timescaledb'

# Custom Settings for Financial Data Processing
# These can be adjusted based on workload characteristics
temp_buffers = 32MB
max_prepared_transactions = 0
max_files_per_process = 1000
max_locks_per_transaction = 64

# JIT Settings (PostgreSQL 11+)
jit = on
jit_above_cost = 100000
jit_inline_above_cost = 500000
jit_optimize_above_cost = 500000
