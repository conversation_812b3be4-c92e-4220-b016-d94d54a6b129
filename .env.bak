# Database Configuration
POSTGRES_PASSWORD=6UD4nu33sGV3YGUfXRxNrq6mr
DATABASE_URL=postgresql://quantum_user:6UD4nu33sGV3YGUfXRxNrq6mr@localhost:5433/quantum_market_db

# Redis Configuration
REDIS_PASSWORD=quantum_redis_pass_2024
REDIS_URL=redis://:quantum_redis_pass_2024@localhost:6381/0

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
SCHEMA_REGISTRY_URL=http://localhost:8081

# API Keys
THIRDWEB_CLIENT_ID=your_thirdweb_client_id_here
THIRDWEB_SECRET_KEY=your_thirdweb_secret_key_here
NEBULA_API_KEY=your_nebula_api_key_here

# External APIs
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Environment
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=true

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Financial Data Sources
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
QUANDL_API_KEY=your_quandl_api_key_here

# Blockchain Networks
ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/your_alchemy_key
POLYGON_RPC_URL=https://polygon-mainnet.g.alchemy.com/v2/your_alchemy_key
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# CrossRef API for Citation Verification
CROSSREF_API_URL=https://api.crossref.org
CROSSREF_MAILTO=<EMAIL>

# OpenTelemetry Configuration
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_SERVICE_NAME=quantum-market-intelligence
OTEL_RESOURCE_ATTRIBUTES=service.name=quantum-market-intelligence,service.version=1.0.0
