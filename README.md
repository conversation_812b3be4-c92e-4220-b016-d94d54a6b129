# Quantum Market Intelligence Hub

**Financial-grade real-time analytics with quantum finance models**

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/quantum-market-intelligence/quantum-hub)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![TimescaleDB](https://img.shields.io/badge/TimescaleDB-2.13+-orange.svg)](https://timescale.com)

## Overview

The Quantum Market Intelligence Hub is a production-grade financial analytics platform that implements quantum finance models for real-time market analysis. Built with zero-trust validation, cryptographic security, and 99.9th percentile quality standards.

### Key Features

- **Quantum Finance Models**: Mutual information networks and quantum tunneling predictions
- **Real-time Data Processing**: Kafka-based streaming with financial-grade reliability
- **Zero-Trust Validation**: Cryptographic signatures and data integrity verification
- **TimescaleDB Integration**: Optimized time-series database for financial data
- **Comprehensive Monitoring**: OpenTelemetry, Prometheus, and Grafana observability
- **Blockchain Integration**: Thirdweb SDK with Nebula AI for on-chain analytics

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │  Kafka Streams  │    │  Risk Engine    │
│                 │───▶│                 │───▶│                 │
│ • Binance       │    │ • Schema Reg    │    │ • Quantum Models│
│ • CoinGecko     │    │ • Avro Schemas  │    │ • MI Networks   │
│ • Social APIs   │    │ • Zero-Trust    │    │ • Backtesting   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI       │    │  TimescaleDB    │
│                 │◀───│                 │◀───│                 │
│ • Next.js       │    │ • Async/Await   │    │ • Hypertables   │
│ • Real-time UI  │    │ • Pydantic v2   │    │ • Compression   │
│ • Thirdweb SDK  │    │ • OpenTelemetry │    │ • Retention     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Docker & Docker Compose
- Python 3.11+
- Node.js 18+ (for frontend)
- 8GB+ RAM recommended

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/quantum-market-intelligence/quantum-hub.git
   cd quantum-hub
   ```

2. **Set up the foundation infrastructure**
   ```bash
   ./scripts/setup.sh
   ```

3. **Run comprehensive tests**
   ```bash
   ./scripts/test_phase0.sh
   ```

4. **Generate SBOM for compliance**
   ```bash
   ./scripts/generate_sbom.sh
   ```

### Services

After setup, the following services will be available:

| Service | URL | Description |
|---------|-----|-------------|
| Backend API | http://localhost:8000 | FastAPI application |
| API Docs | http://localhost:8000/docs | Interactive API documentation |
| Health Check | http://localhost:8000/health | System health monitoring |
| TimescaleDB | localhost:5433 | Time-series database |
| Kafka | localhost:9092 | Message streaming |
| Schema Registry | http://localhost:8081 | Avro schema management |
| Redis | localhost:6381 | Caching and sessions |
| Prometheus | http://localhost:9090 | Metrics collection |
| Grafana | http://localhost:3001 | Visualization dashboard |

## Development Phases

### ✅ Phase 0: Foundation (Completed)
- Docker infrastructure with TimescaleDB, Kafka, Redis
- Cryptographic validation framework
- Zero-trust Kafka testing
- OpenTelemetry observability
- SBOM generation with Sigstore

### 🚧 Phase 1: Data Ingestion (In Progress)
- Financial data APIs integration
- Avro schema validation
- Dead letter queues
- Mutual information transforms

### 📋 Phase 2: Quantum Risk Engine
- Mutual information networks
- Quantum tunneling models
- Backtesting validation
- Performance optimization

### 📋 Phase 3: LLM Integration
- Thirdweb Nebula AI integration
- Citation verification
- Confidence thresholds
- Guardrails implementation

### 📋 Phase 4: Frontend Dashboard
- Next.js with TypeScript
- Real-time data visualization
- Thirdweb wallet integration
- Responsive design

### 📋 Phase 5: Monetization
- NFT-based access control
- Universal Bridge payments
- Subscription management
- Revenue analytics

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Database
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL=postgresql://quantum_user:password@localhost:5433/quantum_market_db

# APIs
THIRDWEB_CLIENT_ID=your_thirdweb_client_id
NEBULA_API_KEY=your_nebula_api_key
BINANCE_API_KEY=your_binance_api_key
COINGECKO_API_KEY=your_coingecko_api_key

# Security
JWT_SECRET_KEY=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key
```

### Quantum Model Parameters

```bash
# Mutual Information Network
MUTUAL_INFORMATION_THRESHOLD=0.25

# Quantum Tunneling Model
QUANTUM_TUNNELING_THRESHOLD=0.65
VOLATILITY_WINDOW_DAYS=30
```

## API Documentation

### Health Endpoints

```bash
# Basic health check
curl http://localhost:8000/health

# Detailed health with all components
curl http://localhost:8000/health/detailed

# Database-specific health
curl http://localhost:8000/health/database

# Kafka-specific health
curl http://localhost:8000/health/kafka
```

### Market Data Endpoints (Phase 1)

```bash
# Get real-time price data
curl http://localhost:8000/api/v1/market/prices/BTC

# Get social sentiment
curl http://localhost:8000/api/v1/market/sentiment/BTC

# Get on-chain metrics
curl http://localhost:8000/api/v1/market/onchain/BTC
```

### Risk Analysis Endpoints (Phase 2)

```bash
# Get mutual information scores
curl http://localhost:8000/api/v1/risk/mutual-information/BTC-ETH

# Get quantum predictions
curl http://localhost:8000/api/v1/risk/quantum-tunneling/BTC

# Get combined risk assessment
curl http://localhost:8000/api/v1/risk/assessment/BTC
```

## Testing

### Unit Tests
```bash
cd backend
pytest tests/ -v --cov=app
```

### Integration Tests
```bash
./scripts/test_phase0.sh
```

### Load Testing
```bash
# Install k6
brew install k6  # macOS
# or
sudo apt install k6  # Ubuntu

# Run load tests
k6 run tests/load/api_load_test.js
```

## Monitoring

### Metrics
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001 (admin/quantum_admin_2024)

### Tracing
- **Jaeger**: http://localhost:16686

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f timescaledb
docker-compose logs -f kafka
```

## Security

### Cryptographic Validation
- All data includes SHA-256 hashes for integrity
- RSA-PSS signatures for authenticity
- Zero-trust validation at every layer

### SBOM Generation
```bash
./scripts/generate_sbom.sh
```

### Security Scanning
```bash
# Scan Docker images
docker scout cves quantum-backend:latest

# Scan Python dependencies
safety check -r backend/requirements.txt
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Standards
- 99.9th percentile quality standards
- Comprehensive testing (unit, integration, e2e)
- Financial-grade security validation
- Complete documentation

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: [docs.quantum-market.ai](https://docs.quantum-market.ai)
- **Issues**: [GitHub Issues](https://github.com/quantum-market-intelligence/quantum-hub/issues)
- **Email**: <EMAIL>
- **Discord**: [Quantum Hub Community](https://discord.gg/quantum-hub)

## Acknowledgments

- [TimescaleDB](https://timescale.com) for time-series database optimization
- [Thirdweb](https://thirdweb.com) for blockchain infrastructure
- [FastAPI](https://fastapi.tiangolo.com) for high-performance API framework
- [Confluent](https://confluent.io) for Kafka ecosystem tools

---

**Built with ❤️ for the financial technology community**
