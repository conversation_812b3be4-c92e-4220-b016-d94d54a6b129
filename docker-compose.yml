services:
  # TimescaleDB - Financial-grade time-series database
  timescaledb:
    image: timescale/timescaledb:2.13.1-pg15
    container_name: quantum-timescaledb
    environment:
      POSTGRES_DB: quantum_market_db
      POSTGRES_USER: quantum_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-quantum_secure_pass_2024}
      TIMESCALEDB_TELEMETRY: off
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"  # Avoid conflicts with local PostgreSQL
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./infrastructure/timescaledb/init:/docker-entrypoint-initdb.d
      - ./infrastructure/timescaledb/config/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - quantum-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quantum_user -d quantum_market_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Zookeeper for Kaf<PERSON>
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.1
    container_name: quantum-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - quantum-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Kafka - Financial data streaming
  kafka:
    image: confluentinc/cp-kafka:7.5.1
    container_name: quantum-kafka
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: false
      KAFKA_LOG_RETENTION_HOURS: 168  # 7 days for financial data
      KAFKA_LOG_SEGMENT_BYTES: **********  # 1GB segments
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000  # 5 minutes
    ports:
      - "9092:9092"
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - quantum-network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 10s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # Schema Registry for Avro validation
  schema-registry:
    image: confluentinc/cp-schema-registry:7.5.1
    container_name: quantum-schema-registry
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      SCHEMA_REGISTRY_HOST_NAME: schema-registry
      SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS: kafka:29092
      SCHEMA_REGISTRY_LISTENERS: http://0.0.0.0:8081
      SCHEMA_REGISTRY_SCHEMA_COMPATIBILITY_LEVEL: BACKWARD
      SCHEMA_REGISTRY_LOG4J_ROOT_LOGLEVEL: INFO
    ports:
      - "8081:8081"
    networks:
      - quantum-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/subjects"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis - Caching and session management
  redis:
    image: redis:7.2.3-alpine
    container_name: quantum-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-quantum_redis_pass_2024}
    ports:
      - "6381:6379"  # Avoid conflicts with local Redis
    volumes:
      - redis_data:/data
    networks:
      - quantum-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: quantum-backend
    depends_on:
      timescaledb:
        condition: service_healthy
      kafka:
        condition: service_healthy
      redis:
        condition: service_healthy
      schema-registry:
        condition: service_healthy
    environment:
      DATABASE_URL: postgresql+asyncpg://quantum_user:${POSTGRES_PASSWORD:-quantum_secure_pass_2024}@timescaledb:5432/quantum_market_db
      REDIS_URL: redis://:${REDIS_PASSWORD:-quantum_redis_pass_2024}@redis:6379/0
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      SCHEMA_REGISTRY_URL: http://schema-registry:8081
      ENVIRONMENT: development
      LOG_LEVEL: INFO
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/venv  # Exclude virtual environment from bind mount
    networks:
      - quantum-network
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: quantum-frontend
    depends_on:
      - backend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_THIRDWEB_CLIENT_ID: ${THIRDWEB_CLIENT_ID}
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules  # Exclude node_modules from bind mount
    networks:
      - quantum-network
    restart: unless-stopped

volumes:
  timescaledb_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  redis_data:
    driver: local

networks:
  quantum-network:
    driver: bridge
    ipam:
      config:
        - subnet: *************/24
