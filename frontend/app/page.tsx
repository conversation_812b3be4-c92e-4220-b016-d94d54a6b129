export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-white mb-8">
            Quantum Nexus
          </h1>
          <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">
            Advanced cryptocurrency market intelligence and pattern detection platform
            powered by quantum finance models and real-time data analysis.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="bg-white/10 backdrop-blur-lg rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Real-time Data</h3>
              <p className="text-gray-300">
                Live market data from multiple exchanges with millisecond precision
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Pattern Detection</h3>
              <p className="text-gray-300">
                Advanced algorithms detect market patterns and anomalies
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-lg rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Quantum Models</h3>
              <p className="text-gray-300">
                Cutting-edge quantum finance models for predictive analysis
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
