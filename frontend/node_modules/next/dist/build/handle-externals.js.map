{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["isResourceInPackages", "resolveExternal", "makeExternalHandler", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "path", "sep", "includes", "join", "replace", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "NODE_ESM_RESOLVE_OPTIONS", "nodeResolveOptions", "NODE_RESOLVE_OPTIONS", "baseEsmResolveOptions", "NODE_BASE_ESM_RESOLVE_OPTIONS", "baseResolveOptions", "NODE_BASE_RESOLVE_OPTIONS", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "config", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "isWebpackAppLayer", "test", "notExternalModules", "BARREL_OPTIMIZATION_PREFIX", "resolveNextExternal", "isExternal", "isWebpackServerLayer", "WEBPACK_LAYERS", "serverSideRendering", "isRelative", "fullRequest", "resolveResult", "undefined", "defaultOverrides", "Error", "externalType", "transpilePackages", "Map", "pkg", "pkgRes", "set", "dirname", "shouldBeBundled", "bundlePagesExternals"], "mappings": ";;;;;;;;;;;;;;;;IA0BgBA,oBAAoB;eAApBA;;IAgBMC,eAAe;eAAfA;;IAwFNC,mBAAmB;eAAnBA;;;2BAlIe;6BAEE;4BACU;6DAC1B;+BAMV;wBACiD;;;;;;AAExD,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,MAAMI,mBAAmB;AAElB,SAASV,qBACdW,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,OAAOD,gCAAAA,aAAcE,IAAI,CAAC,CAACC,IACzBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMI,aAAI,CAACC,GAAG,IACxDT,SAASU,QAAQ,CACfF,aAAI,CAACC,GAAG,GACND,aAAI,CAACG,IAAI,CAAC,gBAAgBP,EAAEQ,OAAO,CAAC,OAAOJ,aAAI,CAACC,GAAG,KACnDD,aAAI,CAACC,GAAG;AAGpB;AAEO,eAAenB,gBACpBuB,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBC,uCAAwB,EACjDC,qBAA0BC,mCAAoB,EAC9CC,wBAA6BC,4CAA6B,EAC1DC,qBAA0BC,wCAAyB;IAEnD,MAAMC,eAAe,CAAC,CAACf;IACvB,MAAMgB,oBAAoBhB,uBAAuB;IAEjD,IAAIiB,MAAqB;IACzB,IAAIC,QAAiB;IAErB,IAAIC,mBACFJ,gBAAgBZ,iBAAiB;QAAC;QAAM;KAAM,GAAG;QAAC;KAAM;IAE1D,KAAK,MAAMiB,aAAaD,iBAAkB;QACxC,MAAME,UAAUjB,WACdgB,YAAYb,oBAAoBE;QAGlC,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACQ,KAAKC,MAAM,GAAG,MAAMG,QAAQpB,SAASC;QACzC,EAAE,OAAOoB,KAAK;YACZL,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACd,kBAAkBe,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIX,iBAAiB;YACnB,OAAO;gBAAEkB,UAAUlB,gBAAgBY;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIX,kBAAkB;YACpB,IAAIkB;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAActB,WAClBc,QAAQP,wBAAwBE;gBAEjC,CAACW,SAASC,UAAU,GAAG,MAAMC,YAAY3B,KAAKG;YACjD,EAAE,OAAOoB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYP,OAAOC,UAAUO,WAAW;gBAC1CR,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEO,SAASzC,oBAAoB,EAClCkD,MAAM,EACNC,0BAA0B,EAC1B7B,GAAG,EAKJ;QAE2B4B;IAD1B,IAAIE;IACJ,MAAMb,oBAAoBW,EAAAA,uBAAAA,OAAOG,YAAY,qBAAnBH,qBAAqBZ,YAAY,MAAK;IAEhE,OAAO,eAAegB,gBACpB9B,OAAe,EACfC,OAAe,EACf8B,cAAsB,EACtBC,KAA8B,EAC9B7B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM8B,UACJhC,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBE,aAAI,CAACyC,KAAK,CAACC,UAAU,CAAClC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBmC,QAAQC,QAAQ,KAAK,WAAW5C,aAAI,CAAC6C,KAAK,CAACH,UAAU,CAAClC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMsC,aAAaC,IAAAA,yBAAiB,EAACR;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaQ,IAAI,CAACxC,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIxB,mBAAmBgE,IAAI,CAACxC,YAAY,CAACsC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAEtC,QAAQ,CAAC;YAC9B;YAEA,MAAMyC,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAACxC,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQN,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIM,QAAQV,UAAU,CAACoD,sCAA0B,GAAG;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAMzC,iBAAiB6B,mBAAmB;QAE1C;;;;;;KAMC,GACD,MAAMa,sBAAsB,CAACtB;YAC3B,MAAMuB,aAAa/D,gBAAgB2D,IAAI,CAACnB;YAExC,sFAAsF;YACtF,sGAAsG;YACtG,IAAIuB,YAAY;gBACd,oGAAoG;gBACpG,oCAAoC;gBACpC,OAAO,CAAC,SAAS,EAAEvB,SAASzB,OAAO,CAAC,oBAAoB,aAAa,CAAC;YACxE;QACF;QAEA,4DAA4D;QAC5D,yFAAyF;QACzF,IACEiD,IAAAA,4BAAoB,EAACd,UACrB/B,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDkD,IAAI,CAACxC,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8CwC,IAAI,CAACxC,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8DwC,IAAI,CAChExC,YAEF,4CAA4CwC,IAAI,CAACxC,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsEwC,IAAI,CACxExC,YAEF,2CAA2CwC,IAAI,CAACxC,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAO2C,oBAAoB3C;QAC7B;QAEA,gFAAgF;QAChF,qEAAqE;QACrE,oDAAoD;QACpD,IAAI+B,UAAUe,yBAAc,CAACC,mBAAmB,EAAE;YAChD,MAAMC,aAAahD,QAAQV,UAAU,CAAC;YACtC,MAAM2D,cAAcD,aAChBxD,aAAI,CAACG,IAAI,CAACI,SAASC,SAASJ,OAAO,CAAC,OAAO,OAC3CI;YACJ,OAAO2C,oBAAoBM;QAC7B;QAEA,6FAA6F;QAC7F,MAAMC,gBAAgB,MAAM5E,gBAC1BuB,KACA4B,OAAOG,YAAY,CAACf,YAAY,EAChCd,SACAC,SACAC,gBACAC,YACA8B,UAAUW,sBAAsBQ;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAc7B,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAIrB,YAAY,oBAAoB;YAClCkD,cAAcnC,GAAG,GAAGqC,6BAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAErC,GAAG,EAAEC,KAAK,EAAE,GAAGkC;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAACnC,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACd,kBAAkBe,SAAS,CAACF,mBAAmB;YAClD,MAAM,IAAIuC,MACR,CAAC,cAAc,EAAErD,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAMsD,eAAetC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2CwB,IAAI,CAACzB,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2ByB,IAAI,CAACzB,QAChC,8BAA8ByB,IAAI,CAACzB,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIU,OAAO8B,iBAAiB,IAAI,CAAC5B,6BAA6B;YAC5DA,8BAA8B,IAAI6B;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAOhC,OAAO8B,iBAAiB,CAAE;gBAC1C,MAAMG,SAAS,MAAMpF,gBACnBuB,KACA4B,OAAOG,YAAY,CAACf,YAAY,EAChCd,SACA0D,MAAM,iBACNxD,gBACAC,YACA8B,UAAUW,sBAAsBQ;gBAElC,IAAIO,OAAO3C,GAAG,EAAE;oBACdY,4BAA4BgC,GAAG,CAACF,KAAKjE,aAAI,CAACoE,OAAO,CAACF,OAAO3C,GAAG;gBAC9D;YACF;QACF;QAEA,MAAM8C,kBACJxF,qBACE0C,KACAU,OAAO8B,iBAAiB,EACxB5B,gCAEDX,SAASsB,cACT,CAACA,cAAcb,OAAOG,YAAY,CAACkC,oBAAoB;QAE1D,IAAI/E,iBAAiByD,IAAI,CAACzB,MAAM;YAC9B,IAAI8B,IAAAA,4BAAoB,EAACd,QAAQ;gBAC/B,IAAI,CAACL,2BAA2Bc,IAAI,CAACzB,MAAM;oBACzC,QAAO,0BAA0B;gBACnC;gBACA,OAAO,CAAC,EAAEuC,aAAa,CAAC,EAAEtD,QAAQ,CAAC,CAAC,2BAA2B;;YACjE;YAEA,IAAI,CAAC6D,mBAAmBnC,2BAA2Bc,IAAI,CAACzB,MAAM;gBAC5D,OAAO,CAAC,EAAEuC,aAAa,CAAC,EAAEtD,QAAQ,CAAC,CAAC,0CAA0C;;YAChF;QACF;IAEA,2CAA2C;IAC7C;AACF"}