import type { NextConfigComplete } from '../server/config-shared';
import type { webpack } from 'next/dist/compiled/webpack/webpack';
import type { MiddlewareConfig, PageStaticInfo } from './analysis/get-page-static-info';
import type { LoadedEnvFiles } from '@next/env';
import type { AppLoaderOptions } from './webpack/loaders/next-app-loader';
import type { CompilerNameValues } from '../shared/lib/constants';
import type { __ApiPreviewProps } from '../server/api-utils';
import type { ServerRuntime } from '../../types';
export declare function sortByPageExts(pageExtensions: string[]): (a: string, b: string) => number;
export declare function getStaticInfoIncludingLayouts({ isInsideAppDir, pageExtensions, pageFilePath, appDir, config, isDev, page, }: {
    isInsideAppDir: boolean;
    pageExtensions: string[];
    pageFilePath: string;
    appDir: string | undefined;
    config: NextConfigComplete;
    isDev: boolean | undefined;
    page: string;
}): Promise<PageStaticInfo>;
type ObjectValue<T> = T extends {
    [key: string]: infer V;
} ? V : never;
/**
 * For a given page path removes the provided extensions.
 */
export declare function getPageFromPath(pagePath: string, pageExtensions: string[]): string;
export declare function getPageFilePath({ absolutePagePath, pagesDir, appDir, rootDir, }: {
    absolutePagePath: string;
    pagesDir: string | undefined;
    appDir: string | undefined;
    rootDir: string;
}): string;
export declare function createPagesMapping({ isDev, pageExtensions, pagePaths, pagesType, pagesDir, }: {
    isDev: boolean;
    pageExtensions: string[];
    pagePaths: string[];
    pagesType: 'pages' | 'root' | 'app';
    pagesDir: string | undefined;
}): {
    [page: string]: string;
};
export interface CreateEntrypointsParams {
    buildId: string;
    config: NextConfigComplete;
    envFiles: LoadedEnvFiles;
    isDev?: boolean;
    pages: {
        [page: string]: string;
    };
    pagesDir?: string;
    previewMode: __ApiPreviewProps;
    rootDir: string;
    rootPaths?: Record<string, string>;
    appDir?: string;
    appPaths?: Record<string, string>;
    pageExtensions: string[];
    hasInstrumentationHook?: boolean;
}
export declare function getEdgeServerEntry(opts: {
    rootDir: string;
    absolutePagePath: string;
    buildId: string;
    bundlePath: string;
    config: NextConfigComplete;
    isDev: boolean;
    isServerComponent: boolean;
    page: string;
    pages: {
        [page: string]: string;
    };
    middleware?: Partial<MiddlewareConfig>;
    pagesType: 'app' | 'pages' | 'root';
    appDirLoader?: string;
    hasInstrumentationHook?: boolean;
    preferredRegion: string | string[] | undefined;
    middlewareConfig?: MiddlewareConfig;
}): string | {
    import: string;
    layer: "rsc";
    filename?: undefined;
} | {
    import: string;
    filename: string;
    layer?: undefined;
} | {
    import: string;
    layer: "ssr" | undefined;
    filename?: undefined;
};
export declare function getAppEntry(opts: Readonly<AppLoaderOptions>): {
    import: string;
    layer: "rsc";
};
export declare function getClientEntry(opts: {
    absolutePagePath: string;
    page: string;
}): string | string[];
export declare function runDependingOnPageType<T>(params: {
    onClient: () => T;
    onEdgeServer: () => T;
    onServer: () => T;
    page: string;
    pageRuntime: ServerRuntime;
    pageType?: 'app' | 'pages' | 'root';
}): void;
export declare function createEntrypoints(params: CreateEntrypointsParams): Promise<{
    client: webpack.EntryObject;
    server: webpack.EntryObject;
    edgeServer: webpack.EntryObject;
    middlewareMatchers: undefined;
}>;
export declare function finalizeEntrypoint({ name, compilerType, value, isServerComponent, hasAppDir, }: {
    compilerType?: CompilerNameValues;
    name: string;
    value: ObjectValue<webpack.EntryObject>;
    isServerComponent?: boolean;
    hasAppDir?: boolean;
}): ObjectValue<webpack.EntryObject>;
export {};
