{"version": 3, "sources": ["../../../src/server/async-storage/static-generation-async-storage-wrapper.ts"], "names": ["StaticGenerationAsyncStorageWrapper", "wrap", "storage", "urlPathname", "renderOpts", "postpone", "callback", "isStaticGeneration", "supportsDynamicHTML", "isDraftMode", "isServerAction", "store", "pagePath", "originalPathname", "incrementalCache", "globalThis", "__incrementalCache", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "experimental", "run"], "mappings": "AAkCA,OAAO,MAAMA,sCAGT;IACFC,MACEC,OAAiD,EACjD,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAA2B,EAC9DC,QAAkD;QAElD;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMC,qBACJ,CAACH,WAAWI,mBAAmB,IAC/B,CAACJ,WAAWK,WAAW,IACvB,CAACL,WAAWM,cAAc;QAE5B,MAAMC,QAA+B;YACnCJ;YACAJ;YACAS,UAAUR,WAAWS,gBAAgB;YACrCC,kBACE,qEAAqE;YACrE,mDAAmD;YACnDV,WAAWU,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;YACvEC,cAAcb,WAAWa,YAAY;YACrCC,gBAAgBd,WAAWe,UAAU;YACrCC,YAAYhB,WAAWgB,UAAU;YACjCC,sBAAsBjB,WAAWiB,oBAAoB;YAErDZ,aAAaL,WAAWK,WAAW;YACnCa,cAAclB,WAAWkB,YAAY;YACrCjB;QACF;QAEA,sFAAsF;QACtFD,WAAWO,KAAK,GAAGA;QAEnB,OAAOT,QAAQqB,GAAG,CAACZ,OAAOL,UAAUK;IACtC;AACF,EAAC"}