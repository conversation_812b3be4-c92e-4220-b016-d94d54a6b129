{"version": 3, "sources": ["../../../src/server/lib/router-server.ts"], "names": ["url", "path", "loadConfig", "serveStatic", "setupDebug", "Telemetry", "DecodeError", "findPagesDir", "setupFsCheck", "proxyRequest", "isAbortError", "pipeToNodeResponse", "getResolveRoutes", "getRequestMeta", "pathHasPrefix", "removePathPrefix", "setupCompression", "NoFallbackError", "signalFromNodeResponse", "isPostpone", "PHASE_PRODUCTION_SERVER", "PHASE_DEVELOPMENT_SERVER", "PERMANENT_REDIRECT_STATUS", "DevBundlerService", "debug", "requestHandlers", "initialize", "opts", "process", "env", "NODE_ENV", "dev", "config", "dir", "silent", "compress", "fs<PERSON><PERSON><PERSON>", "minimalMode", "renderServer", "developmentBundler", "devBundlerService", "telemetry", "distDir", "join", "pagesDir", "appDir", "setupDevBundler", "require", "nextConfig", "isCustomServer", "customServer", "turbo", "TURBOPACK", "port", "req", "res", "instance", "renderServerOpts", "hostname", "server", "isNodeDebugging", "serverFields", "experimentalTestProxy", "experimentalHttpsServer", "bundlerService", "handlers", "logError", "type", "err", "logErrorWithOriginalStack", "on", "bind", "resolveRoutes", "ensureMiddleware", "requestHandlerImpl", "_err", "invokedOutputs", "Set", "invokeRender", "parsedUrl", "invoke<PERSON><PERSON>", "handleIndex", "additionalInvokeHeaders", "i18n", "basePath", "startsWith", "query", "__next<PERSON><PERSON><PERSON>", "handleLocale", "pathname", "headers", "getMiddlewareMatchers", "length", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "end", "Error", "invokeHeaders", "encodeURIComponent", "JSON", "stringify", "Object", "assign", "initResult", "requestHandler", "handleRequest", "e", "origUrl", "parse", "hotReloaderResult", "hotReloader", "run", "finished", "resHeaders", "bodyStream", "matchedOutput", "isUpgradeReq", "signal", "closed", "key", "keys", "result", "destination", "format", "protocol", "undefined", "cloneBodyStream", "experimental", "proxyTimeout", "fsPath", "itemPath", "appFiles", "has", "pageFiles", "message", "<PERSON><PERSON><PERSON><PERSON>", "method", "root", "itemsRoot", "etag", "generateEtags", "POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC", "validErrorStatus", "invoke<PERSON>tatus", "add", "appNotFound", "hasAppNotFound", "getItem", "console", "error", "Number", "err2", "wrapRequestHandlerWorker", "interceptTestApis", "upgradeHandler", "socket", "head", "includes", "onHMR"], "mappings": "AAAA,oDAAoD;AAIpD,6EAA6E;AAC7E,OAAO,sBAAqB;AAC5B,OAAO,kBAAiB;AAExB,OAAOA,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,OAAOC,gBAAgB,YAAW;AAClC,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,YAAY,QAAQ,+BAA8B;AAC3D,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,mBAAkB;AACnE,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,cAAc,QAAQ,kBAAiB;AAChD,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,OAAOC,sBAAsB,iCAAgC;AAC7D,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,UAAU,QAAQ,6BAA4B;AAEvD,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,yBAAyB,QACpB,6BAA4B;AACnC,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD,MAAMC,QAAQpB,WAAW;AAezB,MAAMqB,kBAAwD,CAAC;AAE/D,OAAO,eAAeC,WAAWC,IAYhC;IACC,IAAI,CAACC,QAAQC,GAAG,CAACC,QAAQ,EAAE;QACzB,0BAA0B;QAC1BF,QAAQC,GAAG,CAACC,QAAQ,GAAGH,KAAKI,GAAG,GAAG,gBAAgB;IACpD;IAEA,MAAMC,SAAS,MAAM9B,WACnByB,KAAKI,GAAG,GAAGV,2BAA2BD,yBACtCO,KAAKM,GAAG,EACR;QAAEC,QAAQ;IAAM;IAGlB,IAAIC;IAEJ,IAAIH,CAAAA,0BAAAA,OAAQG,QAAQ,MAAK,OAAO;QAC9BA,WAAWnB;IACb;IAEA,MAAMoB,YAAY,MAAM5B,aAAa;QACnCuB,KAAKJ,KAAKI,GAAG;QACbE,KAAKN,KAAKM,GAAG;QACbD;QACAK,aAAaV,KAAKU,WAAW;IAC/B;IAEA,MAAMC,eAAyC,CAAC;IAEhD,IAAIC;IAEJ,IAAIC;IAEJ,IAAIb,KAAKI,GAAG,EAAE;QACZ,MAAMU,YAAY,IAAIpC,UAAU;YAC9BqC,SAASzC,KAAK0C,IAAI,CAAChB,KAAKM,GAAG,EAAED,OAAOU,OAAO;QAC7C;QACA,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGtC,aAAaoB,KAAKM,GAAG;QAElD,MAAM,EAAEa,eAAe,EAAE,GACvBC,QAAQ;QAEVR,qBAAqB,MAAMO,gBAAgB;YACzC,6HAA6H;YAC7HR;YACAO;YACAD;YACAH;YACAL;YACAH,KAAKN,KAAKM,GAAG;YACbe,YAAYhB;YACZiB,gBAAgBtB,KAAKuB,YAAY;YACjCC,OAAO,CAAC,CAACvB,QAAQC,GAAG,CAACuB,SAAS;YAC9BC,MAAM1B,KAAK0B,IAAI;QACjB;QAEAb,oBAAoB,IAAIjB,kBACtBgB,oBACA,yEAAyE;QACzE,mBAAmB;QACnB,CAACe,KAAKC;YACJ,OAAO9B,eAAe,CAACE,KAAKM,GAAG,CAAC,CAACqB,KAAKC;QACxC;IAEJ;IAEAjB,aAAakB,QAAQ,GACnBT,QAAQ;IAEV,MAAMU,mBAA8D;QAClEJ,MAAM1B,KAAK0B,IAAI;QACfpB,KAAKN,KAAKM,GAAG;QACbyB,UAAU/B,KAAK+B,QAAQ;QACvBrB,aAAaV,KAAKU,WAAW;QAC7BN,KAAK,CAAC,CAACJ,KAAKI,GAAG;QACf4B,QAAQhC,KAAKgC,MAAM;QACnBC,iBAAiB,CAAC,CAACjC,KAAKiC,eAAe;QACvCC,cAActB,CAAAA,sCAAAA,mBAAoBsB,YAAY,KAAI,CAAC;QACnDC,uBAAuB,CAAC,CAACnC,KAAKmC,qBAAqB;QACnDC,yBAAyB,CAAC,CAACpC,KAAKoC,uBAAuB;QACvDC,gBAAgBxB;IAClB;IAEA,yBAAyB;IACzB,MAAMyB,WAAW,MAAM3B,aAAakB,QAAQ,CAAC9B,UAAU,CAAC+B;IAExD,MAAMS,WAAW,OACfC,MACAC;QAEA,IAAIjD,WAAWiD,MAAM;YACnB,0EAA0E;YAC1E,qDAAqD;YACrD;QACF;QACA,OAAM7B,sCAAAA,mBAAoB8B,yBAAyB,CAACD,KAAKD;IAC3D;IAEAvC,QAAQ0C,EAAE,CAAC,qBAAqBJ,SAASK,IAAI,CAAC,MAAM;IACpD3C,QAAQ0C,EAAE,CAAC,sBAAsBJ,SAASK,IAAI,CAAC,MAAM;IAErD,MAAMC,gBAAgB5D,iBACpBwB,WACAJ,QACAL,MACAW,aAAakB,QAAQ,EACrBC,kBACAlB,sCAAAA,mBAAoBkC,gBAAgB;IAGtC,MAAMC,qBAA2C,OAAOpB,KAAKC;QAC3D,IAAIpB,UAAU;YACZ,uCAAuC;YACvCA,SAASmB,KAAKC,KAAK,KAAO;QAC5B;QACAD,IAAIgB,EAAE,CAAC,SAAS,CAACK;QACf,2BAA2B;QAC7B;QACApB,IAAIe,EAAE,CAAC,SAAS,CAACK;QACf,2BAA2B;QAC7B;QAEA,MAAMC,iBAAiB,IAAIC;QAE3B,eAAeC,aACbC,SAAiC,EACjCC,UAAkB,EAClBC,WAAmB,EACnBC,0BAAkD,CAAC,CAAC;gBAiBlD9C;YAfF,6DAA6D;YAC7D,sCAAsC;YACtC,IACEJ,OAAOmD,IAAI,IACXpE,iBAAiBiE,YAAYhD,OAAOoD,QAAQ,EAAEC,UAAU,CACtD,CAAC,CAAC,EAAEN,UAAUO,KAAK,CAACC,YAAY,CAAC,IAAI,CAAC,GAExC;gBACAP,aAAa5C,UAAUoD,YAAY,CACjCzE,iBAAiBiE,YAAYhD,OAAOoD,QAAQ,GAC5CK,QAAQ;YACZ;YAEA,IACEnC,IAAIoC,OAAO,CAAC,gBAAgB,MAC5BtD,mCAAAA,UAAUuD,qBAAqB,uBAA/BvD,iCAAmCwD,MAAM,KACzC7E,iBAAiBiE,YAAYhD,OAAOoD,QAAQ,MAAM,QAClD;gBACA7B,IAAIsC,SAAS,CAAC,yBAAyBd,UAAUU,QAAQ,IAAI;gBAC7DlC,IAAIuC,UAAU,GAAG;gBACjBvC,IAAIsC,SAAS,CAAC,gBAAgB;gBAC9BtC,IAAIwC,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,IAAI,CAAC9B,UAAU;gBACb,MAAM,IAAI+B,MAAM;YAClB;YAEA,MAAMC,gBAAoC;gBACxC,GAAG3C,IAAIoC,OAAO;gBACd,uBAAuB;gBACvB,iBAAiBV;gBACjB,kBAAkBkB,mBAAmBC,KAAKC,SAAS,CAACrB,UAAUO,KAAK;gBACnE,GAAIJ,2BAA2B,CAAC,CAAC;YACnC;YACAmB,OAAOC,MAAM,CAAChD,IAAIoC,OAAO,EAAEO;YAE3BzE,MAAM,gBAAgB8B,IAAItD,GAAG,EAAEiG;YAE/B,IAAI;oBACuB3D;gBAAzB,MAAMiE,aAAa,OAAMjE,iCAAAA,yBAAAA,aAAckB,QAAQ,qBAAtBlB,uBAAwBZ,UAAU,CACzD+B;gBAEF,IAAI;oBACF,OAAM8C,8BAAAA,WAAYC,cAAc,CAAClD,KAAKC;gBACxC,EAAE,OAAOa,KAAK;oBACZ,IAAIA,eAAenD,iBAAiB;wBAClC,2BAA2B;wBAC3B,MAAMwF,cAAcxB,cAAc;wBAClC;oBACF;oBACA,MAAMb;gBACR;gBACA;YACF,EAAE,OAAOsC,GAAG;gBACV,qEAAqE;gBACrE,mEAAmE;gBACnE,cAAc;gBACd,IAAIhG,aAAagG,IAAI;oBACnB;gBACF;gBACA,MAAMA;YACR;QACF;QAEA,MAAMD,gBAAgB,OAAOxB;YAC3B,IAAIA,cAAc,GAAG;gBACnB,MAAM,IAAIe,MAAM,CAAC,2CAA2C,EAAE1C,IAAItD,GAAG,CAAC,CAAC;YACzE;YAEA,4BAA4B;YAC5B,IAAIuC,oBAAoB;gBACtB,MAAMoE,UAAUrD,IAAItD,GAAG,IAAI;gBAE3B,IAAIgC,OAAOoD,QAAQ,IAAItE,cAAc6F,SAAS3E,OAAOoD,QAAQ,GAAG;oBAC9D9B,IAAItD,GAAG,GAAGe,iBAAiB4F,SAAS3E,OAAOoD,QAAQ;gBACrD;gBACA,MAAML,YAAY/E,IAAI4G,KAAK,CAACtD,IAAItD,GAAG,IAAI;gBAEvC,MAAM6G,oBAAoB,MAAMtE,mBAAmBuE,WAAW,CAACC,GAAG,CAChEzD,KACAC,KACAwB;gBAGF,IAAI8B,kBAAkBG,QAAQ,EAAE;oBAC9B,OAAOH;gBACT;gBACAvD,IAAItD,GAAG,GAAG2G;YACZ;YAEA,MAAM,EACJK,QAAQ,EACRjC,SAAS,EACTe,UAAU,EACVmB,UAAU,EACVC,UAAU,EACVC,aAAa,EACd,GAAG,MAAM3C,cAAc;gBACtBlB;gBACAC;gBACA6D,cAAc;gBACdC,QAAQnG,uBAAuBqC;gBAC/BqB;YACF;YAEA,IAAIrB,IAAI+D,MAAM,IAAI/D,IAAIyD,QAAQ,EAAE;gBAC9B;YACF;YAEA,IAAIzE,sBAAsB4E,CAAAA,iCAAAA,cAAehD,IAAI,MAAK,oBAAoB;gBACpE,MAAMwC,UAAUrD,IAAItD,GAAG,IAAI;gBAE3B,IAAIgC,OAAOoD,QAAQ,IAAItE,cAAc6F,SAAS3E,OAAOoD,QAAQ,GAAG;oBAC9D9B,IAAItD,GAAG,GAAGe,iBAAiB4F,SAAS3E,OAAOoD,QAAQ;gBACrD;gBAEA,IAAI6B,YAAY;oBACd,KAAK,MAAMM,OAAOlB,OAAOmB,IAAI,CAACP,YAAa;wBACzC1D,IAAIsC,SAAS,CAAC0B,KAAKN,UAAU,CAACM,IAAI;oBACpC;gBACF;gBACA,MAAME,SAAS,MAAMlF,mBAAmBiE,cAAc,CAAClD,KAAKC;gBAE5D,IAAIkE,OAAOT,QAAQ,EAAE;oBACnB;gBACF;gBACA,sEAAsE;gBACtE1D,IAAItD,GAAG,GAAG2G;YACZ;YAEAnF,MAAM,mBAAmB8B,IAAItD,GAAG,EAAE;gBAChCmH;gBACArB;gBACAmB;gBACAC,YAAY,CAAC,CAACA;gBACdnC,WAAW;oBACTU,UAAUV,UAAUU,QAAQ;oBAC5BH,OAAOP,UAAUO,KAAK;gBACxB;gBACA0B;YACF;YAEA,0CAA0C;YAC1C,KAAK,MAAMO,OAAOlB,OAAOmB,IAAI,CAACP,cAAc,CAAC,GAAI;gBAC/C1D,IAAIsC,SAAS,CAAC0B,KAAKN,UAAU,CAACM,IAAI;YACpC;YAEA,kBAAkB;YAClB,IAAI,CAACL,cAAcpB,cAAcA,aAAa,OAAOA,aAAa,KAAK;gBACrE,MAAM4B,cAAc1H,IAAI2H,MAAM,CAAC5C;gBAC/BxB,IAAIuC,UAAU,GAAGA;gBACjBvC,IAAIsC,SAAS,CAAC,YAAY6B;gBAE1B,IAAI5B,eAAexE,2BAA2B;oBAC5CiC,IAAIsC,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE6B,YAAY,CAAC;gBACjD;gBACA,OAAOnE,IAAIwC,GAAG,CAAC2B;YACjB;YAEA,kCAAkC;YAClC,IAAIR,YAAY;gBACd3D,IAAIuC,UAAU,GAAGA,cAAc;gBAC/B,OAAO,MAAMnF,mBAAmBuG,YAAY3D;YAC9C;YAEA,IAAIyD,YAAYjC,UAAU6C,QAAQ,EAAE;oBAMhC/G;gBALF,OAAO,MAAMJ,aACX6C,KACAC,KACAwB,WACA8C,YACAhH,kBAAAA,eAAeyC,KAAK,oCAApBzC,gBAAqCiH,eAAe,IACpD9F,OAAO+F,YAAY,CAACC,YAAY;YAEpC;YAEA,IAAIb,CAAAA,iCAAAA,cAAec,MAAM,KAAId,cAAce,QAAQ,EAAE;gBACnD,IACEvG,KAAKI,GAAG,IACPK,CAAAA,UAAU+F,QAAQ,CAACC,GAAG,CAACjB,cAAce,QAAQ,KAC5C9F,UAAUiG,SAAS,CAACD,GAAG,CAACjB,cAAce,QAAQ,CAAA,GAChD;oBACA3E,IAAIuC,UAAU,GAAG;oBACjB,MAAMhB,aAAaC,WAAW,WAAWE,aAAa;wBACpD,mBAAmB;wBACnB,kBAAkBkB,KAAKC,SAAS,CAAC;4BAC/BkC,SAAS,CAAC,2DAA2D,EAAEnB,cAAce,QAAQ,CAAC,8DAA8D,CAAC;wBAC/J;oBACF;oBACA;gBACF;gBAEA,IACE,CAAC3E,IAAIgF,SAAS,CAAC,oBACfpB,cAAchD,IAAI,KAAK,oBACvB;oBACA,IAAIxC,KAAKI,GAAG,EAAE;wBACZwB,IAAIsC,SAAS,CAAC,iBAAiB;oBACjC,OAAO;wBACLtC,IAAIsC,SAAS,CACX,iBACA;oBAEJ;gBACF;gBACA,IAAI,CAAEvC,CAAAA,IAAIkF,MAAM,KAAK,SAASlF,IAAIkF,MAAM,KAAK,MAAK,GAAI;oBACpDjF,IAAIsC,SAAS,CAAC,SAAS;wBAAC;wBAAO;qBAAO;oBACtCtC,IAAIuC,UAAU,GAAG;oBACjB,OAAO,MAAMhB,aACX9E,IAAI4G,KAAK,CAAC,QAAQ,OAClB,QACA3B,aACA;wBACE,mBAAmB;oBACrB;gBAEJ;gBAEA,IAAI;oBACF,OAAO,MAAM9E,YAAYmD,KAAKC,KAAK4D,cAAce,QAAQ,EAAE;wBACzDO,MAAMtB,cAAcuB,SAAS;wBAC7B,uEAAuE;wBACvEC,MAAM3G,OAAO4G,aAAa;oBAC5B;gBACF,EAAE,OAAOxE,KAAU;oBACjB;;;;;WAKC,GACD,MAAMyE,wCAAwC,IAAIhE,IAAI;wBACpD,kFAAkF;wBAClF,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,kDAAkD;wBAClD,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,gGAAgG;wBAChG,+FAA+F;wBAC/F,qFAAqF;wBACrF,OAAO;wBAEP,8DAA8D;wBAC9D,+FAA+F;wBAC/F;wBAEA,0DAA0D;wBAC1D,+FAA+F;wBAC/F;wBAEA,2DAA2D;wBAC3D,+FAA+F;wBAC/F;qBACD;oBAED,IAAIiE,mBAAmBD,sCAAsCT,GAAG,CAC9DhE,IAAI0B,UAAU;oBAGhB,qCAAqC;oBACrC,IAAI,CAACgD,kBAAkB;wBACnB1E,IAAY0B,UAAU,GAAG;oBAC7B;oBAEA,IAAI,OAAO1B,IAAI0B,UAAU,KAAK,UAAU;wBACtC,MAAMd,aAAa,CAAC,CAAC,EAAEZ,IAAI0B,UAAU,CAAC,CAAC;wBACvC,MAAMiD,eAAe,CAAC,EAAE3E,IAAI0B,UAAU,CAAC,CAAC;wBACxCvC,IAAIuC,UAAU,GAAG1B,IAAI0B,UAAU;wBAC/B,OAAO,MAAMhB,aACX9E,IAAI4G,KAAK,CAAC5B,YAAY,OACtBA,YACAC,aACA;4BACE,mBAAmB8D;wBACrB;oBAEJ;oBACA,MAAM3E;gBACR;YACF;YAEA,IAAI+C,eAAe;gBACjBvC,eAAeoE,GAAG,CAAC7B,cAAce,QAAQ;gBAEzC,OAAO,MAAMpD,aACXC,WACAA,UAAUU,QAAQ,IAAI,KACtBR,aACA;oBACE,mBAAmBkC,cAAce,QAAQ;gBAC3C;YAEJ;YAEA,WAAW;YACX3E,IAAIsC,SAAS,CACX,iBACA;YAGF,0IAA0I;YAC1I,IAAIlE,KAAKI,GAAG,IAAI,CAACoF,iBAAiBpC,UAAUU,QAAQ,KAAK,gBAAgB;gBACvElC,IAAIuC,UAAU,GAAG;gBACjBvC,IAAIwC,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,MAAMkD,cAActH,KAAKI,GAAG,GACxBQ,sCAAAA,mBAAoBsB,YAAY,CAACqF,cAAc,GAC/C,MAAM9G,UAAU+G,OAAO,CAAC;YAE5B5F,IAAIuC,UAAU,GAAG;YAEjB,IAAImD,aAAa;gBACf,OAAO,MAAMnE,aACXC,WACApD,KAAKI,GAAG,GAAG,eAAe,eAC1BkD,aACA;oBACE,mBAAmB;gBACrB;YAEJ;YAEA,MAAMH,aAAaC,WAAW,QAAQE,aAAa;gBACjD,mBAAmB;YACrB;QACF;QAEA,IAAI;YACF,MAAMwB,cAAc;QACtB,EAAE,OAAOrC,KAAK;YACZ,IAAI;gBACF,IAAIY,aAAa;gBACjB,IAAI+D,eAAe;gBAEnB,IAAI3E,eAAe9D,aAAa;oBAC9B0E,aAAa;oBACb+D,eAAe;gBACjB,OAAO;oBACLK,QAAQC,KAAK,CAACjF;gBAChB;gBACAb,IAAIuC,UAAU,GAAGwD,OAAOP;gBACxB,OAAO,MAAMjE,aAAa9E,IAAI4G,KAAK,CAAC5B,YAAY,OAAOA,YAAY,GAAG;oBACpE,mBAAmB+D;gBACrB;YACF,EAAE,OAAOQ,MAAM;gBACbH,QAAQC,KAAK,CAACE;YAChB;YACAhG,IAAIuC,UAAU,GAAG;YACjBvC,IAAIwC,GAAG,CAAC;QACV;IACF;IAEA,IAAIS,iBAAuC9B;IAC3C,IAAI/C,KAAKmC,qBAAqB,EAAE;QAC9B,2CAA2C;QAC3C,MAAM,EACJ0F,wBAAwB,EACxBC,iBAAiB,EAClB,GAAG1G,QAAQ;QACZyD,iBAAiBgD,yBAAyBhD;QAC1CiD;IACF;IACAhI,eAAe,CAACE,KAAKM,GAAG,CAAC,GAAGuE;IAE5B,MAAMkD,iBAAuC,OAAOpG,KAAKqG,QAAQC;QAC/D,IAAI;YACFtG,IAAIgB,EAAE,CAAC,SAAS,CAACK;YACf,2BAA2B;YAC3B,uBAAuB;YACzB;YACAgF,OAAOrF,EAAE,CAAC,SAAS,CAACK;YAClB,2BAA2B;YAC3B,uBAAuB;YACzB;YAEA,IAAIhD,KAAKI,GAAG,IAAIQ,oBAAoB;oBAC9Be;gBAAJ,KAAIA,WAAAA,IAAItD,GAAG,qBAAPsD,SAASuG,QAAQ,CAAC,CAAC,kBAAkB,CAAC,GAAG;oBAC3C,OAAOtH,mBAAmBuE,WAAW,CAACgD,KAAK,CAACxG,KAAKqG,QAAQC;gBAC3D;YACF;YAEA,MAAM,EAAEzC,aAAa,EAAEpC,SAAS,EAAE,GAAG,MAAMP,cAAc;gBACvDlB;gBACAC,KAAKoG;gBACLvC,cAAc;gBACdC,QAAQnG,uBAAuByI;YACjC;YAEA,mDAAmD;YACnD,oCAAoC;YACpC,IAAIxC,eAAe;gBACjB,OAAOwC,OAAO5D,GAAG;YACnB;YAEA,IAAIhB,UAAU6C,QAAQ,EAAE;gBACtB,OAAO,MAAMnH,aAAa6C,KAAKqG,QAAe5E,WAAW6E;YAC3D;QAEA,sEAAsE;QACtE,sDAAsD;QACxD,EAAE,OAAOxF,KAAK;YACZgF,QAAQC,KAAK,CAAC,kCAAkCjF;YAChDuF,OAAO5D,GAAG;QACZ;IACF;IAEA,OAAO;QAACS;QAAgBkD;KAAe;AACzC"}