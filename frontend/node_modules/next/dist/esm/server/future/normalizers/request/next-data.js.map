{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/next-data.ts"], "names": ["denormalizePagePath", "PrefixPathnameNormalizer", "SuffixPathnameNormalizer", "NextDataPathnameNormalizer", "constructor", "buildID", "suffix", "Error", "prefix", "match", "pathname", "normalize", "matched"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,yDAAwD;AAC5F,SAASC,wBAAwB,QAAQ,WAAU;AACnD,SAASC,wBAAwB,QAAQ,WAAU;AAEnD,OAAO,MAAMC;IAGXC,YAAYC,OAAe,CAAE;aADZC,SAAS,IAAIJ,yBAAyB;QAErD,IAAI,CAACG,SAAS;YACZ,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAI,CAACC,MAAM,GAAG,IAAIP,yBAAyB,CAAC,YAAY,EAAEI,QAAQ,CAAC;IACrE;IAEOI,MAAMC,QAAgB,EAAE;QAC7B,OAAO,IAAI,CAACF,MAAM,CAACC,KAAK,CAACC,aAAa,IAAI,CAACJ,MAAM,CAACG,KAAK,CAACC;IAC1D;IAEOC,UAAUD,QAAgB,EAAEE,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACH,KAAK,CAACC,WAAW,OAAOA;QAE9CA,WAAW,IAAI,CAACF,MAAM,CAACG,SAAS,CAACD,UAAU;QAC3CA,WAAW,IAAI,CAACJ,MAAM,CAACK,SAAS,CAACD,UAAU;QAE3C,OAAOV,oBAAoBU;IAC7B;AACF"}