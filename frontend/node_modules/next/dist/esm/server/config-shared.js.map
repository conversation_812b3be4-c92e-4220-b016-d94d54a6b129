{"version": 3, "sources": ["../../src/server/config-shared.ts"], "names": ["os", "imageConfigDefault", "defaultConfig", "env", "webpack", "eslint", "ignoreDuringBuilds", "typescript", "ignoreBuildErrors", "tsconfigPath", "distDir", "cleanDistDir", "assetPrefix", "config<PERSON><PERSON><PERSON>", "useFileSystemPublicRoutes", "generateBuildId", "generateEtags", "pageExtensions", "poweredByHeader", "compress", "analyticsId", "process", "VERCEL_ANALYTICS_ID", "images", "devIndicators", "buildActivity", "buildActivityPosition", "onDemandEntries", "maxInactiveAge", "pagesBufferLength", "amp", "canonicalBase", "basePath", "sassOptions", "trailingSlash", "i18n", "productionBrowserSourceMaps", "optimizeFonts", "excludeDefaultMomentLocales", "serverRuntimeConfig", "publicRuntimeConfig", "reactProductionProfiling", "reactStrictMode", "httpAgentOptions", "keepAlive", "outputFileTracing", "staticPageGenerationTimeout", "swcMinify", "output", "NEXT_PRIVATE_STANDALONE", "undefined", "modularizeImports", "experimental", "windowHistorySupport", "serverMinification", "serverSourceMaps", "caseSensitiveRoutes", "useDeploymentId", "deploymentId", "useDeploymentIdServerActions", "appDocumentPreloading", "clientRouterFilter", "clientRouterFilterRedirects", "fetchCacheKeyPrefix", "middlewarePrefetch", "optimisticClientCache", "manualClientBasePath", "cpus", "Math", "max", "Number", "CIRCLE_NODE_TOTAL", "length", "memoryBasedWorkersCount", "isrFlushToDisk", "workerThreads", "proxyTimeout", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "externalDir", "disableOptimizedLoading", "gzipSize", "craCompat", "esmExternals", "isrMemoryCacheSize", "incremental<PERSON>ache<PERSON>andlerPath", "fullySpecified", "outputFileTracingRoot", "NEXT_PRIVATE_OUTPUT_TRACE_ROOT", "swcTraceProfiling", "forceSwcTransforms", "swcPlugins", "largePageDataBytes", "disablePostcssPresetEnv", "urlImports", "adjustFontFallbacks", "adjustFontFallbacksWithSizeAdjust", "turbo", "turbotrace", "typedRoutes", "instrumentationHook", "bundlePagesExternals", "ppr", "normalizeConfig", "phase", "config"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAGnB,SAASC,kBAAkB,QAAQ,6BAA4B;AAgrB/D,OAAO,MAAMC,gBAA4B;IACvCC,KAAK,CAAC;IACNC,SAAS;IACTC,QAAQ;QACNC,oBAAoB;IACtB;IACAC,YAAY;QACVC,mBAAmB;QACnBC,cAAc;IAChB;IACAC,SAAS;IACTC,cAAc;IACdC,aAAa;IACbC,cAAc;IACdC,2BAA2B;IAC3BC,iBAAiB,IAAM;IACvBC,eAAe;IACfC,gBAAgB;QAAC;QAAO;QAAM;QAAO;KAAK;IAC1CC,iBAAiB;IACjBC,UAAU;IACVC,aAAaC,QAAQlB,GAAG,CAACmB,mBAAmB,IAAI;IAChDC,QAAQtB;IACRuB,eAAe;QACbC,eAAe;QACfC,uBAAuB;IACzB;IACAC,iBAAiB;QACfC,gBAAgB,KAAK;QACrBC,mBAAmB;IACrB;IACAC,KAAK;QACHC,eAAe;IACjB;IACAC,UAAU;IACVC,aAAa,CAAC;IACdC,eAAe;IACfC,MAAM;IACNC,6BAA6B;IAC7BC,eAAe;IACfC,6BAA6B;IAC7BC,qBAAqB,CAAC;IACtBC,qBAAqB,CAAC;IACtBC,0BAA0B;IAC1BC,iBAAiB;IACjBC,kBAAkB;QAChBC,WAAW;IACb;IACAC,mBAAmB;IACnBC,6BAA6B;IAC7BC,WAAW;IACXC,QAAQ,CAAC,CAAC3B,QAAQlB,GAAG,CAAC8C,uBAAuB,GAAG,eAAeC;IAC/DC,mBAAmBD;IACnBE,cAAc;QACZC,sBAAsB;QACtBC,oBAAoB;QACpBC,kBAAkB;QAClBC,qBAAqB;QACrBC,iBAAiB;QACjBC,cAAcR;QACdS,8BAA8B;QAC9BC,uBAAuBV;QACvBW,oBAAoB;QACpBC,6BAA6B;QAC7BC,qBAAqB;QACrBC,oBAAoB;QACpBC,uBAAuB;QACvBC,sBAAsB;QACtBC,MAAMC,KAAKC,GAAG,CACZ,GACA,AAACC,CAAAA,OAAOjD,QAAQlB,GAAG,CAACoE,iBAAiB,KACnC,AAACvE,CAAAA,GAAGmE,IAAI,MAAM;YAAEK,QAAQ;QAAE,CAAA,EAAGA,MAAM,AAAD,IAAK;QAE3CC,yBAAyB;QACzBC,gBAAgB;QAChBC,eAAe;QACfC,cAAc1B;QACd2B,aAAa;QACbC,mBAAmB;QACnBC,mBAAmB;QACnBC,aAAa;QACbC,yBAAyB;QACzBC,UAAU;QACVC,WAAW;QACXC,cAAc;QACd,wBAAwB;QACxBC,oBAAoB,KAAK,OAAO;QAChCC,6BAA6BpC;QAC7BqC,gBAAgB;QAChBC,uBAAuBnE,QAAQlB,GAAG,CAACsF,8BAA8B,IAAI;QACrEC,mBAAmB;QACnBC,oBAAoB;QACpBC,YAAY1C;QACZ2C,oBAAoB,MAAM;QAC1BC,yBAAyB5C;QACzBpB,KAAKoB;QACL6C,YAAY7C;QACZ8C,qBAAqB;QACrBC,mCAAmC;QACnCC,OAAOhD;QACPiD,YAAYjD;QACZkD,aAAa;QACbC,qBAAqB;QACrBC,sBAAsB;QACtBC,KAAK;IACP;AACF,EAAC;AAED,OAAO,eAAeC,gBAAgBC,KAAa,EAAEC,MAAW;IAC9D,IAAI,OAAOA,WAAW,YAAY;QAChCA,SAASA,OAAOD,OAAO;YAAEvG;QAAc;IACzC;IACA,gFAAgF;IAChF,OAAO,MAAMwG;AACf"}