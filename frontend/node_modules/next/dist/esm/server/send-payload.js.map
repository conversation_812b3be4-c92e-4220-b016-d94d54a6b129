{"version": 3, "sources": ["../../src/server/send-payload.ts"], "names": ["isResSent", "generateETag", "fresh", "formatRevalidate", "RSC_CONTENT_TYPE_HEADER", "sendEtagResponse", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "headers", "statusCode", "end", "sendRenderResult", "result", "type", "generateEtags", "poweredByHeader", "revalidate", "payload", "isDynamic", "toUnchunkedString", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "contentType", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": "AAIA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,YAAY,QAAQ,aAAY;AACzC,OAAOC,WAAW,2BAA0B;AAC5C,SAASC,gBAAgB,QAAQ,mBAAkB;AACnD,SAASC,uBAAuB,QAAQ,0CAAyC;AAEjF,OAAO,SAASC,iBACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIN,MAAMI,IAAII,OAAO,EAAE;QAAEF;IAAK,IAAI;QAChCD,IAAII,UAAU,GAAG;QACjBJ,IAAIK,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEA,OAAO,eAAeC,iBAAiB,EACrCP,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,UAAU,EASX;IACC,IAAIlB,UAAUO,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,IAAI,OAAOS,eAAe,aAAa;QACrCX,IAAIE,SAAS,CAAC,iBAAiBN,iBAAiBe;IAClD;IAEA,MAAMC,UAAUL,OAAOM,SAAS,GAAG,OAAON,OAAOO,iBAAiB;IAElE,IAAIF,YAAY,MAAM;QACpB,MAAMX,OAAOQ,gBAAgBf,aAAakB,WAAWG;QACrD,IAAIjB,iBAAiBC,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIgB,SAAS,CAAC,iBAAiB;QAClChB,IAAIE,SAAS,CACX,gBACAK,OAAOU,WAAW,GACdV,OAAOU,WAAW,GAClBT,SAAS,QACTX,0BACAW,SAAS,SACT,qBACA;IAER;IAEA,IAAII,SAAS;QACXZ,IAAIE,SAAS,CAAC,kBAAkBgB,OAAOC,UAAU,CAACP;IACpD;IAEA,IAAIb,IAAIqB,MAAM,KAAK,QAAQ;QACzBpB,IAAIK,GAAG,CAAC;QACR;IACF;IAEA,IAAIO,YAAY,MAAM;QACpBZ,IAAIK,GAAG,CAACO;QACR;IACF;IAEA,uEAAuE;IACvE,MAAML,OAAOc,kBAAkB,CAACrB;AAClC"}