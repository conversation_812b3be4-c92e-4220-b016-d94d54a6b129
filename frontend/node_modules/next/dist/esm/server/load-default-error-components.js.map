{"version": 3, "sources": ["../../src/server/load-default-error-components.ts"], "names": ["BUILD_MANIFEST", "join", "interopDefault", "getTracer", "LoadComponentsSpan", "loadManifestWithRetries", "loadDefaultErrorComponentsImpl", "distDir", "Document", "require", "AppMod", "App", "ComponentMod", "Component", "routeModule", "userland", "default", "pageConfig", "buildManifest", "reactLoadableManifest", "page", "loadDefaultErrorComponents", "wrap"], "mappings": "AAeA,SAASA,cAAc,QAAQ,0BAAyB;AACxD,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,uBAAuB,QAAQ,oBAAmB;AA2B3D,eAAeC,+BACbC,OAAe;IAEf,MAAMC,WAAWN,eAAeO,QAAQ;IACxC,MAAMC,SAASD,QAAQ;IACvB,MAAME,MAAMT,eAAeQ;IAE3B,yDAAyD;IACzD,qGAAqG;IACrG,MAAME,eACJH,QAAQ;IACV,MAAMI,YAAYD,aAAaE,WAAW,CAACC,QAAQ,CAACC,OAAO;IAE3D,OAAO;QACLL;QACAH;QACAK;QACAI,YAAY,CAAC;QACbC,eAAe,MAAMb,wBACnBJ,KAAKM,SAAS,CAAC,SAAS,EAAEP,eAAe,CAAC;QAE5CmB,uBAAuB,CAAC;QACxBP;QACAQ,MAAM;QACNN,aAAaF,aAAaE,WAAW;IACvC;AACF;AACA,OAAO,MAAMO,6BAA6BlB,YAAYmB,IAAI,CACxDlB,mBAAmBiB,0BAA0B,EAC7Cf,gCACD"}