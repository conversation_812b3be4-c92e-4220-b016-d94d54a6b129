{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["webpack", "needsExperimentalReact", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getDefineEnv", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "previewModeId", "__NEXT_DEFINE_ENV", "Object", "keys", "process", "env", "reduce", "prev", "startsWith", "JSON", "stringify", "acc", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "windowHistorySupport", "useDeploymentIdServerActions", "deploymentId", "manualClientBasePath", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "assetPrefix", "undefined", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAEA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,sBAAsB,QAAQ,wCAAuC;AAE9E,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AA0BA,OAAO,SAASC,aAAa,EAC3BC,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBV,MAAM,EACNW,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,aAAa,EACU;QAyHNpB,gBAKSA,iBAY0BA;IAzIpD,OAAO;QACL,+CAA+C;QAC/CqB,mBAAmB;QAEnB,GAAGC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEC,MAAM,CAChC,CAACC,MAAiC1B;YAChC,IAAIA,IAAI2B,UAAU,CAAC,iBAAiB;gBAClCD,IAAI,CAAC,CAAC,YAAY,EAAE1B,IAAI,CAAC,CAAC,GAAG4B,KAAKC,SAAS,CAACN,QAAQC,GAAG,CAACxB,IAAI;YAC9D;YACA,OAAO0B;QACT,GACA,CAAC,EACF;QACD,GAAGL,OAAOC,IAAI,CAACvB,OAAOyB,GAAG,EAAEC,MAAM,CAAC,CAACK,KAAK9B;YACtCF,qBAAqBC,QAAQC;YAE7B,OAAO;gBACL,GAAG8B,GAAG;gBACN,CAAC,CAAC,YAAY,EAAE9B,IAAI,CAAC,CAAC,EAAE4B,KAAKC,SAAS,CAAC9B,OAAOyB,GAAG,CAACxB,IAAI;YACxD;QACF,GAAG,CAAC,EAAE;QACN,GAAI,CAACe,eACD,CAAC,IACD;YACEgB,aAAaH,KAAKC,SAAS,CACzB;;;;aAIC,GACDN,QAAQC,GAAG,CAACQ,0BAA0B,IAAI;QAE9C,CAAC;QACL,qBAAqBJ,KAAKC,SAAS,CAACtB;QACpC,yBAAyBqB,KAAKC,SAAS,CAACtB;QACxC,6DAA6D;QAC7D,wBAAwBqB,KAAKC,SAAS,CAACnB,MAAM,gBAAgB;QAC7D,4BAA4BkB,KAAKC,SAAS,CACxCd,eAAe,SAASE,eAAe,WAAW;QAEpD,4BAA4BW,KAAKC,SAAS,CAAC;QAC3C,6CAA6CD,KAAKC,SAAS,CACzD9B,OAAOkC,YAAY,CAACC,oBAAoB;QAE1C,4CAA4CN,KAAKC,SAAS,CACxD9B,OAAOkC,YAAY,CAACE,4BAA4B;QAElD,kCAAkCP,KAAKC,SAAS,CAC9C9B,OAAOkC,YAAY,CAACG,YAAY,IAAI;QAEtC,6CACER,KAAKC,SAAS,CAACjB;QACjB,sCAAsCgB,KAAKC,SAAS,CAACV;QACrD,iDAAiDS,KAAKC,SAAS,CAC7DrB;QAEF,0CAA0CoB,KAAKC,SAAS,CACtDX,sBAAsB,EAAE;QAE1B,8CAA8CU,KAAKC,SAAS,CAC1D9B,OAAOkC,YAAY,CAACI,oBAAoB;QAE1C,mDAAmDT,KAAKC,SAAS,CAC/D9B,OAAOkC,YAAY,CAACK,kBAAkB;QAExC,6CAA6CV,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB8B,YAAY;QAEnC,6CAA6CX,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB+B,aAAa;QAEpC,8CAA8CZ,KAAKC,SAAS,CAC1D9B,OAAOkC,YAAY,CAACQ,qBAAqB;QAE3C,0CAA0Cb,KAAKC,SAAS,CACtD9B,OAAOkC,YAAY,CAACS,kBAAkB;QAExC,mCAAmCd,KAAKC,SAAS,CAAC9B,OAAO4C,WAAW;QACpE,mBAAmBf,KAAKC,SAAS,CAACf;QAClC,gCAAgCc,KAAKC,SAAS,CAC5CN,QAAQC,GAAG,CAACoB,gBAAgB;QAE9B,2FAA2F;QAC3F,GAAIlC,OAAQI,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+Ba,KAAKC,SAAS,CAAClB;QAChD,IACA,CAAC,CAAC;QACN,qCAAqCiB,KAAKC,SAAS,CAAC9B,OAAO8C,aAAa;QACxE,sCAAsCjB,KAAKC,SAAS,CAClD9B,OAAO+C,aAAa,CAACC,aAAa;QAEpC,+CAA+CnB,KAAKC,SAAS,CAC3D9B,OAAO+C,aAAa,CAACE,qBAAqB;QAE5C,kCAAkCpB,KAAKC,SAAS,CAC9C9B,OAAOkD,eAAe,KAAK,OAAO,QAAQlD,OAAOkD,eAAe;QAElE,sCAAsCrB,KAAKC,SAAS,CAClD,6EAA6E;QAC7E9B,OAAOkD,eAAe,KAAK,OAAO,OAAOlD,OAAOkD,eAAe;QAEjE,qCAAqCrB,KAAKC,SAAS,CACjD,CAACnB,OAAOX,OAAOmD,aAAa;QAE9B,mCAAmCtB,KAAKC,SAAS,CAC/C9B,OAAOkC,YAAY,CAACkB,WAAW,IAAI,CAACzC;QAEtC,qCAAqCkB,KAAKC,SAAS,CACjD9B,OAAOkC,YAAY,CAACmB,iBAAiB,IAAI,CAAC1C;QAE5C,yCAAyCkB,KAAKC,SAAS,CACrD9B,OAAOkC,YAAY,CAACoB,iBAAiB;QAEvC,iCAAiCzB,KAAKC,SAAS,CAAC;YAC9CyB,aAAavD,OAAOwD,MAAM,CAACD,WAAW;YACtCE,YAAYzD,OAAOwD,MAAM,CAACC,UAAU;YACpCC,MAAM1D,OAAOwD,MAAM,CAACE,IAAI;YACxBC,QAAQ3D,OAAOwD,MAAM,CAACG,MAAM;YAC5BC,qBAAqB5D,OAAOwD,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE7D,2BAAAA,iBAAAA,OAAQwD,MAAM,qBAAdxD,eAAgB6D,WAAW;YACxC,GAAIlD,MACA;gBACE,gEAAgE;gBAChEmD,SAAS9D,OAAOwD,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE/D,kBAAAA,OAAOwD,MAAM,qBAAbxD,gBAAe+D,cAAc;gBAC7CC,QAAQhE,OAAOgE,MAAM;YACvB,IACA,CAAC,CAAC;QACR;QACA,sCAAsCnC,KAAKC,SAAS,CAAC9B,OAAOiE,QAAQ;QACpE,uCAAuCpC,KAAKC,SAAS,CACnD9B,OAAOkC,YAAY,CAACgC,cAAc;QAEpC,mCAAmCrC,KAAKC,SAAS,CAAChB;QAClD,oCAAoCe,KAAKC,SAAS,CAAC9B,OAAOgE,MAAM;QAChE,mCAAmCnC,KAAKC,SAAS,CAAC,CAAC,CAAC9B,OAAOmE,IAAI;QAC/D,mCAAmCtC,KAAKC,SAAS,EAAC9B,eAAAA,OAAOmE,IAAI,qBAAXnE,aAAa8D,OAAO;QACtE,mCAAmCjC,KAAKC,SAAS,CAAC9B,OAAOoE,WAAW;QACpE,kDAAkDvC,KAAKC,SAAS,CAC9D9B,OAAOqE,0BAA0B;QAEnC,0DAA0DxC,KAAKC,SAAS,CACtE9B,OAAOkC,YAAY,CAACoC,iCAAiC;QAEvD,4CAA4CzC,KAAKC,SAAS,CACxD9B,OAAOuE,yBAAyB;QAElC,iDAAiD1C,KAAKC,SAAS,CAC7D9B,OAAOkC,YAAY,CAACsC,oBAAoB,IACtCxE,OAAOkC,YAAY,CAACsC,oBAAoB,CAACC,MAAM,GAAG;QAEtD,6CAA6C5C,KAAKC,SAAS,CACzD9B,OAAOkC,YAAY,CAACsC,oBAAoB;QAE1C,mCAAmC3C,KAAKC,SAAS,CAAC9B,OAAO0E,WAAW;QACpE,GAAIzD,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiBY,KAAKC,SAAS,CAAC;QAClC,IACA6C,SAAS;QACb,GAAI1D,0BACA;YACE,yCAAyCY,KAAKC,SAAS,CACrDhC,uBAAuBE;QAE3B,IACA2E,SAAS;IACf;AACF;AAEA,OAAO,SAASC,mBAAmBC,OAA+B;IAChE,OAAO,IAAIhF,QAAQiF,YAAY,CAACvE,aAAasE;AAC/C"}