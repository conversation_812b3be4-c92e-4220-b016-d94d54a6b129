{"version": 3, "sources": ["../../../src/build/webpack/utils.ts"], "names": ["isAppRouteRoute", "traverseModules", "compilation", "callback", "filterChunkGroup", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "mod", "modId", "getModuleId", "anyModule", "modules", "subMod", "forEachEntryModule", "name", "entry", "entries", "startsWith", "entryDependency", "dependencies", "request", "entryModule", "moduleGraph", "getResolvedModule", "dependency", "modRequest", "includes"], "mappings": "AACA,SAASA,eAAe,QAAQ,+BAA8B;AAE9D,OAAO,SAASC,gBACdC,WAAgC,EAChCC,QAKQ,EACRC,gBAA8D;IAE9DF,YAAYG,WAAW,CAACC,OAAO,CAAC,CAACC;QAC/B,IAAIH,oBAAoB,CAACA,iBAAiBG,aAAa;YACrD;QACF;QACAA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;YACzB,MAAMC,eAAeR,YAAYS,UAAU,CAACC,uBAAuB,CACjEH;YAGF,KAAK,MAAMI,OAAOH,aAAc;gBAC9B,MAAMI,QAAQZ,YAAYS,UAAU,CAACI,WAAW,CAACF;gBACjDV,SAASU,KAAKJ,OAAOF,YAAYO;gBACjC,MAAME,YAAYH;gBAClB,IAAIG,UAAUC,OAAO,EAAE;oBACrB,KAAK,MAAMC,UAAUF,UAAUC,OAAO,CACpCd,SAASe,QAAQT,OAAOF,YAAYO;gBACxC;YACF;QACF;IACF;AACF;AAEA,mCAAmC;AACnC,OAAO,SAASK,mBACdjB,WAAgB,EAChBC,QAA6E;IAE7E,KAAK,MAAM,CAACiB,MAAMC,MAAM,IAAInB,YAAYoB,OAAO,CAACA,OAAO,GAAI;YAYvDD;QAXF,gCAAgC;QAChC,IACED,KAAKG,UAAU,CAAC,aAChB,4BAA4B;QAC3BH,KAAKG,UAAU,CAAC,WAAWvB,gBAAgBoB,OAC5C;YACA;QACF;QAEA,wDAAwD;QACxD,MAAMI,mBACJH,sBAAAA,MAAMI,YAAY,qBAAlBJ,mBAAoB,CAAC,EAAE;QACzB,mDAAmD;QACnD,IAAI,CAACG,mBAAmB,CAACA,gBAAgBE,OAAO,EAAE;QAElD,MAAMA,UAAUF,gBAAgBE,OAAO;QAEvC,IACE,CAACA,QAAQH,UAAU,CAAC,4BACpB,CAACG,QAAQH,UAAU,CAAC,qBAEpB;QAEF,IAAII,cACFzB,YAAY0B,WAAW,CAACC,iBAAiB,CAACL;QAE5C,IAAIE,QAAQH,UAAU,CAAC,0BAA0B;YAC/CI,YAAYF,YAAY,CAACnB,OAAO,CAAC,CAACwB;gBAChC,MAAMC,aAAiC,AAACD,WAAmBJ,OAAO;gBAClE,IAAIK,8BAAAA,WAAYC,QAAQ,CAAC,oBAAoB;oBAC3CL,cAAczB,YAAY0B,WAAW,CAACC,iBAAiB,CAACC;gBAC1D;YACF;QACF;QAEA3B,SAAS;YAAEiB;YAAMO;QAAY;IAC/B;AACF"}