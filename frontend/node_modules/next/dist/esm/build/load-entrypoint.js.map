{"version": 3, "sources": ["../../src/build/load-entrypoint.ts"], "names": ["fs", "path", "PACKAGE_ROOT", "normalize", "join", "__dirname", "TEMPLATE_FOLDER", "TEMPLATES_ESM_FOLDER", "loadEntrypoint", "entrypoint", "replacements", "injections", "imports", "filepath", "resolve", "file", "readFile", "count", "replaceAll", "_", "fromRequest", "importRequest", "relative", "replace", "startsWith", "Error", "JSON", "stringify", "replaced", "Set", "RegExp", "Object", "keys", "map", "k", "match", "key", "parse", "add", "matches", "size", "length", "difference", "filter", "has", "injected", "importsAdded", "asNamespace"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AAEvB,6DAA6D;AAC7D,MAAMC,eAAeD,KAAKE,SAAS,CAACF,KAAKG,IAAI,CAACC,WAAW;AACzD,MAAMC,kBAAkBL,KAAKG,IAAI,CAACC,WAAW;AAC7C,MAAME,uBAAuBN,KAAKE,SAAS,CACzCF,KAAKG,IAAI,CAACC,WAAW;AAGvB;;;;;;;;;;;;;;;CAeC,GACD,OAAO,eAAeG,eACpBC,UAQe,EACfC,YAA6C,EAC7CC,UAAmC,EACnCC,OAAuC;IAEvC,MAAMC,WAAWZ,KAAKa,OAAO,CAC3Bb,KAAKG,IAAI,CAACG,sBAAsB,CAAC,EAAEE,WAAW,GAAG,CAAC;IAGpD,IAAIM,OAAO,MAAMf,GAAGgB,QAAQ,CAACH,UAAU;IAEvC,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAII,QAAQ;IACZF,OAAOA,KAAKG,UAAU,CACpB,kCACA,SAAUC,CAAC,EAAEC,WAAW,EAAEC,aAAa;QACrCJ;QAEA,MAAMK,WAAWrB,KACdqB,QAAQ,CACPpB,cACAD,KAAKa,OAAO,CAACR,iBAAiBc,eAAeC,eAE/C,2DAA2D;SAC1DE,OAAO,CAAC,OAAO;QAElB,0EAA0E;QAC1E,uEAAuE;QACvE,oCAAoC;QACpC,IAAI,CAACD,SAASE,UAAU,CAAC,UAAU;YACjC,MAAM,IAAIC,MACR,CAAC,kEAAkE,EAAEH,SAAS,CAAC,CAAC;QAEpF;QAEA,OAAOF,cACH,CAAC,KAAK,EAAEM,KAAKC,SAAS,CAACL,UAAU,CAAC,GAClC,CAAC,OAAO,EAAEI,KAAKC,SAAS,CAACL,UAAU,CAAC;IAC1C;IAGF,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,iBAAiB;IACjB,IAAIL,UAAU,GAAG;QACf,MAAM,IAAIQ,MAAM;IAClB;IAEA,MAAMG,WAAW,IAAIC;IAErB,2EAA2E;IAC3E,uCAAuC;IACvCd,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OACF,CAAC,EAAEC,OAAOC,IAAI,CAACtB,cACZuB,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnB9B,IAAI,CAAC,KAAK,CAAC,EACd,MAEF,CAAC+B;QACC,MAAMC,MAAMV,KAAKW,KAAK,CAACF;QAEvB,IAAI,CAAEC,CAAAA,OAAO1B,YAAW,GAAI;YAC1B,MAAM,IAAIe,MAAM,CAAC,wCAAwC,EAAEW,IAAI,CAAC;QAClE;QAEAR,SAASU,GAAG,CAACF;QAEb,OAAOV,KAAKC,SAAS,CAACjB,YAAY,CAAC0B,IAAI;IACzC;IAGF,4DAA4D;IAC5D,IAAIG,UAAUxB,KAAKoB,KAAK,CAAC;IACzB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,6DAA6D,EAAEc,QAAQnC,IAAI,CAC1E,MACA,CAAC;IAEP;IAEA,mEAAmE;IACnE,IAAIwB,SAASY,IAAI,KAAKT,OAAOC,IAAI,CAACtB,cAAc+B,MAAM,EAAE;QACtD,yEAAyE;QACzE,uEAAuE;QACvE,kDAAkD;QAClD,MAAMC,aAAaX,OAAOC,IAAI,CAACtB,cAAciC,MAAM,CACjD,CAACP,MAAQ,CAACR,SAASgB,GAAG,CAACR;QAGzB,MAAM,IAAIX,MACR,CAAC,+DAA+D,EAAEiB,WAAWtC,IAAI,CAC/E,MACA,YAAY,CAAC;IAEnB;IAEA,0BAA0B;IAC1B,MAAMyC,WAAW,IAAIhB;IACrB,IAAIlB,YAAY;QACd,iEAAiE;QACjEI,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OAAO,CAAC,WAAW,EAAEC,OAAOC,IAAI,CAACrB,YAAYP,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAC/D,CAACe,GAAGiB;YACF,IAAI,CAAEA,CAAAA,OAAOzB,UAAS,GAAI;gBACxB,MAAM,IAAIc,MAAM,CAAC,gCAAgC,EAAEW,IAAI,CAAC;YAC1D;YAEAS,SAASP,GAAG,CAACF;YAEb,OAAO,CAAC,MAAM,EAAEA,IAAI,GAAG,EAAEzB,UAAU,CAACyB,IAAI,CAAC,CAAC;QAC5C;IAEJ;IAEA,oDAAoD;IACpDG,UAAUxB,KAAKoB,KAAK,CAAC;IACrB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,oDAAoD,EAAEc,QAAQnC,IAAI,CACjE,MACA,CAAC;IAEP;IAEA,2DAA2D;IAC3D,IAAIyC,SAASL,IAAI,KAAKT,OAAOC,IAAI,CAACrB,cAAc,CAAC,GAAG8B,MAAM,EAAE;QAC1D,uEAAuE;QACvE,2EAA2E;QAC3E,8BAA8B;QAC9B,MAAMC,aAAaX,OAAOC,IAAI,CAACrB,cAAc,CAAC,GAAGgC,MAAM,CACrD,CAACP,MAAQ,CAACS,SAASD,GAAG,CAACR;QAGzB,MAAM,IAAIX,MACR,CAAC,sDAAsD,EAAEiB,WAAWtC,IAAI,CACtE,MACA,YAAY,CAAC;IAEnB;IAEA,gCAAgC;IAChC,MAAM0C,eAAe,IAAIjB;IACzB,IAAIjB,SAAS;QACX,8DAA8D;QAC9DG,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OACF,CAAC,8BAA8B,EAAEC,OAAOC,IAAI,CAACpB,SAASR,IAAI,CAAC,KAAK,CAAC,CAAC,EAClE,MAEF,CAACe,GAAG4B,cAAc,EAAE,EAAEX;YACpB,IAAI,CAAEA,CAAAA,OAAOxB,OAAM,GAAI;gBACrB,MAAM,IAAIa,MAAM,CAAC,sCAAsC,EAAEW,IAAI,CAAC;YAChE;YAEAU,aAAaR,GAAG,CAACF;YAEjB,IAAIxB,OAAO,CAACwB,IAAI,EAAE;gBAChB,OAAO,CAAC,OAAO,EAAEW,YAAY,EAAEX,IAAI,MAAM,EAAEV,KAAKC,SAAS,CACvDf,OAAO,CAACwB,IAAI,EACZ,CAAC;YACL,OAAO;gBACL,OAAO,CAAC,MAAM,EAAEA,IAAI,OAAO,CAAC;YAC9B;QACF;IAEJ;IAEA,iDAAiD;IACjDG,UAAUxB,KAAKoB,KAAK,CAAC;IACrB,IAAII,SAAS;QACX,MAAM,IAAId,MACR,CAAC,iDAAiD,EAAEc,QAAQnC,IAAI,CAAC,MAAM,CAAC;IAE5E;IAEA,wDAAwD;IACxD,IAAI0C,aAAaN,IAAI,KAAKT,OAAOC,IAAI,CAACpB,WAAW,CAAC,GAAG6B,MAAM,EAAE;QAC3D,oEAAoE;QACpE,qEAAqE;QACrE,8BAA8B;QAC9B,MAAMC,aAAaX,OAAOC,IAAI,CAACpB,WAAW,CAAC,GAAG+B,MAAM,CAClD,CAACP,MAAQ,CAACU,aAAaF,GAAG,CAACR;QAG7B,MAAM,IAAIX,MACR,CAAC,mDAAmD,EAAEiB,WAAWtC,IAAI,CACnE,MACA,YAAY,CAAC;IAEnB;IAEA,OAAOW;AACT"}