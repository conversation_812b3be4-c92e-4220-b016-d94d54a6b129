{"version": 3, "sources": ["../../../src/client/components/promise-queue.ts"], "names": ["PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "runningCount", "result", "error", "processNext", "enqueueResult", "queue", "push", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "length", "shift"], "mappings": "AAAA;;;;;AAKA;;IAEE,mFACA,+EACA,iEAmDA;AAtDF,OAAO,MAAMA;IAcXC,QAAWC,SAA2B,EAAc;QAClD,IAAIC;QACJ,IAAIC;QAEJ,MAAMC,cAAc,IAAIC,QAAQ,CAACC,SAASC;YACxCL,cAAcI;YACdH,aAAaI;QACf;QAEA,MAAMC,OAAO;YACX,IAAI;gBACF,gCAAA,IAAI,EAAEC,eAAAA;gBACN,MAAMC,SAAS,MAAMT;gBACrBC,YAAYQ;YACd,EAAE,OAAOC,OAAO;gBACdR,WAAWQ;YACb,SAAU;gBACR,gCAAA,IAAI,EAAEF,eAAAA;gBACN,gCAAA,IAAI,EAAEG,cAAAA;YACR;QACF;QAEA,MAAMC,gBAAgB;YAAEZ,WAAWG;YAAaI;QAAK;QACrD,gDAAgD;QAChD,gCAAA,IAAI,EAAEM,QAAAA,QAAMC,IAAI,CAACF;QACjB,gCAAA,IAAI,EAAED,cAAAA;QAEN,OAAOR;IACT;IAEAY,KAAKf,SAAuB,EAAE;QAC5B,MAAMgB,QAAQ,gCAAA,IAAI,EAAEH,QAAAA,QAAMI,SAAS,CAAC,CAACC,OAASA,KAAKlB,SAAS,KAAKA;QAEjE,IAAIgB,QAAQ,CAAC,GAAG;YACd,MAAMG,aAAa,gCAAA,IAAI,EAAEN,QAAAA,QAAMO,MAAM,CAACJ,OAAO,EAAE,CAAC,EAAE;YAClD,gCAAA,IAAI,EAAEH,QAAAA,QAAMQ,OAAO,CAACF;YACpB,gCAAA,IAAI,EAAER,cAAAA,cAAY;QACpB;IACF;IA5CAW,YAAYC,iBAAiB,CAAC,CAAE;QA8ChC,4BAAA;mBAAA;;QArDA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QAME,gCAAA,IAAI,EAAEA,iBAAAA,mBAAiBA;QACvB,gCAAA,IAAI,EAAEf,eAAAA,iBAAe;QACrB,gCAAA,IAAI,EAAEK,QAAAA,UAAQ,EAAE;IAClB;AAkDF;AARE,SAAA,YAAaW,MAAc;IAAdA,IAAAA,mBAAAA,SAAS;IACpB,IACE,AAAC,CAAA,gCAAA,IAAI,EAAEhB,eAAAA,iBAAe,gCAAA,IAAI,EAAEe,iBAAAA,oBAAkBC,MAAK,KACnD,gCAAA,IAAI,EAAEX,QAAAA,QAAMY,MAAM,GAAG,GACrB;YACA;SAAA,+CAAA,gCAAA,IAAI,EAAEZ,QAAAA,QAAMa,KAAK,uBAAjB,6CAAqBnB,IAAI;IAC3B;AACF"}