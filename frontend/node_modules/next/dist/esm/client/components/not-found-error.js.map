{"version": 3, "sources": ["../../../src/client/components/not-found-error.tsx"], "names": ["React", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "h1", "margin", "padding", "fontSize", "fontWeight", "verticalAlign", "lineHeight", "h2", "NotFound", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,MAAMC,SAA8C;IAClDC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IAEAC,MAAM;QACJJ,SAAS;IACX;IAEAK,IAAI;QACFL,SAAS;QACTM,QAAQ;QACRC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,eAAe;QACfC,YAAY;IACd;IAEAC,IAAI;QACFJ,UAAU;QACVC,YAAY;QACZE,YAAY;QACZL,QAAQ;IACV;AACF;AAEA,eAAe,SAASO;IACtB,qBACE,wDAEE,oBAACC,eAAM,qDAEP,oBAACC;QAAIC,OAAOrB,OAAOC,KAAK;qBACtB,oBAACmB,2BACC,oBAACC;QACCC,yBAAyB;YACvB;;;;;;;;;;;;cAYA,GACAC,QAAS;QACX;sBAEF,oBAACb;QAAGc,WAAU;QAAgBH,OAAOrB,OAAOU,EAAE;OAAE,sBAGhD,oBAACU;QAAIC,OAAOrB,OAAOS,IAAI;qBACrB,oBAACQ;QAAGI,OAAOrB,OAAOiB,EAAE;OAAE;AAMlC"}