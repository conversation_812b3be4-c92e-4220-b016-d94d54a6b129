{"version": 3, "sources": ["../../src/client/request-idle-callback.ts"], "names": ["requestIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "cancelIdleCallback", "id", "clearTimeout"], "mappings": "AAAA,OAAO,MAAMA,sBACX,AAAC,OAAOC,SAAS,eACfA,KAAKD,mBAAmB,IACxBC,KAAKD,mBAAmB,CAACE,IAAI,CAACC,WAChC,SAAUC,EAAuB;IAC/B,IAAIC,QAAQC,KAAKC,GAAG;IACpB,OAAON,KAAKO,UAAU,CAAC;QACrBJ,GAAG;YACDK,YAAY;YACZC,eAAe;gBACb,OAAOC,KAAKC,GAAG,CAAC,GAAG,KAAMN,CAAAA,KAAKC,GAAG,KAAKF,KAAI;YAC5C;QACF;IACF,GAAG;AACL,EAAC;AAEH,OAAO,MAAMQ,qBACX,AAAC,OAAOZ,SAAS,eACfA,KAAKY,kBAAkB,IACvBZ,KAAKY,kBAAkB,CAACX,IAAI,CAACC,WAC/B,SAAUW,EAAU;IAClB,OAAOC,aAAaD;AACtB,EAAC"}