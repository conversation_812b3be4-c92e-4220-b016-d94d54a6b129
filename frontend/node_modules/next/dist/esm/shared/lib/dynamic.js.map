{"version": 3, "sources": ["../../../src/shared/lib/dynamic.tsx"], "names": ["React", "Loadable", "isServerSide", "window", "convertModule", "mod", "default", "noSSR", "LoadableInitializer", "loadableOptions", "webpack", "modules", "Loading", "loading", "error", "isLoading", "past<PERSON>elay", "timedOut", "dynamic", "dynamicOptions", "options", "loadableFn", "process", "env", "NODE_ENV", "p", "message", "br", "stack", "Promise", "loader", "loaderFn", "then", "resolve", "loadableGenerated", "ssr"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,cAAc,4BAA2B;AAEhD,MAAMC,eAAe,OAAOC,WAAW;AA2BvC,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASC,cAAiBC,GAAgD;IACxE,OAAO;QAAEC,SAAS,CAACD,uBAAD,AAACA,IAA4BC,OAAO,KAAID;IAAI;AAChE;AAqBA,OAAO,SAASE,MACdC,mBAAkC,EAClCC,eAAkC;IAElC,yEAAyE;IACzE,OAAOA,gBAAgBC,OAAO;IAC9B,OAAOD,gBAAgBE,OAAO;IAE9B,oFAAoF;IACpF,IAAI,CAACT,cAAc;QACjB,OAAOM,oBAAoBC;IAC7B;IAEA,MAAMG,UAAUH,gBAAgBI,OAAO;IACvC,gDAAgD;IAChD,OAAO,kBACL,oBAACD;YAAQE,OAAO;YAAMC,WAAAA;YAAUC,WAAW;YAAOC,UAAU;;AAEhE;AAEA,eAAe,SAASC,QACtBC,cAA6C,EAC7CC,OAA2B;IAE3B,IAAIC,aAAapB;IAEjB,IAAIQ,kBAAsC;QACxC,wDAAwD;QACxDI,SAAS;gBAAC,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;YACvC,IAAI,CAACA,WAAW,OAAO;YACvB,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIT,WAAW;oBACb,OAAO;gBACT;gBACA,IAAID,OAAO;oBACT,qBACE,oBAACW,WACEX,MAAMY,OAAO,gBACd,oBAACC,aACAb,MAAMc,KAAK;gBAGlB;YACF;YACA,OAAO;QACT;IACF;IAEA,qEAAqE;IACrE,wGAAwG;IACxG,2HAA2H;IAC3H,mEAAmE;IACnE,IAAIT,0BAA0BU,SAAS;QACrCpB,gBAAgBqB,MAAM,GAAG,IAAMX;IAC/B,uFAAuF;IACzF,OAAO,IAAI,OAAOA,mBAAmB,YAAY;QAC/CV,gBAAgBqB,MAAM,GAAGX;IACzB,mGAAmG;IACrG,OAAO,IAAI,OAAOA,mBAAmB,UAAU;QAC7CV,kBAAkB;YAAE,GAAGA,eAAe;YAAE,GAAGU,cAAc;QAAC;IAC5D;IAEA,gHAAgH;IAChHV,kBAAkB;QAAE,GAAGA,eAAe;QAAE,GAAGW,OAAO;IAAC;IAEnD,MAAMW,WAAWtB,gBAAgBqB,MAAM;IACvC,MAAMA,SAAS,IACbC,YAAY,OACRA,WAAWC,IAAI,CAAC5B,iBAChByB,QAAQI,OAAO,CAAC7B,cAAc,IAAM;IAE1C,2DAA2D;IAC3D,IAAIK,gBAAgByB,iBAAiB,EAAE;QACrCzB,kBAAkB;YAChB,GAAGA,eAAe;YAClB,GAAGA,gBAAgByB,iBAAiB;QACtC;QACA,OAAOzB,gBAAgByB,iBAAiB;IAC1C;IAEA,0GAA0G;IAC1G,IAAI,OAAOzB,gBAAgB0B,GAAG,KAAK,aAAa,CAAC1B,gBAAgB0B,GAAG,EAAE;QACpE,OAAO1B,gBAAgBC,OAAO;QAC9B,OAAOD,gBAAgBE,OAAO;QAE9B,OAAOJ,MAAMc,YAAYZ;IAC3B;IAEA,OAAOY,WAAW;QAAE,GAAGZ,eAAe;QAAEqB,QAAQA;IAAoB;AACtE"}