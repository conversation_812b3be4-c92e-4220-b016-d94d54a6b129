{"version": 3, "sources": ["../../../src/shared/lib/head.tsx"], "names": ["React", "useContext", "Effect", "AmpStateContext", "HeadManagerContext", "isInAmpMode", "warnOnce", "defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "Fragment", "concat", "Children", "toArray", "props", "children", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "has", "add", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "filter", "map", "c", "process", "env", "NODE_ENV", "__NEXT_OPTIMIZE_FONTS", "some", "url", "startsWith", "newProps", "undefined", "cloneElement", "srcMessage", "Head", "ampState", "headManager", "reduceComponentsToState"], "mappings": "AAAA;AAEA,OAAOA,SAASC,UAAU,QAAQ,QAAO;AACzC,OAAOC,YAAY,gBAAe;AAClC,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,kBAAkB,QAAQ,wCAAuC;AAC1E,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,QAAQ,QAAQ,oBAAmB;AAM5C,OAAO,SAASC,YAAYC,SAAiB;IAAjBA,IAAAA,sBAAAA,YAAY;IACtC,MAAMC,OAAO;sBAAC,oBAACC;YAAKC,SAAQ;;KAAW;IACvC,IAAI,CAACH,WAAW;QACdC,KAAKG,IAAI,eAAC,oBAACF;YAAKG,MAAK;YAAWC,SAAQ;;IAC1C;IACA,OAAOL;AACT;AAEA,SAASM,iBACPC,IAAoC,EACpCC,KAAuB;IAEvB,8FAA8F;IAC9F,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,UAAU;QAC1D,OAAOD;IACT;IACA,kCAAkC;IAClC,IAAIC,MAAMC,IAAI,KAAKlB,MAAMmB,QAAQ,EAAE;QACjC,OAAOH,KAAKI,MAAM,CAChB,mGAAmG;QACnGpB,MAAMqB,QAAQ,CAACC,OAAO,CAACL,MAAMM,KAAK,CAACC,QAAQ,EAAEC,MAAM,CACjD,mGAAmG;QACnG,CACEC,cACAC;YAEA,IACE,OAAOA,kBAAkB,YACzB,OAAOA,kBAAkB,UACzB;gBACA,OAAOD;YACT;YACA,OAAOA,aAAaN,MAAM,CAACO;QAC7B,GACA,EAAE;IAGR;IACA,OAAOX,KAAKI,MAAM,CAACH;AACrB;AAEA,MAAMW,YAAY;IAAC;IAAQ;IAAa;IAAW;CAAW;AAE9D;;;;AAIA,GACA,SAASC;IACP,MAAMC,OAAO,IAAIC;IACjB,MAAMC,OAAO,IAAID;IACjB,MAAME,YAAY,IAAIF;IACtB,MAAMG,iBAAsD,CAAC;IAE7D,OAAO,CAACC;QACN,IAAIC,WAAW;QACf,IAAIC,SAAS;QAEb,IAAIF,EAAEG,GAAG,IAAI,OAAOH,EAAEG,GAAG,KAAK,YAAYH,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO,GAAG;YAChEF,SAAS;YACT,MAAMC,MAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO;YAC7C,IAAIT,KAAKW,GAAG,CAACH,MAAM;gBACjBF,WAAW;YACb,OAAO;gBACLN,KAAKY,GAAG,CAACJ;YACX;QACF;QAEA,wCAAwC;QACxC,OAAQH,EAAEjB,IAAI;YACZ,KAAK;YACL,KAAK;gBACH,IAAIc,KAAKS,GAAG,CAACN,EAAEjB,IAAI,GAAG;oBACpBkB,WAAW;gBACb,OAAO;oBACLJ,KAAKU,GAAG,CAACP,EAAEjB,IAAI;gBACjB;gBACA;YACF,KAAK;gBACH,IAAK,IAAIyB,IAAI,GAAGC,MAAMhB,UAAUiB,MAAM,EAAEF,IAAIC,KAAKD,IAAK;oBACpD,MAAMG,WAAWlB,SAAS,CAACe,EAAE;oBAC7B,IAAI,CAACR,EAAEZ,KAAK,CAACwB,cAAc,CAACD,WAAW;oBAEvC,IAAIA,aAAa,WAAW;wBAC1B,IAAIb,UAAUQ,GAAG,CAACK,WAAW;4BAC3BV,WAAW;wBACb,OAAO;4BACLH,UAAUS,GAAG,CAACI;wBAChB;oBACF,OAAO;wBACL,MAAME,WAAWb,EAAEZ,KAAK,CAACuB,SAAS;wBAClC,MAAMG,aAAaf,cAAc,CAACY,SAAS,IAAI,IAAIf;wBACnD,IAAI,AAACe,CAAAA,aAAa,UAAU,CAACT,MAAK,KAAMY,WAAWR,GAAG,CAACO,WAAW;4BAChEZ,WAAW;wBACb,OAAO;4BACLa,WAAWP,GAAG,CAACM;4BACfd,cAAc,CAACY,SAAS,GAAGG;wBAC7B;oBACF;gBACF;gBACA;QACJ;QAEA,OAAOb;IACT;AACF;AAEA;;;CAGC,GACD,SAASc,iBACPC,oBAAoD,EACpD5B,KAAQ;IAER,MAAM,EAAEf,SAAS,EAAE,GAAGe;IACtB,OAAO4B,qBACJ1B,MAAM,CAACV,kBAAkB,EAAE,EAC3BqC,OAAO,GACPhC,MAAM,CAACb,YAAYC,WAAW4C,OAAO,IACrCC,MAAM,CAACxB,UACPuB,OAAO,GACPE,GAAG,CAAC,CAACC,GAA4BZ;QAChC,MAAML,MAAMiB,EAAEjB,GAAG,IAAIK;QACrB,IACEa,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBF,QAAQC,GAAG,CAACE,qBAAqB,IACjC,CAACnD,WACD;YACA,IACE+C,EAAErC,IAAI,KAAK,UACXqC,EAAEhC,KAAK,CAAC,OAAO,IACf,0FAA0F;YAC1F;gBAAC;gBAAoC;aAA2B,CAACqC,IAAI,CACnE,CAACC,MAAQN,EAAEhC,KAAK,CAAC,OAAO,CAACuC,UAAU,CAACD,OAEtC;gBACA,MAAME,WAAW;oBAAE,GAAIR,EAAEhC,KAAK,IAAI,CAAC,CAAC;gBAAE;gBACtCwC,QAAQ,CAAC,YAAY,GAAGA,QAAQ,CAAC,OAAO;gBACxCA,QAAQ,CAAC,OAAO,GAAGC;gBAEnB,gEAAgE;gBAChED,QAAQ,CAAC,uBAAuB,GAAG;gBAEnC,qBAAO/D,MAAMiE,YAAY,CAACV,GAAGQ;YAC/B;QACF;QACA,IAAIP,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,yDAAyD;YACzD,IAAIH,EAAErC,IAAI,KAAK,YAAYqC,EAAEhC,KAAK,CAAC,OAAO,KAAK,uBAAuB;gBACpE,MAAM2C,aAAaX,EAAEhC,KAAK,CAAC,MAAM,GAC7B,AAAC,4BAAyBgC,EAAEhC,KAAK,CAAC,MAAM,GAAC,MACxC;gBACLjB,SACE,AAAC,mDAAgD4D,aAAW;YAEhE,OAAO,IAAIX,EAAErC,IAAI,KAAK,UAAUqC,EAAEhC,KAAK,CAAC,MAAM,KAAK,cAAc;gBAC/DjB,SACE,AAAC,wFAAqFiD,EAAEhC,KAAK,CAAC,OAAO,GAAC;YAE1G;QACF;QACA,qBAAOvB,MAAMiE,YAAY,CAACV,GAAG;YAAEjB;QAAI;IACrC;AACJ;AAEA;;;CAGC,GACD,SAAS6B,KAAK,KAA2C;IAA3C,IAAA,EAAE3C,QAAQ,EAAiC,GAA3C;IACZ,MAAM4C,WAAWnE,WAAWE;IAC5B,MAAMkE,cAAcpE,WAAWG;IAC/B,qBACE,oBAACF;QACCoE,yBAAyBpB;QACzBmB,aAAaA;QACb7D,WAAWH,YAAY+D;OAEtB5C;AAGP;AAEA,eAAe2C,KAAI"}