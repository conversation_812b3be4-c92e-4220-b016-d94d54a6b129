{"version": 3, "sources": ["../../src/server/server-utils.ts"], "names": ["normalizeVercelUrl", "interpolateDynamicPath", "getUtils", "req", "trustQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageIsDynamic", "defaultRouteRegex", "_parsedUrl", "parseUrl", "url", "search", "key", "Object", "keys", "query", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "groups", "includes", "formatUrl", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramIdx", "indexOf", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "slice", "length", "page", "i18n", "basePath", "rewrites", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "defaultRouteMatches", "getNamedRouteRegex", "getRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "removeTrailingSlash", "checkRewrite", "rewrite", "matcher", "getPathMatch", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "matchHas", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareDestination", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "destLocalePathResult", "normalizeLocalePath", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "renderOpts", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "matchesHasLocale", "normalizedKey", "substring", "routeKeyNames", "filterLocaleItem", "val", "isCatchAll", "_val", "some", "item", "toLowerCase", "locale", "splice", "every", "name", "reduce", "prev", "keyName", "paramName", "pos", "parseInt", "headers", "normalizeDynamicRouteParams", "ignoreOptional", "hasValidParams", "normalizeRscURL", "defaultValue", "isOptional", "isDefaultValue", "defaultVal", "undefined", "split"], "mappings": ";;;;;;;;;;;;;;;;IAoBgBA,kBAAkB;eAAlBA;;IA0BAC,sBAAsB;eAAtBA;;IAuCAC,QAAQ;eAARA;;;qBA9EuC;qCACnB;2BACP;4BACM;8BACH;oCAIzB;qCAC6B;0BACJ;2BACQ;AAEjC,SAASF,mBACdG,GAAoB,EACpBC,UAAmB,EACnBC,SAAoB,EACpBC,aAAuB,EACvBC,iBAAqE;IAErE,mEAAmE;IACnE,gDAAgD;IAChD,IAAID,iBAAiBF,cAAcG,mBAAmB;QACpD,MAAMC,aAAaC,IAAAA,UAAQ,EAACN,IAAIO,GAAG,EAAG;QACtC,OAAO,AAACF,WAAmBG,MAAM;QAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACN,WAAWO,KAAK,EAAG;YAC/C,IACE,AAACH,QAAQI,kCAAuB,IAC9BJ,IAAIK,UAAU,CAACD,kCAAuB,KACxC,AAACX,CAAAA,aAAaQ,OAAOC,IAAI,CAACP,kBAAkBW,MAAM,CAAA,EAAGC,QAAQ,CAACP,MAC9D;gBACA,OAAOJ,WAAWO,KAAK,CAACH,IAAI;YAC9B;QACF;QACAT,IAAIO,GAAG,GAAGU,IAAAA,WAAS,EAACZ;IACtB;AACF;AAEO,SAASP,uBACdoB,QAAgB,EAChBC,MAAsB,EACtBf,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOc;IAE/B,KAAK,MAAME,SAASV,OAAOC,IAAI,CAACP,kBAAkBW,MAAM,EAAG;QACzD,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGlB,kBAAkBW,MAAM,CAACK,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,GAAG,EAAEF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,MAAMC,WAAWN,SAAUO,OAAO,CAACF;QAEnC,IAAIC,WAAW,CAAC,GAAG;YACjB,IAAIE;YACJ,MAAMC,QAAQR,MAAM,CAACC,MAAM;YAE3B,IAAIQ,MAAMC,OAAO,CAACF,QAAQ;gBACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;YACjE,OAAO,IAAIN,OAAO;gBAChBD,aAAaM,mBAAmBL;YAClC,OAAO;gBACLD,aAAa;YACf;YAEAR,WACEA,SAASgB,KAAK,CAAC,GAAGV,YAClBE,aACAR,SAASgB,KAAK,CAACV,WAAWD,WAAWY,MAAM;QAC/C;IACF;IAEA,OAAOjB;AACT;AAEO,SAASnB,SAAS,EACvBqC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRpC,aAAa,EACbqC,aAAa,EACbC,aAAa,EAad;IACC,IAAIrC;IACJ,IAAIsC;IACJ,IAAIC;IAEJ,IAAIxC,eAAe;QACjBC,oBAAoBwC,IAAAA,8BAAkB,EAACR,MAAM;QAC7CM,sBAAsBG,IAAAA,6BAAe,EAACzC;QACtCuC,sBAAsBD,oBAAoBN;IAC5C;IAEA,SAASU,eAAe9C,GAAoB,EAAE+C,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAU7B,QAAQ;QAEnC,MAAMgC,cAAc;YAClB,MAAMC,oBAAoBC,IAAAA,wCAAmB,EAACH,cAAc;YAC5D,OACEE,sBAAsBC,IAAAA,wCAAmB,EAAChB,UAC1CM,uCAAAA,oBAAsBS;QAE1B;QAEA,MAAME,eAAe,CAACC;YACpB,MAAMC,UAAUC,IAAAA,uBAAY,EAC1BF,QAAQG,MAAM,GAAIjB,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEkB,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACnB;YACf;YAEF,IAAItB,SAASoC,QAAQR,UAAU7B,QAAQ;YAEvC,IAAI,AAACoC,CAAAA,QAAQO,GAAG,IAAIP,QAAQQ,OAAO,AAAD,KAAM3C,QAAQ;gBAC9C,MAAM4C,YAAYC,IAAAA,4BAAQ,EACxBhE,KACA+C,UAAUnC,KAAK,EACf0C,QAAQO,GAAG,EACXP,QAAQQ,OAAO;gBAGjB,IAAIC,WAAW;oBACbrD,OAAOuD,MAAM,CAAC9C,QAAQ4C;gBACxB,OAAO;oBACL5C,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAE+C,iBAAiB,EAAEC,SAAS,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;oBAC1DC,qBAAqB;oBACrBC,aAAahB,QAAQgB,WAAW;oBAChCnD,QAAQA;oBACRP,OAAOmC,UAAUnC,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIsD,kBAAkBK,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEA7D,OAAOuD,MAAM,CAACjB,eAAemB,WAAWhD;gBACxCT,OAAOuD,MAAM,CAAClB,UAAUnC,KAAK,EAAEsD,kBAAkBtD,KAAK;gBACtD,OAAO,AAACsD,kBAA0BtD,KAAK;gBAEvCF,OAAOuD,MAAM,CAAClB,WAAWmB;gBAEzBjB,aAAaF,UAAU7B,QAAQ;gBAE/B,IAAIoB,UAAU;oBACZW,aACEA,WAAYuB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEnC,SAAS,CAAC,GAAG,OAAO;gBAC3D;gBAEA,IAAID,MAAM;oBACR,MAAMqC,uBAAuBC,IAAAA,wCAAmB,EAC9C1B,YACAZ,KAAKuC,OAAO;oBAEd3B,aAAayB,qBAAqBxD,QAAQ;oBAC1C6B,UAAUnC,KAAK,CAACiE,kBAAkB,GAChCH,qBAAqBI,cAAc,IAAI3D,OAAO0D,kBAAkB;gBACpE;gBAEA,IAAI5B,eAAeb,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAIjC,iBAAiBuC,qBAAqB;oBACxC,MAAMqC,gBAAgBrC,oBAAoBO;oBAC1C,IAAI8B,eAAe;wBACjBhC,UAAUnC,KAAK,GAAG;4BAChB,GAAGmC,UAAUnC,KAAK;4BAClB,GAAGmE,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMzB,WAAWf,SAASyC,WAAW,IAAI,EAAE,CAAE;YAChD3B,aAAaC;QACf;QAEA,IAAIL,eAAeb,MAAM;YACvB,IAAI6C,WAAW;YAEf,KAAK,MAAM3B,WAAWf,SAAS2C,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAW5B,aAAaC;gBACxB,IAAI2B,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC/B,eAAe;gBAC/B,KAAK,MAAMI,WAAWf,SAAS4C,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAW5B,aAAaC;oBACxB,IAAI2B,UAAU;gBAChB;YACF;QACF;QACA,OAAOjC;IACT;IAEA,SAASoC,0BACPpF,GAAoB,EACpBqF,UAAgB,EAChBP,cAAuB;QAEvB,OAAOjC,IAAAA,6BAAe,EACpB,AAAC;YACC,MAAM,EAAE9B,MAAM,EAAEuE,SAAS,EAAE,GAAGlF;YAE9B,OAAO;gBACLmF,IAAI;oBACF,qDAAqD;oBACrDC,MAAM,CAACC;wBACL,MAAMC,MAAMhF,OAAOiF,WAAW,CAAC,IAAIC,gBAAgBH;wBACnD,MAAMI,mBACJxD,QAAQyC,kBAAkBY,GAAG,CAAC,IAAI,KAAKZ;wBAEzC,KAAK,MAAMrE,OAAOC,OAAOC,IAAI,CAAC+E,KAAM;4BAClC,MAAM/D,QAAQ+D,GAAG,CAACjF,IAAI;4BAEtB,IACEA,QAAQI,kCAAuB,IAC/BJ,IAAIK,UAAU,CAACD,kCAAuB,GACtC;gCACA,MAAMiF,gBAAgBrF,IAAIsF,SAAS,CACjClF,kCAAuB,CAACsB,MAAM;gCAEhCuD,GAAG,CAACI,cAAc,GAAGnE;gCACrB,OAAO+D,GAAG,CAACjF,IAAI;4BACjB;wBACF;wBAEA,mCAAmC;wBACnC,MAAMuF,gBAAgBtF,OAAOC,IAAI,CAAC2E,aAAa,CAAC;wBAChD,MAAMW,mBAAmB,CAACC;4BACxB,IAAI7D,MAAM;gCACR,gDAAgD;gCAChD,4CAA4C;gCAC5C,WAAW;gCACX,MAAM8D,aAAavE,MAAMC,OAAO,CAACqE;gCACjC,MAAME,OAAOD,aAAaD,GAAG,CAAC,EAAE,GAAGA;gCAEnC,IACE,OAAOE,SAAS,YAChB/D,KAAKuC,OAAO,CAACyB,IAAI,CAAC,CAACC;oCACjB,IAAIA,KAAKC,WAAW,OAAOH,KAAKG,WAAW,IAAI;wCAC7CzB,iBAAiBwB;wCACjBjB,WAAWmB,MAAM,GAAG1B;wCACpB,OAAO;oCACT;oCACA,OAAO;gCACT,IACA;oCACA,wCAAwC;oCACxC,IAAIqB,YAAY;wCACZD,IAAiBO,MAAM,CAAC,GAAG;oCAC/B;oCAEA,sCAAsC;oCACtC,qBAAqB;oCACrB,OAAON,aAAaD,IAAI/D,MAAM,KAAK,IAAI;gCACzC;4BACF;4BACA,OAAO;wBACT;wBAEA,IAAI6D,cAAcU,KAAK,CAAC,CAACC,OAASjB,GAAG,CAACiB,KAAK,GAAG;4BAC5C,OAAOX,cAAcY,MAAM,CAAC,CAACC,MAAMC;gCACjC,MAAMC,YAAYzB,6BAAAA,SAAW,CAACwB,QAAQ;gCAEtC,IAAIC,aAAa,CAACd,iBAAiBP,GAAG,CAACoB,QAAQ,GAAG;oCAChDD,IAAI,CAAC9F,MAAM,CAACgG,UAAU,CAACC,GAAG,CAAC,GAAGtB,GAAG,CAACoB,QAAQ;gCAC5C;gCACA,OAAOD;4BACT,GAAG,CAAC;wBACN;wBAEA,OAAOnG,OAAOC,IAAI,CAAC+E,KAAKkB,MAAM,CAAC,CAACC,MAAMpG;4BACpC,IAAI,CAACwF,iBAAiBP,GAAG,CAACjF,IAAI,GAAG;gCAC/B,IAAIqF,gBAAgBrF;gCAEpB,IAAIoF,kBAAkB;oCACpBC,gBAAgBmB,SAASxG,KAAK,MAAM,IAAI;gCAC1C;gCACA,OAAOC,OAAOuD,MAAM,CAAC4C,MAAM;oCACzB,CAACf,cAAc,EAAEJ,GAAG,CAACjF,IAAI;gCAC3B;4BACF;4BACA,OAAOoG;wBACT,GAAG,CAAC;oBACN;gBACF;gBACA9F;YACF;QACF,KACAf,IAAIkH,OAAO,CAAC,sBAAsB;IACtC;IAEA,SAASC,4BACPhG,MAAsB,EACtBiG,cAAwB;QAExB,IAAIC,iBAAiB;QACrB,IAAI,CAACjH,mBAAmB,OAAO;YAAEe;YAAQkG,gBAAgB;QAAM;QAE/DlG,SAAST,OAAOC,IAAI,CAACP,kBAAkBW,MAAM,EAAE6F,MAAM,CAAC,CAACC,MAAMpG;YAC3D,IAAIkB,QAAuCR,MAAM,CAACV,IAAI;YAEtD,IAAI,OAAOkB,UAAU,UAAU;gBAC7BA,QAAQ2F,IAAAA,yBAAe,EAAC3F;YAC1B;YACA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;gBACxBA,QAAQA,MAAMG,GAAG,CAAC,CAACoE;oBACjB,IAAI,OAAOA,QAAQ,UAAU;wBAC3BA,MAAMoB,IAAAA,yBAAe,EAACpB;oBACxB;oBACA,OAAOA;gBACT;YACF;YAEA,uDAAuD;YACvD,0DAA0D;YAC1D,sCAAsC;YACtC,MAAMqB,eAAe5E,mBAAoB,CAAClC,IAAI;YAC9C,MAAM+G,aAAapH,kBAAmBW,MAAM,CAACN,IAAI,CAACY,QAAQ;YAE1D,MAAMoG,iBAAiB7F,MAAMC,OAAO,CAAC0F,gBACjCA,aAAalB,IAAI,CAAC,CAACqB;gBACjB,OAAO9F,MAAMC,OAAO,CAACF,SACjBA,MAAM0E,IAAI,CAAC,CAACH,MAAQA,IAAIlF,QAAQ,CAAC0G,eACjC/F,yBAAAA,MAAOX,QAAQ,CAAC0G;YACtB,KACA/F,yBAAAA,MAAOX,QAAQ,CAACuG;YAEpB,IACEE,kBACC,OAAO9F,UAAU,eAAe,CAAE6F,CAAAA,cAAcJ,cAAa,GAC9D;gBACAC,iBAAiB;YACnB;YAEA,gEAAgE;YAChE,oBAAoB;YACpB,IACEG,cACC,CAAA,CAAC7F,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMQ,MAAM,KAAK,KACjB,6CAA6C;YAC7C,+CAA+C;YAC9CR,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAElB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;gBACAkB,QAAQgG;gBACR,OAAOxG,MAAM,CAACV,IAAI;YACpB;YAEA,+DAA+D;YAC/D,6CAA6C;YAC7C,IACEkB,SACA,OAAOA,UAAU,YACjBvB,kBAAmBW,MAAM,CAACN,IAAI,CAACa,MAAM,EACrC;gBACAK,QAAQA,MAAMiG,KAAK,CAAC;YACtB;YAEA,IAAIjG,OAAO;gBACTkF,IAAI,CAACpG,IAAI,GAAGkB;YACd;YACA,OAAOkF;QACT,GAAG,CAAC;QAEJ,OAAO;YACL1F;YACAkG;QACF;IACF;IAEA,OAAO;QACLvE;QACA1C;QACAsC;QACAC;QACAyC;QACA+B;QACAtH,oBAAoB,CAClBG,KACAC,YACAC,YAEAL,mBACEG,KACAC,YACAC,WACAC,eACAC;QAEJN,wBAAwB,CACtBoB,UACAC,SACGrB,uBAAuBoB,UAAUC,QAAQf;IAChD;AACF"}