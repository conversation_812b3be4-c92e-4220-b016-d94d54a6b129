{"version": 3, "sources": ["../../../src/shared/lib/get-img-props.ts"], "names": ["getImgProps", "VALID_LOADING_VALUES", "undefined", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "push", "length", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "Set", "map", "w", "find", "p", "generateImgAttrs", "config", "unoptimized", "quality", "loader", "srcSet", "last", "i", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "c", "imageConfigDefault", "imageSizes", "sort", "a", "b", "isDefaultLoader", "Error", "customImageLoader", "obj", "_", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "endsWith", "dangerouslyAllowSVG", "qualityInt", "process", "env", "NODE_ENV", "output", "position", "isNaN", "includes", "String", "warnOnce", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "pathname", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "window", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "type", "buffered", "console", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "getImageBlurSvg", "placeholder<PERSON><PERSON><PERSON>", "backgroundSize", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "props", "decoding", "meta"], "mappings": ";;;;+BA0OgBA;;;eAAAA;;;0BA1OS;8BACO;6BACG;AA4EnC,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAkBzD,SAASC,gBACPC,GAAoC;IAEpC,OAAO,AAACA,IAAsBC,OAAO,KAAKH;AAC5C;AAEA,SAASI,kBACPF,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKF;AAC1C;AAEA,SAASK,eAAeH,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACdD,CAAAA,gBAAgBC,QACfE,kBAAkBF,IAAmB;AAE3C;AAEA,MAAMI,UAAU,IAAIC;AAIpB,IAAIC;AAEJ,SAASC,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,aAAa;QAC5B,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOC,OAAOC,QAAQ,CAACF,KAAKA,IAAIG;IAClC;IACA,IAAI,OAAOH,MAAM,YAAY,WAAWI,IAAI,CAACJ,IAAI;QAC/C,OAAOK,SAASL,GAAG;IACrB;IACA,OAAOG;AACT;AAEA,SAASG,UACP,KAAsC,EACtCC,KAAyB,EACzBC,KAAyB;IAFzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAIA,IAAIF,OAAO;QACT,yDAAyD;QACzD,MAAMG,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAaG,IAAI,CAACV,SAASQ,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAaI,MAAM,EAAE;YACvB,MAAMC,gBAAgBC,KAAKC,GAAG,IAAIP,gBAAgB;YAClD,OAAO;gBACLQ,QAAQV,SAASW,MAAM,CAAC,CAACC,IAAMA,KAAKb,WAAW,CAAC,EAAE,GAAGQ;gBACrDM,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQV;YAAUa,MAAM;QAAI;IACvC;IACA,IAAI,OAAOhB,UAAU,UAAU;QAC7B,OAAO;YAAEa,QAAQX;YAAac,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAII,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACjB;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACkB,GAAG,CACpC,CAACC,IAAMhB,SAASiB,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMhB,QAAQ,CAACA,SAASM,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAEI;QAAQG,MAAM;IAAI;AAC7B;AAkBA,SAASM,iBAAiB,KAQR;IARQ,IAAA,EACxBC,MAAM,EACNtC,GAAG,EACHuC,WAAW,EACXxB,KAAK,EACLyB,OAAO,EACPxB,KAAK,EACLyB,MAAM,EACU,GARQ;IASxB,IAAIF,aAAa;QACf,OAAO;YAAEvC;YAAK0C,QAAQ5C;YAAWkB,OAAOlB;QAAU;IACpD;IAEA,MAAM,EAAE8B,MAAM,EAAEG,IAAI,EAAE,GAAGjB,UAAUwB,QAAQvB,OAAOC;IAClD,MAAM2B,OAAOf,OAAOJ,MAAM,GAAG;IAE7B,OAAO;QACLR,OAAO,CAACA,SAASe,SAAS,MAAM,UAAUf;QAC1C0B,QAAQd,OACLK,GAAG,CACF,CAACC,GAAGU,IACF,AAAGH,OAAO;gBAAEH;gBAAQtC;gBAAKwC;gBAASzB,OAAOmB;YAAE,KAAG,MAC5CH,CAAAA,SAAS,MAAMG,IAAIU,IAAI,CAAA,IACtBb,MAENc,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD7C,KAAKyC,OAAO;YAAEH;YAAQtC;YAAKwC;YAASzB,OAAOa,MAAM,CAACe,KAAK;QAAC;IAC1D;AACF;AAKO,SAAS/C,YACd,KAuBa,EACbkD,MAKC;IA7BD,IAAA,EACE9C,GAAG,EACHgB,KAAK,EACLuB,cAAc,KAAK,EACnBQ,WAAW,KAAK,EAChBC,OAAO,EACPC,SAAS,EACTT,OAAO,EACPzB,KAAK,EACLmC,MAAM,EACNC,OAAO,KAAK,EACZC,KAAK,EACLC,MAAM,EACNC,iBAAiB,EACjBC,cAAc,OAAO,EACrBC,WAAW,EACXC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACR,GAAGC,MACQ,GAvBb;IAuCA,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGrB;IAC9D,IAAIR;IACJ,IAAI8B,IAAIJ,WAAWK,+BAAkB;IACrC,IAAI,cAAcD,GAAG;QACnB9B,SAAS8B;IACX,OAAO;QACL,MAAMlD,WAAW;eAAIkD,EAAEnD,WAAW;eAAKmD,EAAEE,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMxD,cAAcmD,EAAEnD,WAAW,CAACsD,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrDnC,SAAS;YAAE,GAAG8B,CAAC;YAAElD;YAAUD;QAAY;IACzC;IAEA,IAAIwB,SAAgCsB,KAAKtB,MAAM,IAAI0B;IAEnD,sDAAsD;IACtD,OAAOJ,KAAKtB,MAAM;IAClB,OAAO,AAACsB,KAAarB,MAAM;IAE3B,6CAA6C;IAC7C,oDAAoD;IACpD,MAAMgC,kBAAkB,wBAAwBjC;IAEhD,IAAIiC,iBAAiB;QACnB,IAAIpC,OAAOG,MAAM,KAAK,UAAU;YAC9B,MAAM,IAAIkC,MACR,AAAC,qBAAkB3E,MAAI,gCACpB;QAEP;IACF,OAAO;QACL,8CAA8C;QAC9C,+CAA+C;QAC/C,iDAAiD;QACjD,MAAM4E,oBAAoBnC;QAC1BA,SAAS,CAACoC;YACR,MAAM,EAAEvC,QAAQwC,CAAC,EAAE,GAAGC,MAAM,GAAGF;YAC/B,OAAOD,kBAAkBG;QAC3B;IACF;IAEA,IAAIrB,QAAQ;QACV,IAAIA,WAAW,QAAQ;YACrBP,OAAO;QACT;QACA,MAAM6B,gBAAoE;YACxEC,WAAW;gBAAEC,UAAU;gBAAQhC,QAAQ;YAAO;YAC9CiC,YAAY;gBAAEpE,OAAO;gBAAQmC,QAAQ;YAAO;QAC9C;QACA,MAAMkC,gBAAoD;YACxDD,YAAY;YACZhC,MAAM;QACR;QACA,MAAMkC,cAAcL,aAAa,CAACtB,OAAO;QACzC,IAAI2B,aAAa;YACfjC,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGiC,WAAW;YAAC;QACrC;QACA,MAAMC,cAAcF,aAAa,CAAC1B,OAAO;QACzC,IAAI4B,eAAe,CAACtE,OAAO;YACzBA,QAAQsE;QACV;IACF;IAEA,IAAIC,YAAY;IAChB,IAAIC,WAAWjF,OAAOQ;IACtB,IAAI0E,YAAYlF,OAAO2C;IACvB,IAAIwC;IACJ,IAAIC;IACJ,IAAIxF,eAAeH,MAAM;QACvB,MAAM4F,kBAAkB7F,gBAAgBC,OAAOA,IAAIC,OAAO,GAAGD;QAE7D,IAAI,CAAC4F,gBAAgB5F,GAAG,EAAE;YACxB,MAAM,IAAI2E,MACR,AAAC,gJAA6IkB,KAAKC,SAAS,CAC1JF;QAGN;QACA,IAAI,CAACA,gBAAgB1C,MAAM,IAAI,CAAC0C,gBAAgB7E,KAAK,EAAE;YACrD,MAAM,IAAI4D,MACR,AAAC,6JAA0JkB,KAAKC,SAAS,CACvKF;QAGN;QAEAF,YAAYE,gBAAgBF,SAAS;QACrCC,aAAaC,gBAAgBD,UAAU;QACvCnC,cAAcA,eAAeoC,gBAAgBpC,WAAW;QACxD+B,YAAYK,gBAAgB5F,GAAG;QAE/B,IAAI,CAACmD,MAAM;YACT,IAAI,CAACqC,YAAY,CAACC,WAAW;gBAC3BD,WAAWI,gBAAgB7E,KAAK;gBAChC0E,YAAYG,gBAAgB1C,MAAM;YACpC,OAAO,IAAIsC,YAAY,CAACC,WAAW;gBACjC,MAAMM,QAAQP,WAAWI,gBAAgB7E,KAAK;gBAC9C0E,YAAY/D,KAAKsE,KAAK,CAACJ,gBAAgB1C,MAAM,GAAG6C;YAClD,OAAO,IAAI,CAACP,YAAYC,WAAW;gBACjC,MAAMM,QAAQN,YAAYG,gBAAgB1C,MAAM;gBAChDsC,WAAW9D,KAAKsE,KAAK,CAACJ,gBAAgB7E,KAAK,GAAGgF;YAChD;QACF;IACF;IACA/F,MAAM,OAAOA,QAAQ,WAAWA,MAAMuF;IAEtC,IAAIU,SACF,CAAClD,YAAaC,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI,CAAChD,OAAOA,IAAIkG,UAAU,CAAC,YAAYlG,IAAIkG,UAAU,CAAC,UAAU;QAC9D,uEAAuE;QACvE3D,cAAc;QACd0D,SAAS;IACX;IACA,IAAI3D,OAAOC,WAAW,EAAE;QACtBA,cAAc;IAChB;IACA,IAAImC,mBAAmB1E,IAAImG,QAAQ,CAAC,WAAW,CAAC7D,OAAO8D,mBAAmB,EAAE;QAC1E,yDAAyD;QACzD,+CAA+C;QAC/C7D,cAAc;IAChB;IACA,IAAIQ,UAAU;QACZU,gBAAgB;IAClB;IAEA,MAAM4C,aAAa9F,OAAOiC;IAE1B,IAAI8D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIlE,OAAOmE,MAAM,KAAK,YAAY/B,mBAAmB,CAACnC,aAAa;YACjE,MAAM,IAAIoC,MACP;QAML;QACA,IAAI,CAAC3E,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CuC,cAAc;QAChB,OAAO;YACL,IAAIY,MAAM;gBACR,IAAIpC,OAAO;oBACT,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIkD,QAAQ;oBACV,MAAM,IAAIyB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOsD,QAAQ,KAAItD,MAAMsD,QAAQ,KAAK,YAAY;oBACpD,MAAM,IAAI/B,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOrC,KAAK,KAAIqC,MAAMrC,KAAK,KAAK,QAAQ;oBAC1C,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOF,MAAM,KAAIE,MAAMF,MAAM,KAAK,QAAQ;oBAC5C,MAAM,IAAIyB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;YACF,OAAO;gBACL,IAAI,OAAOwF,aAAa,aAAa;oBACnC,MAAM,IAAIb,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI2G,MAAMnB,WAAW;oBAC1B,MAAM,IAAIb,MACR,AAAC,qBAAkB3E,MAAI,sFAAmFe,QAAM;gBAEpH;gBACA,IAAI,OAAO0E,cAAc,aAAa;oBACpC,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI2G,MAAMlB,YAAY;oBAC3B,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI,uFAAoFkD,SAAO;gBAEtH;YACF;QACF;QACA,IAAI,CAACrD,qBAAqB+G,QAAQ,CAAC5D,UAAU;YAC3C,MAAM,IAAI2B,MACR,AAAC,qBAAkB3E,MAAI,iDAA8CgD,UAAQ,wBAAqBnD,qBAAqBoC,GAAG,CACxH4E,QACAhE,IAAI,CAAC,OAAK;QAEhB;QACA,IAAIE,YAAYC,YAAY,QAAQ;YAClC,MAAM,IAAI2B,MACR,AAAC,qBAAkB3E,MAAI;QAE3B;QACA,IACEuD,gBAAgB,WAChBA,gBAAgB,UAChB,CAACA,YAAY2C,UAAU,CAAC,gBACxB;YACA,MAAM,IAAIvB,MACR,AAAC,qBAAkB3E,MAAI,2CAAwCuD,cAAY;QAE/E;QACA,IAAIA,gBAAgB,SAAS;YAC3B,IAAIiC,YAAYC,aAAaD,WAAWC,YAAY,MAAM;gBACxDqB,IAAAA,kBAAQ,EACN,AAAC,qBAAkB9G,MAAI;YAE3B;QACF;QACA,IAAIuD,gBAAgB,UAAU,CAACC,aAAa;YAC1C,MAAMuD,iBAAiB;gBAAC;gBAAQ;gBAAO;gBAAQ;aAAO,CAAC,iCAAiC;;YAExF,MAAM,IAAIpC,MACR,AAAC,qBAAkB3E,MAAI,6TAGkE+G,eAAelE,IAAI,CACxG,OACA;QAIR;QACA,IAAI,SAASkB,MAAM;YACjB+C,IAAAA,kBAAQ,EACN,AAAC,qBAAkB9G,MAAI;QAE3B;QAEA,IAAI,CAACuC,eAAe,CAACmC,iBAAiB;YACpC,MAAMsC,SAASvE,OAAO;gBACpBH;gBACAtC;gBACAe,OAAOyE,YAAY;gBACnBhD,SAAS6D,cAAc;YACzB;YACA,IAAIY;YACJ,IAAI;gBACFA,MAAM,IAAIC,IAAIF;YAChB,EAAE,OAAOG,KAAK,CAAC;YACf,IAAIH,WAAWhH,OAAQiH,OAAOA,IAAIG,QAAQ,KAAKpH,OAAO,CAACiH,IAAII,MAAM,EAAG;gBAClEP,IAAAA,kBAAQ,EACN,AAAC,qBAAkB9G,MAAI,4HACpB;YAEP;QACF;QAEA,IAAIsD,mBAAmB;YACrBwD,IAAAA,kBAAQ,EACN,AAAC,qBAAkB9G,MAAI;QAE3B;QAEA,KAAK,MAAM,CAACsH,WAAWC,YAAY,IAAIC,OAAOC,OAAO,CAAC;YACpD/D;YACAC;YACAC;YACAC;YACAC;QACF,GAAI;YACF,IAAIyD,aAAa;gBACfT,IAAAA,kBAAQ,EACN,AAAC,qBAAkB9G,MAAI,wBAAqBsH,YAAU,0CACnD;YAEP;QACF;QAEA,IACE,OAAOI,WAAW,eAClB,CAACpH,gBACDoH,OAAOC,mBAAmB,EAC1B;YACArH,eAAe,IAAIqH,oBAAoB,CAACC;gBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;wBAE3BD;oBADf,0EAA0E;oBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgB7H,GAAG,KAAI;oBACtC,MAAMiI,WAAW7H,QAAQ8H,GAAG,CAACH;oBAC7B,IACEE,YACA,CAACA,SAASlF,QAAQ,IAClBkF,SAAS1E,WAAW,KAAK,WACzB,CAAC0E,SAASjI,GAAG,CAACkG,UAAU,CAAC,YACzB,CAAC+B,SAASjI,GAAG,CAACkG,UAAU,CAAC,UACzB;wBACA,iDAAiD;wBACjDY,IAAAA,kBAAQ,EACN,AAAC,qBAAkBmB,SAASjI,GAAG,GAAC,8HAC7B;oBAEP;gBACF;YACF;YACA,IAAI;gBACFM,aAAa6H,OAAO,CAAC;oBACnBC,MAAM;oBACNC,UAAU;gBACZ;YACF,EAAE,OAAOlB,KAAK;gBACZ,oCAAoC;gBACpCmB,QAAQC,KAAK,CAACpB;YAChB;QACF;IACF;IACA,MAAMqB,WAAWhB,OAAOiB,MAAM,CAC5BtF,OACI;QACEuD,UAAU;QACVxD,QAAQ;QACRnC,OAAO;QACP2H,MAAM;QACNC,KAAK;QACLC,OAAO;QACPC,QAAQ;QACRlF;QACAC;IACF,IACA,CAAC,GACLK,cAAc,CAAC,IAAI;QAAE6E,OAAO;IAAc,GAC1C1F;IAGF,MAAM2F,kBACJ,CAAC7E,gBAAgBX,gBAAgB,UAC7BA,gBAAgB,SACd,AAAC,2CAAwCyF,IAAAA,6BAAe,EAAC;QACvDxD;QACAC;QACAC;QACAC;QACAnC,aAAaA,eAAe;QAC5BG,WAAW6E,SAAS7E,SAAS;IAC/B,KAAG,OACH,AAAC,UAAOJ,cAAY,KAAI,uBAAuB;OACjD;IAEN,IAAI0F,mBAAmBF,kBACnB;QACEG,gBAAgBV,SAAS7E,SAAS,IAAI;QACtCwF,oBAAoBX,SAAS5E,cAAc,IAAI;QAC/CwF,kBAAkB;QAClBL;IACF,IACA,CAAC;IAEL,IAAIzC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IACEyC,iBAAiBF,eAAe,IAChCxF,gBAAgB,WAChBC,+BAAAA,YAAa0C,UAAU,CAAC,OACxB;YACA,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF+C,iBAAiBF,eAAe,GAAG,AAAC,UAAOvF,cAAY;QACzD;IACF;IAEA,MAAM6F,gBAAgBhH,iBAAiB;QACrCC;QACAtC;QACAuC;QACAxB,OAAOyE;QACPhD,SAAS6D;QACTrF;QACAyB;IACF;IAEA,IAAI6D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAOkB,WAAW,aAAa;YACjC,IAAI4B;YACJ,IAAI;gBACFA,UAAU,IAAIpC,IAAImC,cAAcrJ,GAAG;YACrC,EAAE,OAAOuJ,GAAG;gBACVD,UAAU,IAAIpC,IAAImC,cAAcrJ,GAAG,EAAE0H,OAAO8B,QAAQ,CAACC,IAAI;YAC3D;YACArJ,QAAQsJ,GAAG,CAACJ,QAAQG,IAAI,EAAE;gBAAEzJ;gBAAK+C;gBAAUQ;YAAY;QACzD;IACF;IAEA,MAAMoG,QAAkB;QACtB,GAAG5F,IAAI;QACPf,SAASiD,SAAS,SAASjD;QAC3BS;QACA1C,OAAOyE;QACPtC,QAAQuC;QACRmE,UAAU;QACV3G;QACAG,OAAO;YAAE,GAAGoF,QAAQ;YAAE,GAAGS,gBAAgB;QAAC;QAC1CjI,OAAOqI,cAAcrI,KAAK;QAC1B0B,QAAQ2G,cAAc3G,MAAM;QAC5B1C,KAAKqJ,cAAcrJ,GAAG;IACxB;IACA,MAAM6J,OAAO;QAAEtH;QAAaQ;QAAUQ;QAAaJ;IAAK;IACxD,OAAO;QAAEwG;QAAOE;IAAK;AACvB"}