{"version": 3, "sources": ["../../../../src/shared/lib/page-path/normalize-page-path.ts"], "names": ["normalizePagePath", "page", "normalized", "test", "isDynamicRoute", "ensureLeadingSlash", "process", "env", "NEXT_RUNTIME", "posix", "require", "resolvedPage", "normalize", "NormalizeError"], "mappings": ";;;;+BAagBA;;;eAAAA;;;oCAbmB;uBACJ;wBACA;AAWxB,SAASA,kBAAkBC,IAAY;IAC5C,MAAMC,aACJ,iBAAiBC,IAAI,CAACF,SAAS,CAACG,IAAAA,qBAAc,EAACH,QAC3C,AAAC,WAAQA,OACTA,SAAS,MACT,WACAI,IAAAA,sCAAkB,EAACJ;IAEzB,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,EAAEC,KAAK,EAAE,GAAGC,QAAQ;QAC1B,MAAMC,eAAeF,MAAMG,SAAS,CAACV;QACrC,IAAIS,iBAAiBT,YAAY;YAC/B,MAAM,IAAIW,sBAAc,CACtB,AAAC,2CAAwCX,aAAW,MAAGS;QAE3D;IACF;IAEA,OAAOT;AACT"}