{"version": 3, "sources": ["../../../src/shared/lib/head.tsx"], "names": ["defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "React", "Fragment", "concat", "Children", "toArray", "props", "children", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "has", "add", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "filter", "map", "c", "process", "env", "NODE_ENV", "__NEXT_OPTIMIZE_FONTS", "some", "url", "startsWith", "newProps", "undefined", "cloneElement", "srcMessage", "warnOnce", "Head", "ampState", "useContext", "AmpStateContext", "headManager", "HeadManagerContext", "Effect", "reduceComponentsToState", "isInAmpMode"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAagBA,WAAW;eAAXA;;IAwLhB,OAAmB;eAAnB;;;;;iEAnMkC;qEACf;yCACa;iDACG;yBACP;0BACH;AAMlB,SAASA,YAAYC,SAAiB;IAAjBA,IAAAA,sBAAAA,YAAY;IACtC,MAAMC,OAAO;sBAAC,6BAACC;YAAKC,SAAQ;;KAAW;IACvC,IAAI,CAACH,WAAW;QACdC,KAAKG,IAAI,eAAC,6BAACF;YAAKG,MAAK;YAAWC,SAAQ;;IAC1C;IACA,OAAOL;AACT;AAEA,SAASM,iBACPC,IAAoC,EACpCC,KAAuB;IAEvB,8FAA8F;IAC9F,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,UAAU;QAC1D,OAAOD;IACT;IACA,kCAAkC;IAClC,IAAIC,MAAMC,IAAI,KAAKC,cAAK,CAACC,QAAQ,EAAE;QACjC,OAAOJ,KAAKK,MAAM,CAChB,mGAAmG;QACnGF,cAAK,CAACG,QAAQ,CAACC,OAAO,CAACN,MAAMO,KAAK,CAACC,QAAQ,EAAEC,MAAM,CACjD,mGAAmG;QACnG,CACEC,cACAC;YAEA,IACE,OAAOA,kBAAkB,YACzB,OAAOA,kBAAkB,UACzB;gBACA,OAAOD;YACT;YACA,OAAOA,aAAaN,MAAM,CAACO;QAC7B,GACA,EAAE;IAGR;IACA,OAAOZ,KAAKK,MAAM,CAACJ;AACrB;AAEA,MAAMY,YAAY;IAAC;IAAQ;IAAa;IAAW;CAAW;AAE9D;;;;AAIA,GACA,SAASC;IACP,MAAMC,OAAO,IAAIC;IACjB,MAAMC,OAAO,IAAID;IACjB,MAAME,YAAY,IAAIF;IACtB,MAAMG,iBAAsD,CAAC;IAE7D,OAAO,CAACC;QACN,IAAIC,WAAW;QACf,IAAIC,SAAS;QAEb,IAAIF,EAAEG,GAAG,IAAI,OAAOH,EAAEG,GAAG,KAAK,YAAYH,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO,GAAG;YAChEF,SAAS;YACT,MAAMC,MAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,OAAO;YAC7C,IAAIT,KAAKW,GAAG,CAACH,MAAM;gBACjBF,WAAW;YACb,OAAO;gBACLN,KAAKY,GAAG,CAACJ;YACX;QACF;QAEA,wCAAwC;QACxC,OAAQH,EAAElB,IAAI;YACZ,KAAK;YACL,KAAK;gBACH,IAAIe,KAAKS,GAAG,CAACN,EAAElB,IAAI,GAAG;oBACpBmB,WAAW;gBACb,OAAO;oBACLJ,KAAKU,GAAG,CAACP,EAAElB,IAAI;gBACjB;gBACA;YACF,KAAK;gBACH,IAAK,IAAI0B,IAAI,GAAGC,MAAMhB,UAAUiB,MAAM,EAAEF,IAAIC,KAAKD,IAAK;oBACpD,MAAMG,WAAWlB,SAAS,CAACe,EAAE;oBAC7B,IAAI,CAACR,EAAEZ,KAAK,CAACwB,cAAc,CAACD,WAAW;oBAEvC,IAAIA,aAAa,WAAW;wBAC1B,IAAIb,UAAUQ,GAAG,CAACK,WAAW;4BAC3BV,WAAW;wBACb,OAAO;4BACLH,UAAUS,GAAG,CAACI;wBAChB;oBACF,OAAO;wBACL,MAAME,WAAWb,EAAEZ,KAAK,CAACuB,SAAS;wBAClC,MAAMG,aAAaf,cAAc,CAACY,SAAS,IAAI,IAAIf;wBACnD,IAAI,AAACe,CAAAA,aAAa,UAAU,CAACT,MAAK,KAAMY,WAAWR,GAAG,CAACO,WAAW;4BAChEZ,WAAW;wBACb,OAAO;4BACLa,WAAWP,GAAG,CAACM;4BACfd,cAAc,CAACY,SAAS,GAAGG;wBAC7B;oBACF;gBACF;gBACA;QACJ;QAEA,OAAOb;IACT;AACF;AAEA;;;CAGC,GACD,SAASc,iBACPC,oBAAoD,EACpD5B,KAAQ;IAER,MAAM,EAAEhB,SAAS,EAAE,GAAGgB;IACtB,OAAO4B,qBACJ1B,MAAM,CAACX,kBAAkB,EAAE,EAC3BsC,OAAO,GACPhC,MAAM,CAACd,YAAYC,WAAW6C,OAAO,IACrCC,MAAM,CAACxB,UACPuB,OAAO,GACPE,GAAG,CAAC,CAACC,GAA4BZ;QAChC,MAAML,MAAMiB,EAAEjB,GAAG,IAAIK;QACrB,IACEa,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBF,QAAQC,GAAG,CAACE,qBAAqB,IACjC,CAACpD,WACD;YACA,IACEgD,EAAEtC,IAAI,KAAK,UACXsC,EAAEhC,KAAK,CAAC,OAAO,IACf,0FAA0F;YAC1F;gBAAC;gBAAoC;aAA2B,CAACqC,IAAI,CACnE,CAACC,MAAQN,EAAEhC,KAAK,CAAC,OAAO,CAACuC,UAAU,CAACD,OAEtC;gBACA,MAAME,WAAW;oBAAE,GAAIR,EAAEhC,KAAK,IAAI,CAAC,CAAC;gBAAE;gBACtCwC,QAAQ,CAAC,YAAY,GAAGA,QAAQ,CAAC,OAAO;gBACxCA,QAAQ,CAAC,OAAO,GAAGC;gBAEnB,gEAAgE;gBAChED,QAAQ,CAAC,uBAAuB,GAAG;gBAEnC,qBAAO7C,cAAK,CAAC+C,YAAY,CAACV,GAAGQ;YAC/B;QACF;QACA,IAAIP,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,yDAAyD;YACzD,IAAIH,EAAEtC,IAAI,KAAK,YAAYsC,EAAEhC,KAAK,CAAC,OAAO,KAAK,uBAAuB;gBACpE,MAAM2C,aAAaX,EAAEhC,KAAK,CAAC,MAAM,GAC7B,AAAC,4BAAyBgC,EAAEhC,KAAK,CAAC,MAAM,GAAC,MACxC;gBACL4C,IAAAA,kBAAQ,EACN,AAAC,mDAAgDD,aAAW;YAEhE,OAAO,IAAIX,EAAEtC,IAAI,KAAK,UAAUsC,EAAEhC,KAAK,CAAC,MAAM,KAAK,cAAc;gBAC/D4C,IAAAA,kBAAQ,EACN,AAAC,wFAAqFZ,EAAEhC,KAAK,CAAC,OAAO,GAAC;YAE1G;QACF;QACA,qBAAOL,cAAK,CAAC+C,YAAY,CAACV,GAAG;YAAEjB;QAAI;IACrC;AACJ;AAEA;;;CAGC,GACD,SAAS8B,KAAK,KAA2C;IAA3C,IAAA,EAAE5C,QAAQ,EAAiC,GAA3C;IACZ,MAAM6C,WAAWC,IAAAA,iBAAU,EAACC,wCAAe;IAC3C,MAAMC,cAAcF,IAAAA,iBAAU,EAACG,mDAAkB;IACjD,qBACE,6BAACC,mBAAM;QACLC,yBAAyBzB;QACzBsB,aAAaA;QACbjE,WAAWqE,IAAAA,oBAAW,EAACP;OAEtB7C;AAGP;MAEA,WAAe4C"}