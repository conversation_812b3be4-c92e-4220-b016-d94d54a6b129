{"version": 3, "sources": ["../../../src/shared/lib/dynamic.tsx"], "names": ["noSSR", "dynamic", "isServerSide", "window", "convertModule", "mod", "default", "LoadableInitializer", "loadableOptions", "webpack", "modules", "Loading", "loading", "error", "isLoading", "past<PERSON>elay", "timedOut", "dynamicOptions", "options", "loadableFn", "Loadable", "process", "env", "NODE_ENV", "p", "message", "br", "stack", "Promise", "loader", "loaderFn", "then", "resolve", "loadableGenerated", "ssr"], "mappings": ";;;;;;;;;;;;;;;IAwDgBA,KAAK;eAALA;;IAoBhB,OAqEC;eArEuBC;;;;gEA5EN;gFACG;AAErB,MAAMC,eAAe,OAAOC,WAAW;AA2BvC,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASC,cAAiBC,GAAgD;IACxE,OAAO;QAAEC,SAAS,CAACD,uBAAD,AAACA,IAA4BC,OAAO,KAAID;IAAI;AAChE;AAqBO,SAASL,MACdO,mBAAkC,EAClCC,eAAkC;IAElC,yEAAyE;IACzE,OAAOA,gBAAgBC,OAAO;IAC9B,OAAOD,gBAAgBE,OAAO;IAE9B,oFAAoF;IACpF,IAAI,CAACR,cAAc;QACjB,OAAOK,oBAAoBC;IAC7B;IAEA,MAAMG,UAAUH,gBAAgBI,OAAO;IACvC,gDAAgD;IAChD,OAAO,kBACL,6BAACD;YAAQE,OAAO;YAAMC,WAAAA;YAAUC,WAAW;YAAOC,UAAU;;AAEhE;AAEe,SAASf,QACtBgB,cAA6C,EAC7CC,OAA2B;IAE3B,IAAIC,aAAaC,8BAAQ;IAEzB,IAAIZ,kBAAsC;QACxC,wDAAwD;QACxDI,SAAS;gBAAC,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;YACvC,IAAI,CAACA,WAAW,OAAO;YACvB,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIT,WAAW;oBACb,OAAO;gBACT;gBACA,IAAID,OAAO;oBACT,qBACE,6BAACW,WACEX,MAAMY,OAAO,gBACd,6BAACC,aACAb,MAAMc,KAAK;gBAGlB;YACF;YACA,OAAO;QACT;IACF;IAEA,qEAAqE;IACrE,wGAAwG;IACxG,2HAA2H;IAC3H,mEAAmE;IACnE,IAAIV,0BAA0BW,SAAS;QACrCpB,gBAAgBqB,MAAM,GAAG,IAAMZ;IAC/B,uFAAuF;IACzF,OAAO,IAAI,OAAOA,mBAAmB,YAAY;QAC/CT,gBAAgBqB,MAAM,GAAGZ;IACzB,mGAAmG;IACrG,OAAO,IAAI,OAAOA,mBAAmB,UAAU;QAC7CT,kBAAkB;YAAE,GAAGA,eAAe;YAAE,GAAGS,cAAc;QAAC;IAC5D;IAEA,gHAAgH;IAChHT,kBAAkB;QAAE,GAAGA,eAAe;QAAE,GAAGU,OAAO;IAAC;IAEnD,MAAMY,WAAWtB,gBAAgBqB,MAAM;IACvC,MAAMA,SAAS,IACbC,YAAY,OACRA,WAAWC,IAAI,CAAC3B,iBAChBwB,QAAQI,OAAO,CAAC5B,cAAc,IAAM;IAE1C,2DAA2D;IAC3D,IAAII,gBAAgByB,iBAAiB,EAAE;QACrCzB,kBAAkB;YAChB,GAAGA,eAAe;YAClB,GAAGA,gBAAgByB,iBAAiB;QACtC;QACA,OAAOzB,gBAAgByB,iBAAiB;IAC1C;IAEA,0GAA0G;IAC1G,IAAI,OAAOzB,gBAAgB0B,GAAG,KAAK,aAAa,CAAC1B,gBAAgB0B,GAAG,EAAE;QACpE,OAAO1B,gBAAgBC,OAAO;QAC9B,OAAOD,gBAAgBE,OAAO;QAE9B,OAAOV,MAAMmB,YAAYX;IAC3B;IAEA,OAAOW,WAAW;QAAE,GAAGX,eAAe;QAAEqB,QAAQA;IAAoB;AACtE"}