{"version": 3, "sources": ["../../src/client/script.tsx"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "ignoreProps", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "k", "value", "Object", "entries", "undefined", "includes", "attr", "DOMAttributeNames", "toLowerCase", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "defineProperty"], "mappings": "AAAA;;;;;;;;;;;;;;;;;IA6KgBA,sBAAsB;eAAtBA;;IAgCAC,gBAAgB;eAAhBA;;IA6KhB,OAAqB;eAArB;;;;;mEAxXqB;iEACgC;iDAElB;6BACD;qCACE;AAEpC,MAAMC,cAAc,IAAIC;AACxB,MAAMC,YAAY,IAAIC;AAiBtB,MAAMC,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,oBAAoB,CAACC;IACzB,iGAAiG;IACjG,EAAE;IACF,oEAAoE;IACpE,kFAAkF;IAClF,4EAA4E;IAC5E,6EAA6E;IAC7E,IAAIC,iBAAQ,CAACC,OAAO,EAAE;QACpBF,YAAYG,OAAO,CAAC,CAACC;YACnBH,iBAAQ,CAACC,OAAO,CAACE,YAAY;gBAAEC,IAAI;YAAQ;QAC7C;QAEA;IACF;IAEA,gGAAgG;IAChG,EAAE;IACF,kEAAkE;IAClE,yEAAyE;IACzE,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAIC,OAAOC,SAASD,IAAI;QACxBP,YAAYG,OAAO,CAAC,CAACC;YACnB,IAAIK,OAAOD,SAASE,aAAa,CAAC;YAElCD,KAAKE,IAAI,GAAG;YACZF,KAAKG,GAAG,GAAG;YACXH,KAAKI,IAAI,GAAGT;YAEZG,KAAKO,WAAW,CAACL;QACnB;IACF;AACF;AAEA,MAAMM,aAAa,CAACC;IAClB,MAAM,EACJC,GAAG,EACHC,EAAE,EACFC,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdC,uBAAuB,EACvBC,WAAW,EAAE,EACbC,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACZ,GAAGgB;IAEJ,MAAMS,WAAWP,MAAMD;IAEvB,4BAA4B;IAC5B,IAAIQ,YAAY7B,UAAU8B,GAAG,CAACD,WAAW;QACvC;IACF;IAEA,qDAAqD;IACrD,IAAI/B,YAAYgC,GAAG,CAACT,MAAM;QACxBrB,UAAU+B,GAAG,CAACF;QACd,wGAAwG;QACxG,sGAAsG;QACtG/B,YAAYkC,GAAG,CAACX,KAAKY,IAAI,CAACV,QAAQK;QAClC;IACF;IAEA,0CAA0C,GAC1C,MAAMM,YAAY;QAChB,kDAAkD;QAClD,IAAIV,SAAS;YACXA;QACF;QACA,mDAAmD;QACnDxB,UAAU+B,GAAG,CAACF;IAChB;IAEA,MAAMM,KAAKvB,SAASE,aAAa,CAAC;IAElC,MAAMsB,cAAc,IAAIC,QAAc,CAACC,SAASC;QAC9CJ,GAAGK,gBAAgB,CAAC,QAAQ,SAAUC,CAAC;YACrCH;YACA,IAAIf,QAAQ;gBACVA,OAAOmB,IAAI,CAAC,IAAI,EAAED;YACpB;YACAP;QACF;QACAC,GAAGK,gBAAgB,CAAC,SAAS,SAAUC,CAAC;YACtCF,OAAOE;QACT;IACF,GAAGE,KAAK,CAAC,SAAUF,CAAC;QAClB,IAAIb,SAAS;YACXA,QAAQa;QACV;IACF;IAEA,IAAIhB,yBAAyB;QAC3B,2DAA2D;QAC3DU,GAAGS,SAAS,GAAG,AAACnB,wBAAwBoB,MAAM,IAAe;QAE7DX;IACF,OAAO,IAAIR,UAAU;QACnBS,GAAGW,WAAW,GACZ,OAAOpB,aAAa,WAChBA,WACAqB,MAAMC,OAAO,CAACtB,YACdA,SAASuB,IAAI,CAAC,MACd;QAENf;IACF,OAAO,IAAIb,KAAK;QACdc,GAAGd,GAAG,GAAGA;QACT,4DAA4D;QAC5D,yFAAyF;QAEzFvB,YAAYoD,GAAG,CAAC7B,KAAKe;IACvB;IAEA,KAAK,MAAM,CAACe,GAAGC,MAAM,IAAIC,OAAOC,OAAO,CAAClC,OAAQ;QAC9C,IAAIgC,UAAUG,aAAarD,YAAYsD,QAAQ,CAACL,IAAI;YAClD;QACF;QAEA,MAAMM,OAAOC,8BAAiB,CAACP,EAAE,IAAIA,EAAEQ,WAAW;QAClDxB,GAAGyB,YAAY,CAACH,MAAML;IACxB;IAEA,IAAIzB,aAAa,UAAU;QACzBQ,GAAGyB,YAAY,CAAC,QAAQ;IAC1B;IAEAzB,GAAGyB,YAAY,CAAC,gBAAgBjC;IAEhC,0CAA0C;IAC1C,IAAIvB,aAAa;QACfD,kBAAkBC;IACpB;IAEAQ,SAASiD,IAAI,CAAC3C,WAAW,CAACiB;AAC5B;AAEO,SAASvC,uBAAuBwB,KAAkB;IACvD,MAAM,EAAEO,WAAW,kBAAkB,EAAE,GAAGP;IAC1C,IAAIO,aAAa,cAAc;QAC7BjB,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9BsB,IAAAA,wCAAmB,EAAC,IAAM3C,WAAWC;QACvC;IACF,OAAO;QACLD,WAAWC;IACb;AACF;AAEA,SAAS2C,eAAe3C,KAAkB;IACxC,IAAIR,SAASoD,UAAU,KAAK,YAAY;QACtCF,IAAAA,wCAAmB,EAAC,IAAM3C,WAAWC;IACvC,OAAO;QACLV,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9BsB,IAAAA,wCAAmB,EAAC,IAAM3C,WAAWC;QACvC;IACF;AACF;AAEA,SAAS6C;IACP,MAAMC,UAAU;WACXtD,SAASuD,gBAAgB,CAAC;WAC1BvD,SAASuD,gBAAgB,CAAC;KAC9B;IACDD,QAAQ3D,OAAO,CAAC,CAAC6D;QACf,MAAMvC,WAAWuC,OAAO9C,EAAE,IAAI8C,OAAOC,YAAY,CAAC;QAClDrE,UAAU+B,GAAG,CAACF;IAChB;AACF;AAEO,SAAShC,iBAAiByE,iBAAgC;IAC/DA,kBAAkB/D,OAAO,CAACX;IAC1BqE;AACF;AAEA,SAASM,OAAOnD,KAAkB;IAChC,MAAM,EACJE,EAAE,EACFD,MAAM,EAAE,EACRE,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdG,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACX,GAAGoE,WACJ,GAAGpD;IAEJ,uCAAuC;IACvC,MAAM,EAAEqD,aAAa,EAAEP,OAAO,EAAEQ,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GACvDC,IAAAA,iBAAU,EAACC,mDAAkB;IAE/B;;;;;;;;;;;;;;;;;;;;;;;;;GAyBC,GACD,MAAMC,yBAAyBC,IAAAA,aAAM,EAAC;IAEtCC,IAAAA,gBAAS,EAAC;QACR,MAAMpD,WAAWP,MAAMD;QACvB,IAAI,CAAC0D,uBAAuBG,OAAO,EAAE;YACnC,sEAAsE;YACtE,IAAI1D,WAAWK,YAAY7B,UAAU8B,GAAG,CAACD,WAAW;gBAClDL;YACF;YAEAuD,uBAAuBG,OAAO,GAAG;QACnC;IACF,GAAG;QAAC1D;QAASF;QAAID;KAAI;IAErB,MAAM8D,4BAA4BH,IAAAA,aAAM,EAAC;IAEzCC,IAAAA,gBAAS,EAAC;QACR,IAAI,CAACE,0BAA0BD,OAAO,EAAE;YACtC,IAAIvD,aAAa,oBAAoB;gBACnCR,WAAWC;YACb,OAAO,IAAIO,aAAa,cAAc;gBACpCoC,eAAe3C;YACjB;YAEA+D,0BAA0BD,OAAO,GAAG;QACtC;IACF,GAAG;QAAC9D;QAAOO;KAAS;IAEpB,IAAIA,aAAa,uBAAuBA,aAAa,UAAU;QAC7D,IAAI8C,eAAe;YACjBP,OAAO,CAACvC,SAAS,GAAG,AAACuC,CAAAA,OAAO,CAACvC,SAAS,IAAI,EAAE,AAAD,EAAGyD,MAAM,CAAC;gBACnD;oBACE9D;oBACAD;oBACAE;oBACAC;oBACAI;oBACA,GAAG4C,SAAS;gBACd;aACD;YACDC,cAAcP;QAChB,OAAO,IAAIQ,YAAYA,YAAY;YACjC,uCAAuC;YACvC1E,UAAU+B,GAAG,CAACT,MAAMD;QACtB,OAAO,IAAIqD,YAAY,CAACA,YAAY;YAClCvD,WAAWC;QACb;IACF;IAEA,uEAAuE;IACvE,IAAIuD,QAAQ;QACV,oFAAoF;QACpF,uEAAuE;QACvE,oEAAoE;QACpE,6EAA6E;QAC7E,EAAE;QACF,yEAAyE;QACzE,+EAA+E;QAC/E,4EAA4E;QAC5E,wGAAwG;QACxG,IAAIvE,aAAa;YACfA,YAAYG,OAAO,CAAC,CAAC8E;gBACnBhF,iBAAQ,CAACC,OAAO,CAAC+E,UAAU;oBAAE5E,IAAI;gBAAQ;YAC3C;QACF;QAEA,2EAA2E;QAC3E,gEAAgE;QAChE,IAAIkB,aAAa,qBAAqB;YACpC,IAAI,CAACN,KAAK;gBACR,yDAAyD;gBACzD,IAAImD,UAAU/C,uBAAuB,EAAE;oBACrC,2DAA2D;oBAC3D+C,UAAU9C,QAAQ,GAAG8C,UAAU/C,uBAAuB,CACnDoB,MAAM;oBACT,OAAO2B,UAAU/C,uBAAuB;gBAC1C;gBAEA,qBACE,6BAAC2C;oBACCQ,OAAOA;oBACPnD,yBAAyB;wBACvBoB,QAAQ,AAAC,4CAAyCyC,KAAKC,SAAS,CAAC;4BAC/D;4BACA;gCAAE,GAAGf,SAAS;4BAAC;yBAChB,IAAE;oBACL;;YAGN,OAAO;gBACL,aAAa;gBACbnE,iBAAQ,CAACmF,OAAO,CACdnE,KACAmD,UAAUiB,SAAS,GACf;oBAAEhF,IAAI;oBAAUgF,WAAWjB,UAAUiB,SAAS;gBAAC,IAC/C;oBAAEhF,IAAI;gBAAS;gBAErB,qBACE,6BAAC2D;oBACCQ,OAAOA;oBACPnD,yBAAyB;wBACvBoB,QAAQ,AAAC,4CAAyCyC,KAAKC,SAAS,CAAC;4BAC/DlE;yBACD,IAAE;oBACL;;YAGN;QACF,OAAO,IAAIM,aAAa,oBAAoB;YAC1C,IAAIN,KAAK;gBACP,aAAa;gBACbhB,iBAAQ,CAACmF,OAAO,CACdnE,KACAmD,UAAUiB,SAAS,GACf;oBAAEhF,IAAI;oBAAUgF,WAAWjB,UAAUiB,SAAS;gBAAC,IAC/C;oBAAEhF,IAAI;gBAAS;YAEvB;QACF;IACF;IAEA,OAAO;AACT;AAEA4C,OAAOqC,cAAc,CAACnB,QAAQ,gBAAgB;IAAEnB,OAAO;AAAK;MAE5D,WAAemB"}