{"version": 3, "sources": ["../../../src/client/components/static-generation-bailout.ts"], "names": ["staticGenerationBailout", "StaticGenBailoutError", "Error", "code", "formatErrorMessage", "reason", "opts", "dynamic", "link", "suffix", "staticGenerationStore", "staticGenerationAsyncStorage", "getStore", "forceStatic", "dynamicShouldError", "message", "maybePostpone", "revalidate", "staticPrefetchBailout", "isStaticGeneration", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack"], "mappings": ";;;;+BAuBaA;;;eAAAA;;;oCAvBsB;+BACL;sDACe;AAE7C,MAAMC,8BAA8BC;;;aAClCC,OAAO;;AACT;AASA,SAASC,mBAAmBC,MAAc,EAAEC,IAAkB;IAC5D,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGF,QAAQ,CAAC;IACnC,MAAMG,SAASD,OAAO,AAAC,0BAAuBA,OAAS;IACvD,OAAO,AAAC,SACND,CAAAA,UAAU,AAAC,uBAAqBA,UAAQ,OAAO,EAAC,IACjD,uDAAqDF,SAAO,OAAKI;AACpE;AAEO,MAAMT,0BAAmD,CAC9DK,QACAC;IAEA,MAAMI,wBAAwBC,kEAA4B,CAACC,QAAQ;IACnE,IAAI,CAACF,uBAAuB,OAAO;IAEnC,IAAIA,sBAAsBG,WAAW,EAAE;QACrC,OAAO;IACT;IAEA,IAAIH,sBAAsBI,kBAAkB,EAAE;YAEKR;QADjD,MAAM,IAAIL,sBACRG,mBAAmBC,QAAQ;YAAE,GAAGC,IAAI;YAAEC,SAASD,CAAAA,gBAAAA,wBAAAA,KAAMC,OAAO,YAAbD,gBAAiB;QAAQ;IAE5E;IAEA,MAAMS,UAAUX,mBAAmBC,QAAQ;QACzC,GAAGC,IAAI;QACP,uEAAuE;QACvE,8EAA8E;QAC9EE,MAAM;IACR;IAEAQ,IAAAA,4BAAa,EAACN,uBAAuBL;IAErC,2EAA2E;IAC3E,QAAQ;IACRK,sBAAsBO,UAAU,GAAG;IAEnC,IAAI,EAACX,wBAAAA,KAAMC,OAAO,GAAE;QAClB,0DAA0D;QAC1D,sCAAsC;QACtCG,sBAAsBQ,qBAAqB,GAAG;IAChD;IAEA,IAAIR,sBAAsBS,kBAAkB,EAAE;QAC5C,MAAMC,MAAM,IAAIC,sCAAkB,CAACN;QACnCL,sBAAsBY,uBAAuB,GAAGjB;QAChDK,sBAAsBa,iBAAiB,GAAGH,IAAII,KAAK;QAEnD,MAAMJ;IACR;IAEA,OAAO;AACT"}