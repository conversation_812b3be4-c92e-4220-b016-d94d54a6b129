{"version": 3, "sources": ["../../../src/client/components/headers.ts"], "names": ["headers", "cookies", "draftMode", "staticGenerationBailout", "link", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "Headers", "requestStore", "requestAsyncStorage", "getStore", "Error", "RequestCookiesAdapter", "RequestCookies", "asyncActionStore", "actionAsyncStorage", "isAction", "isAppRoute", "mutableCookies", "DraftMode"], "mappings": ";;;;;;;;;;;;;;;;IAWgBA,OAAO;eAAPA;;IAkBAC,OAAO;eAAPA;;IA6BAC,SAAS;eAATA;;;gCAvDT;yBACwB;yBACA;6CACK;4CACD;yCACK;2BACd;AAEnB,SAASF;IACd,IACEG,IAAAA,gDAAuB,EAAC,WAAW;QACjCC,MAAM;IACR,IACA;QACA,OAAOC,uBAAc,CAACC,IAAI,CAAC,IAAIC,QAAQ,CAAC;IAC1C;IACA,MAAMC,eAAeC,gDAAmB,CAACC,QAAQ;IACjD,IAAI,CAACF,cAAc;QACjB,MAAM,IAAIG,MACP;IAEL;IAEA,OAAOH,aAAaR,OAAO;AAC7B;AAEO,SAASC;IACd,IACEE,IAAAA,gDAAuB,EAAC,WAAW;QACjCC,MAAM;IACR,IACA;QACA,OAAOQ,qCAAqB,CAACN,IAAI,CAAC,IAAIO,uBAAc,CAAC,IAAIN,QAAQ,CAAC;IACpE;IAEA,MAAMC,eAAeC,gDAAmB,CAACC,QAAQ;IACjD,IAAI,CAACF,cAAc;QACjB,MAAM,IAAIG,MACP;IAEL;IAEA,MAAMG,mBAAmBC,8CAAkB,CAACL,QAAQ;IACpD,IACEI,oBACCA,CAAAA,iBAAiBE,QAAQ,IAAIF,iBAAiBG,UAAU,AAAD,GACxD;QACA,2EAA2E;QAC3E,+DAA+D;QAC/D,OAAOT,aAAaU,cAAc;IACpC;IAEA,OAAOV,aAAaP,OAAO;AAC7B;AAEO,SAASC;IACd,MAAMM,eAAeC,gDAAmB,CAACC,QAAQ;IACjD,IAAI,CAACF,cAAc;QACjB,MAAM,IAAIG,MACP;IAEL;IACA,OAAO,IAAIQ,oBAAS,CAACX,aAAaN,SAAS;AAC7C"}