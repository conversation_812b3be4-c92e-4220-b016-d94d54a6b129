{"version": 3, "sources": ["../../../src/client/components/redirect.ts"], "names": ["getRedirectError", "redirect", "permanentRedirect", "isRedirectError", "getURLFromRedirectError", "getRedirectTypeFromError", "REDIRECT_ERROR_CODE", "RedirectType", "push", "replace", "url", "type", "permanent", "error", "Error", "digest", "requestStore", "requestAsyncStorage", "getStore", "mutableCookies", "errorCode", "destination", "split"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAegBA,gBAAgB;eAAhBA;;IAqBAC,QAAQ;eAARA;;IAcAC,iBAAiB;eAAjBA;;IAcAC,eAAe;eAAfA;;IA2BAC,uBAAuB;eAAvBA;;IAQAC,wBAAwB;eAAxBA;;;6CAnGoB;AAGpC,MAAMC,sBAAsB;IAErB;UAAKC,YAAY;IAAZA,aACVC,UAAAA;IADUD,aAEVE,aAAAA;GAFUF,iBAAAA;AAUL,SAASP,iBACdU,GAAW,EACXC,IAAkB,EAClBC,SAA0B;IAA1BA,IAAAA,sBAAAA,YAAqB;IAErB,MAAMC,QAAQ,IAAIC,MAAMR;IACxBO,MAAME,MAAM,GAAG,AAAGT,sBAAoB,MAAGK,OAAK,MAAGD,MAAI,MAAGE;IACxD,MAAMI,eAAeC,gDAAmB,CAACC,QAAQ;IACjD,IAAIF,cAAc;QAChBH,MAAMM,cAAc,GAAGH,aAAaG,cAAc;IACpD;IACA,OAAON;AACT;AASO,SAASZ,SACdS,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA,OA/BU;IAiCV,MAAMX,iBAAiBU,KAAKC,MAAM;AACpC;AASO,SAAST,kBACdQ,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA,OA7CU;IA+CV,MAAMX,iBAAiBU,KAAKC,MAAM;AACpC;AASO,SAASR,gBACdU,KAAU;IAEV,IAAI,QAAOA,yBAAAA,MAAOE,MAAM,MAAK,UAAU,OAAO;IAE9C,MAAM,CAACK,WAAWT,MAAMU,aAAaT,UAAU,GAAG,AAChDC,MAAME,MAAM,CACZO,KAAK,CAAC,KAAK;IAEb,OACEF,cAAcd,uBACbK,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOU,gBAAgB,YACtBT,CAAAA,cAAc,UAAUA,cAAc,OAAM;AAEjD;AAYO,SAASR,wBAAwBS,KAAU;IAChD,IAAI,CAACV,gBAAgBU,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACO,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASjB,yBACdQ,KAAuB;IAEvB,IAAI,CAACV,gBAAgBU,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAME,MAAM,CAACO,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC"}