{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-mutable.ts"], "names": ["handleMutable", "isNotUndefined", "value", "state", "mutable", "shouldScroll", "computeChangedPath", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "scrollableSegments", "onlyHashChange", "hashFragment", "split", "decodeURIComponent", "slice", "segmentPaths", "cache", "prefetchCache", "tree", "patchedTree", "nextUrl"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;oCAXmB;AAOnC,SAASC,eAAkBC,KAAQ;IACjC,OAAO,OAAOA,UAAU;AAC1B;AAEO,SAASF,cACdG,KAA2B,EAC3BC,OAAgB;QAqCRA;QAlCaA;IADrB,0DAA0D;IAC1D,MAAMC,eAAeD,CAAAA,wBAAAA,QAAQC,YAAY,YAApBD,wBAAwB;QA6CrCA,6BAcFE;IAzDN,OAAO;QACLC,SAASJ,MAAMI,OAAO;QACtB,YAAY;QACZC,cAAcP,eAAeG,QAAQI,YAAY,IAC7CJ,QAAQI,YAAY,KAAKL,MAAMK,YAAY,GACzCL,MAAMK,YAAY,GAClBJ,QAAQI,YAAY,GACtBL,MAAMK,YAAY;QACtBC,SAAS;YACPC,aAAaT,eAAeG,QAAQM,WAAW,IAC3CN,QAAQM,WAAW,GACnBP,MAAMM,OAAO,CAACC,WAAW;YAC7BC,eAAeV,eAAeG,QAAQO,aAAa,IAC/CP,QAAQO,aAAa,GACrBR,MAAMM,OAAO,CAACE,aAAa;YAC/BC,4BAA4BX,eAC1BG,QAAQQ,0BAA0B,IAEhCR,QAAQQ,0BAA0B,GAClCT,MAAMM,OAAO,CAACG,0BAA0B;QAC9C;QACA,kEAAkE;QAClEC,mBAAmB;YACjBC,OAAOT,eACHJ,eAAeG,2BAAAA,QAASW,kBAAkB,IACxC,OACAZ,MAAMU,iBAAiB,CAACC,KAAK,GAE/B;YACJE,gBACE,CAAC,CAACZ,QAAQa,YAAY,IACtBd,MAAMK,YAAY,CAACU,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACjCd,wBAAAA,QAAQI,YAAY,qBAApBJ,sBAAsBc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YAC1CD,cAAcZ,eAEV,oCAAoC;YACpCD,QAAQa,YAAY,IAAIb,QAAQa,YAAY,KAAK,KAE/CE,mBAAmBf,QAAQa,YAAY,CAACG,KAAK,CAAC,MAC9CjB,MAAMU,iBAAiB,CAACI,YAAY,GAEtC;YACJI,cAAchB,eACVD,CAAAA,8BAAAA,2BAAAA,QAASW,kBAAkB,YAA3BX,8BAA+BD,MAAMU,iBAAiB,CAACQ,YAAY,GAEnE,EAAE;QACR;QACA,eAAe;QACfC,OAAOlB,QAAQkB,KAAK,GAAGlB,QAAQkB,KAAK,GAAGnB,MAAMmB,KAAK;QAClDC,eAAenB,QAAQmB,aAAa,GAChCnB,QAAQmB,aAAa,GACrBpB,MAAMoB,aAAa;QACvB,8BAA8B;QAC9BC,MAAMvB,eAAeG,QAAQqB,WAAW,IACpCrB,QAAQqB,WAAW,GACnBtB,MAAMqB,IAAI;QACdE,SAASzB,eAAeG,QAAQqB,WAAW,IACvCnB,CAAAA,sBAAAA,IAAAA,sCAAkB,EAACH,MAAMqB,IAAI,EAAEpB,QAAQqB,WAAW,aAAlDnB,sBACAH,MAAMK,YAAY,GAClBL,MAAMuB,OAAO;IACnB;AACF"}