{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fetch-server-response.ts"], "names": ["fetchServerResponse", "createFromFetch", "process", "env", "NEXT_RUNTIME", "require", "doMpaNavigation", "url", "urlToUrlWithoutFlightMarker", "toString", "undefined", "flightRouterState", "nextUrl", "currentBuildId", "prefetchKind", "headers", "RSC_HEADER", "NEXT_ROUTER_STATE_TREE", "encodeURIComponent", "JSON", "stringify", "PrefetchKind", "AUTO", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_URL", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexHash", "join", "fetchUrl", "URL", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "searchParams", "set", "NEXT_RSC_UNION_QUERY", "res", "fetch", "credentials", "responseUrl", "canonicalUrl", "redirected", "contentType", "get", "postponed", "NEXT_DID_POSTPONE_HEADER", "isFlightResponse", "RSC_CONTENT_TYPE_HEADER", "startsWith", "ok", "hash", "buildId", "flightData", "Promise", "resolve", "callServer", "err", "console", "error"], "mappings": "AAAA;;;;;+BA6<PERSON><PERSON>;;;eAAAA;;;kCApBf;2BACqC;+BACjB;oCACE;sBACL;2BACiB;AA5BzC,aAAa;AACb,6DAA6D;AAC7D,oEAAoE;AACpE,MAAM,EAAEC,eAAe,EAAE,GACvB,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AA4Bd,SAASC,gBAAgBC,GAAW;IAClC,OAAO;QAACC,IAAAA,sCAA2B,EAACD,KAAKE,QAAQ;QAAIC;KAAU;AACjE;AAKO,eAAeV,oBACpBO,GAAQ,EACRI,iBAAoC,EACpCC,OAAsB,EACtBC,cAAsB,EACtBC,YAA2B;IAE3B,MAAMC,UAKF;QACF,yBAAyB;QACzB,CAACC,4BAAU,CAAC,EAAE;QACd,mCAAmC;QACnC,CAACC,wCAAsB,CAAC,EAAEC,mBACxBC,KAAKC,SAAS,CAACT;IAEnB;IAEA;;;;;GAKC,GACD,IAAIG,iBAAiBO,gCAAY,CAACC,IAAI,EAAE;QACtCP,OAAO,CAACQ,6CAA2B,CAAC,GAAG;IACzC;IAEA,IAAIX,SAAS;QACXG,OAAO,CAACS,0BAAQ,CAAC,GAAGZ;IACtB;IAEA,MAAMa,mBAAmBC,IAAAA,aAAO,EAC9B;QACEX,OAAO,CAACQ,6CAA2B,CAAC,IAAI;QACxCR,OAAO,CAACE,wCAAsB,CAAC;QAC/BF,OAAO,CAACS,0BAAQ,CAAC;KAClB,CAACG,IAAI,CAAC;IAGT,IAAI;QACF,IAAIC,WAAW,IAAIC,IAAItB;QACvB,IAAIL,QAAQC,GAAG,CAAC2B,QAAQ,KAAK,cAAc;YACzC,IAAI5B,QAAQC,GAAG,CAAC4B,oBAAoB,KAAK,UAAU;gBACjD,IAAIH,SAASI,QAAQ,CAACC,QAAQ,CAAC,MAAM;oBACnCL,SAASI,QAAQ,IAAI;gBACvB,OAAO;oBACLJ,SAASI,QAAQ,IAAI;gBACvB;YACF;QACF;QAEA,8FAA8F;QAC9FJ,SAASM,YAAY,CAACC,GAAG,CAACC,sCAAoB,EAAEX;QAEhD,MAAMY,MAAM,MAAMC,MAAMV,UAAU;YAChC,wFAAwF;YACxFW,aAAa;YACbxB;QACF;QAEA,MAAMyB,cAAchC,IAAAA,sCAA2B,EAAC6B,IAAI9B,GAAG;QACvD,MAAMkC,eAAeJ,IAAIK,UAAU,GAAGF,cAAc9B;QAEpD,MAAMiC,cAAcN,IAAItB,OAAO,CAAC6B,GAAG,CAAC,mBAAmB;QACvD,MAAMC,YAAY,CAAC,CAACR,IAAItB,OAAO,CAAC6B,GAAG,CAACE,mCAAwB;QAC5D,IAAIC,mBAAmBJ,gBAAgBK,yCAAuB;QAE9D,IAAI9C,QAAQC,GAAG,CAAC2B,QAAQ,KAAK,cAAc;YACzC,IAAI5B,QAAQC,GAAG,CAAC4B,oBAAoB,KAAK,UAAU;gBACjD,IAAI,CAACgB,kBAAkB;oBACrBA,mBAAmBJ,YAAYM,UAAU,CAAC;gBAC5C;YACF;QACF;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACF,oBAAoB,CAACV,IAAIa,EAAE,EAAE;YAChC,2FAA2F;YAC3F,IAAI3C,IAAI4C,IAAI,EAAE;gBACZX,YAAYW,IAAI,GAAG5C,IAAI4C,IAAI;YAC7B;YAEA,OAAO7C,gBAAgBkC,YAAY/B,QAAQ;QAC7C;QAEA,2EAA2E;QAC3E,MAAM,CAAC2C,SAASC,WAAW,GAAuB,MAAMpD,gBACtDqD,QAAQC,OAAO,CAAClB,MAChB;YACEmB,YAAAA,yBAAU;QACZ;QAGF,IAAI3C,mBAAmBuC,SAAS;YAC9B,OAAO9C,gBAAgB+B,IAAI9B,GAAG;QAChC;QAEA,OAAO;YAAC8C;YAAYZ;YAAcI;SAAU;IAC9C,EAAE,OAAOY,KAAK;QACZC,QAAQC,KAAK,CACX,AAAC,qCAAkCpD,MAAI,yCACvCkD;QAEF,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YAAClD,IAAIE,QAAQ;YAAIC;SAAU;IACpC;AACF"}