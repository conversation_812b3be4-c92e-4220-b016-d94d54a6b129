{"version": 3, "sources": ["../../src/trace/trace.ts"], "names": ["Span", "trace", "flushAllTraces", "exportTraceState", "initializeTraceState", "getTraceEvents", "recordTraceEvents", "clearTraceEvents", "NUM_OF_MICROSEC_IN_NANOSEC", "BigInt", "count", "getId", "defaultParentSpanId", "shouldSaveTraceEvents", "savedTraceEvents", "SpanStatus", "Started", "Stopped", "constructor", "name", "parentId", "attrs", "startTime", "duration", "status", "id", "_start", "process", "hrtime", "bigint", "now", "Date", "stop", "stopTime", "end", "Number", "MAX_SAFE_INTEGER", "Error", "timestamp", "traceEvent", "tags", "reporter", "report", "push", "<PERSON><PERSON><PERSON><PERSON>", "manualTraceChild", "span", "setAttribute", "key", "value", "String", "traceFn", "fn", "traceAsyncFn", "flushAll", "lastId", "state", "events"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAoBaA,IAAI;eAAJA;;IAuGAC,KAAK;eAALA;;IAQAC,cAAc;eAAdA;;IAKAC,gBAAgB;eAAhBA;;IAKAC,oBAAoB;eAApBA;;IAMGC,cAAc;eAAdA;;IAIAC,iBAAiB;eAAjBA;;IAYHC,gBAAgB;eAAhBA;;;wBAnKY;AAGzB,MAAMC,6BAA6BC,OAAO;AAC1C,IAAIC,QAAQ;AACZ,MAAMC,QAAQ;IACZD;IACA,OAAOA;AACT;AACA,IAAIE;AACJ,IAAIC;AACJ,IAAIC,mBAAiC,EAAE;IAIhC;UAAKC,UAAU;IAAVA,WAAAA,WACVC,aAAAA,KAAAA;IADUD,WAAAA,WAEVE,aAAAA,KAAAA;GAFUF,eAAAA;AAKL,MAAMf;IAaXkB,YAAY,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,SAAS,EAMV,CAAE;QACD,IAAI,CAACH,IAAI,GAAGA;QACZ,IAAI,CAACC,QAAQ,GAAGA,YAAYR;QAC5B,IAAI,CAACW,QAAQ,GAAG;QAChB,IAAI,CAACF,KAAK,GAAGA,QAAQ;YAAE,GAAGA,KAAK;QAAC,IAAI,CAAC;QACrC,IAAI,CAACG,MAAM,GAhCbR;QAiCE,IAAI,CAACS,EAAE,GAAGd;QACV,IAAI,CAACe,MAAM,GAAGJ,aAAaK,QAAQC,MAAM,CAACC,MAAM;QAChD,wEAAwE;QACxE,iDAAiD;QACjD,2IAA2I;QAC3I,wDAAwD;QACxD,iFAAiF;QACjF,IAAI,CAACC,GAAG,GAAGC,KAAKD,GAAG;IACrB;IAEA,yEAAyE;IACzE,+DAA+D;IAC/D,wEAAwE;IACxE,yCAAyC;IACzCE,KAAKC,QAAiB,EAAE;QACtB,MAAMC,MAAcD,YAAYN,QAAQC,MAAM,CAACC,MAAM;QACrD,MAAMN,WAAW,AAACW,CAAAA,MAAM,IAAI,CAACR,MAAM,AAAD,IAAKlB;QACvC,IAAI,CAACgB,MAAM,GAjDbP;QAkDE,IAAIM,WAAWY,OAAOC,gBAAgB,EAAE;YACtC,MAAM,IAAIC,MAAM,CAAC,4CAA4C,EAAEd,SAAS,CAAC;QAC3E;QACA,MAAMe,YAAY,IAAI,CAACZ,MAAM,GAAGlB;QAChC,MAAM+B,aAAyB;YAC7BpB,MAAM,IAAI,CAACA,IAAI;YACfI,UAAUY,OAAOZ;YACjBe,WAAWH,OAAOG;YAClBb,IAAI,IAAI,CAACA,EAAE;YACXL,UAAU,IAAI,CAACA,QAAQ;YACvBoB,MAAM,IAAI,CAACnB,KAAK;YAChBC,WAAW,IAAI,CAACQ,GAAG;QACrB;QACAW,gBAAQ,CAACC,MAAM,CAACH;QAChB,IAAI1B,uBAAuB;YACzBC,iBAAiB6B,IAAI,CAACJ;QACxB;IACF;IAEAK,WAAWzB,IAAY,EAAEE,KAAc,EAAE;QACvC,OAAO,IAAIrB,KAAK;YAAEmB;YAAMC,UAAU,IAAI,CAACK,EAAE;YAAEJ;QAAM;IACnD;IAEAwB,iBACE1B,IAAY,EACZ,yCAAyC;IACzCG,SAAiB,EACjB,wCAAwC;IACxCW,QAAgB,EAChBZ,KAAc,EACd;QACA,MAAMyB,OAAO,IAAI9C,KAAK;YAAEmB;YAAMC,UAAU,IAAI,CAACK,EAAE;YAAEJ;YAAOC;QAAU;QAClEwB,KAAKd,IAAI,CAACC;IACZ;IAEAc,aAAaC,GAAW,EAAEC,KAAU,EAAE;QACpC,IAAI,CAAC5B,KAAK,CAAC2B,IAAI,GAAGE,OAAOD;IAC3B;IAEAE,QAAWC,EAAqB,EAAK;QACnC,IAAI;YACF,OAAOA,GAAG,IAAI;QAChB,SAAU;YACR,IAAI,CAACpB,IAAI;QACX;IACF;IAEA,MAAMqB,aAAgBD,EAAkC,EAAc;QACpE,IAAI;YACF,OAAO,MAAMA,GAAG,IAAI;QACtB,SAAU;YACR,IAAI,CAACpB,IAAI;QACX;IACF;AACF;AAEO,MAAM/B,QAAQ,CACnBkB,MACAC,UACAC;IAEA,OAAO,IAAIrB,KAAK;QAAEmB;QAAMC;QAAUC;IAAM;AAC1C;AAEO,MAAMnB,iBAAiB,IAAMuC,gBAAQ,CAACa,QAAQ;AAK9C,MAAMnD,mBAAmB,IAAmB,CAAA;QACjDS;QACA2C,QAAQ7C;QACRG;IACF,CAAA;AACO,MAAMT,uBAAuB,CAACoD;IACnC9C,QAAQ8C,MAAMD,MAAM;IACpB3C,sBAAsB4C,MAAM5C,mBAAmB;IAC/CC,wBAAwB2C,MAAM3C,qBAAqB;AACrD;AAEO,SAASR;IACd,OAAOS;AACT;AAEO,SAASR,kBAAkBmD,MAAoB;IACpD,KAAK,MAAMlB,cAAckB,OAAQ;QAC/BhB,gBAAQ,CAACC,MAAM,CAACH;QAChB,IAAIA,WAAWd,EAAE,GAAGf,OAAO;YACzBA,QAAQ6B,WAAWd,EAAE,GAAG;QAC1B;IACF;IACA,IAAIZ,uBAAuB;QACzBC,iBAAiB6B,IAAI,IAAIc;IAC3B;AACF;AAEO,MAAMlD,mBAAmB,IAAOO,mBAAmB,EAAE"}