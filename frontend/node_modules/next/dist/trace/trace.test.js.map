{"version": 3, "sources": ["../../src/trace/trace.test.ts"], "names": ["describe", "beforeEach", "initializeTraceState", "lastId", "shouldSaveTraceEvents", "clearTraceEvents", "it", "tmpDir", "mkdtemp", "join", "tmpdir", "setGlobal", "root", "trace", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "traceAsyncFn", "delayedPromise", "Promise", "resolve", "setTimeout", "stop", "traceEvents", "getTraceEvents", "expect", "length", "toEqual", "name", "flushAllTraces", "traceFilename", "serializedTraces", "JSON", "parse", "readFile", "toMatchObject", "id", "parentId", "startTime", "any", "Number", "timestamp", "duration", "tags", "traceState", "exportTraceState", "span", "clone", "stringify", "defaultParentSpanId", "worker1Span", "worker1Traces", "worker2Span", "worker2Traces", "recordTraceEvents", "allTraces", "firstSpan", "worker1<PERSON><PERSON>d", "worker1Root", "worker2<PERSON><PERSON>d", "worker2Root", "lastChildSpan", "rootSpan", "toBeUndefined"], "mappings": ";;;;0BAAkC;sBACb;oBACE;wBACG;uBASnB;AAEPA,SAAS,SAAS;IAChBC,WAAW;QACTC,IAAAA,2BAAoB,EAAC;YACnBC,QAAQ;YACRC,uBAAuB;QACzB;QACAC,IAAAA,uBAAgB;IAClB;IAEAL,SAAS,UAAU;QACjBM,GAAG,0BAA0B;YAC3B,MAAMC,SAAS,MAAMC,IAAAA,iBAAO,EAACC,IAAAA,UAAI,EAACC,IAAAA,UAAM,KAAI;YAC5CC,IAAAA,iBAAS,EAAC,WAAWJ;YACrBI,IAAAA,iBAAS,EAAC,SAAS;YAEnB,MAAMC,OAAOC,IAAAA,YAAK,EAAC,aAAaC,WAAW;gBACzC,YAAY;YACd;YACAF,KAAKG,UAAU,CAAC,cAAcC,OAAO,CAAC,IAAM;YAC5C,MAAMJ,KAAKG,UAAU,CAAC,oBAAoBE,YAAY,CAAC;gBACrD,MAAMC,iBAAiB,IAAIC,QAAQ,CAACC;oBAClCC,WAAWD,SAAS;gBACtB;gBACA,MAAMF;YACR;YACAN,KAAKU,IAAI;YACT,MAAMC,cAAcC,IAAAA,qBAAc;YAClCC,OAAOF,YAAYG,MAAM,EAAEC,OAAO,CAAC;YACnCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YACpCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YACpCF,OAAOF,WAAW,CAAC,EAAE,CAACK,IAAI,EAAED,OAAO,CAAC;YAEpC,4DAA4D;YAC5D,MAAME,IAAAA,qBAAc;YACpB,MAAMC,gBAAgBrB,IAAAA,UAAI,EAACF,QAAQ;YACnC,MAAMwB,mBAAmBC,KAAKC,KAAK,CACjC,MAAMC,IAAAA,kBAAQ,EAACJ,eAAe;YAEhCL,OAAOM,kBAAkBI,aAAa,CAAC;gBACrC;oBACEC,IAAI;oBACJR,MAAM;oBACNS,UAAU;oBACVC,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM,CAAC;gBACT;gBACA;oBACEP,IAAI;oBACJR,MAAM;oBACNS,UAAU;oBACVC,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM,CAAC;gBACT;gBACA;oBACEP,IAAI;oBACJR,MAAM;oBACNU,WAAWb,OAAOc,GAAG,CAACC;oBACtBC,WAAWhB,OAAOc,GAAG,CAACC;oBACtBE,UAAUjB,OAAOc,GAAG,CAACC;oBACrBG,MAAM;wBACJ,YAAY;oBACd;gBACF;aACD;QACH;IACF;IAEA3C,SAAS,UAAU;QACjBM,GAAG,uCAAuC;YACxC,MAAMM,OAAOC,IAAAA,YAAK,EAAC;YACnBY,OAAOb,KAAKwB,EAAE,EAAET,OAAO,CAAC;YACxB,MAAMiB,aAAaC,IAAAA,uBAAgB;YACnCpB,OAAOmB,WAAWzC,MAAM,EAAEwB,OAAO,CAAC;YAClCzB,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;YACV;YACA,MAAM2C,OAAOjC,IAAAA,YAAK,EAAC;YACnBY,OAAOqB,KAAKV,EAAE,EAAET,OAAO,CAAC;QAC1B;QAEArB,GAAG,0CAA0C;YAC3C,MAAMM,OAAOC,IAAAA,YAAK,EAAC;YACnBD,KAAKG,UAAU,CAAC,cAAcC,OAAO,CAAC,IAAM;YAC5CJ,KAAKU,IAAI;YACT,MAAMC,cAAcC,IAAAA,qBAAc;YAClCC,OAAOF,YAAYG,MAAM,EAAEC,OAAO,CAAC;YACnC,sEAAsE;YACtE,qEAAqE;YACrE,uBAAuB;YACvB,MAAMoB,QAAQf,KAAKC,KAAK,CAACD,KAAKgB,SAAS,CAACzB;YACxCE,OAAOsB,OAAOpB,OAAO,CAACJ;QACxB;QAEAjB,GAAG,sDAAsD;YACvD,mEAAmE;YACnE,yDAAyD;YACzD,mEAAmE;YACnEJ,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACR8C,qBAAqB;gBACrB7C,uBAAuB;YACzB;YACA,MAAM8C,cAAcrC,IAAAA,YAAK,EAAC;YAC1BqC,YAAYnC,UAAU,CAAC,wBAAwBC,OAAO,CAAC,IAAM;YAC7DkC,YAAY5B,IAAI;YAChB,MAAM6B,gBAAgB3B,IAAAA,qBAAc;YACpCC,OAAO0B,cAAczB,MAAM,EAAEC,OAAO,CAAC;YAErC,8BAA8B;YAC9BtB,IAAAA,uBAAgB;YAChBH,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACR8C,qBAAqB;gBACrB7C,uBAAuB;YACzB;YACA,MAAMgD,cAAcvC,IAAAA,YAAK,EAAC;YAC1BuC,YAAYrC,UAAU,CAAC,wBAAwBC,OAAO,CAAC,IAAM;YAC7DoC,YAAY9B,IAAI;YAChB,MAAM+B,gBAAgB7B,IAAAA,qBAAc;YACpCC,OAAO4B,cAAc3B,MAAM,EAAEC,OAAO,CAAC;YAErC,oEAAoE;YACpE,oBAAoB;YACpBtB,IAAAA,uBAAgB;YAChBH,IAAAA,2BAAoB,EAAC;gBACnBC,QAAQ;gBACRC,uBAAuB;YACzB;YACA,MAAMQ,OAAOC,IAAAA,YAAK,EAAC;YACnBD,KAAKG,UAAU,CAAC,mBAAmBC,OAAO,CAAC,IAAM;YACjDsC,IAAAA,wBAAiB,EAACH;YAClB1B,OAAOoB,IAAAA,uBAAgB,IAAG1C,MAAM,EAAEwB,OAAO,CAAC;YAC1C2B,IAAAA,wBAAiB,EAACD;YAClB5B,OAAOoB,IAAAA,uBAAgB,IAAG1C,MAAM,EAAEwB,OAAO,CAAC;YAC1Cf,KAAKG,UAAU,CAAC,sBAAsBC,OAAO,CAAC,IAAM;YACpDJ,KAAKU,IAAI;YAET,6CAA6C;YAC7C,MAAMiC,YAAY/B,IAAAA,qBAAc;YAChCC,OAAO8B,UAAU7B,MAAM,EAAEC,OAAO,CAAC;YACjC,MAAM6B,YAAYD,SAAS,CAAC,EAAE;YAC9B9B,OAAO+B,UAAU5B,IAAI,EAAED,OAAO,CAAC;YAC/BF,OAAO+B,UAAUpB,EAAE,EAAET,OAAO,CAAC;YAC7BF,OAAO+B,UAAUnB,QAAQ,EAAEV,OAAO,CAAC;YAEnC,MAAM8B,eAAeF,SAAS,CAAC,EAAE;YACjC9B,OAAOgC,aAAa7B,IAAI,EAAED,OAAO,CAAC;YAClCF,OAAOgC,aAAarB,EAAE,EAAET,OAAO,CAAC;YAChCF,OAAOgC,aAAapB,QAAQ,EAAEV,OAAO,CAAC;YACtC,MAAM+B,cAAcH,SAAS,CAAC,EAAE;YAChC9B,OAAOiC,YAAY9B,IAAI,EAAED,OAAO,CAAC;YACjCF,OAAOiC,YAAYtB,EAAE,EAAET,OAAO,CAAC;YAC/BF,OAAOiC,YAAYrB,QAAQ,EAAEV,OAAO,CAAC;YAErC,MAAMgC,eAAeJ,SAAS,CAAC,EAAE;YACjC9B,OAAOkC,aAAa/B,IAAI,EAAED,OAAO,CAAC;YAClCF,OAAOkC,aAAavB,EAAE,EAAET,OAAO,CAAC;YAChCF,OAAOkC,aAAatB,QAAQ,EAAEV,OAAO,CAAC;YACtC,MAAMiC,cAAcL,SAAS,CAAC,EAAE;YAChC9B,OAAOmC,YAAYhC,IAAI,EAAED,OAAO,CAAC;YACjCF,OAAOmC,YAAYxB,EAAE,EAAET,OAAO,CAAC;YAC/BF,OAAOmC,YAAYvB,QAAQ,EAAEV,OAAO,CAAC;YAErC,MAAMkC,gBAAgBN,SAAS,CAAC,EAAE;YAClC9B,OAAOoC,cAAcjC,IAAI,EAAED,OAAO,CAAC;YACnCF,OAAOoC,cAAczB,EAAE,EAAET,OAAO,CAAC;YACjCF,OAAOoC,cAAcxB,QAAQ,EAAEV,OAAO,CAAC;YAEvC,MAAMmC,WAAWP,SAAS,CAAC,EAAE;YAC7B9B,OAAOqC,SAASlC,IAAI,EAAED,OAAO,CAAC;YAC9BF,OAAOqC,SAAS1B,EAAE,EAAET,OAAO,CAAC;YAC5BF,OAAOqC,SAASzB,QAAQ,EAAE0B,aAAa;QACzC;IACF;AACF"}