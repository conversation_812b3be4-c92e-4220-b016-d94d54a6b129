'use client'

import { useState, useEffect, useCallback } from 'react'

interface MonitoringData {
  status: string
  monitoring_active: boolean
  uptime_hours: number
  monitored_symbols: string[]
  session_statistics: {
    total_patterns_detected: number
    total_alerts_generated: number
    detection_cycles_completed: number
    error_count: number
    detection_rate_per_hour: number
  }
  current_metrics: {
    memory_usage_mb: number
    cpu_usage_percent: number
    avg_analysis_time_ms: number
  }
}

interface AlertData {
  alert_level: string
  pattern_type: string
  symbol: string
  confidence_score: number
  detection_timestamp: string
  message: string
}

export function useRealTimeMonitoring() {
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(null)
  const [alerts, setAlerts] = useState<AlertData[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchMonitoringStatus = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/monitoring/status')
      if (response.ok) {
        const data = await response.json()
        setMonitoringData(data)
        setError(null)
        setIsConnected(true)
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch monitoring status')
      setIsConnected(false)
    }
  }, [])

  const fetchAlerts = useCallback(async () => {
    try {
      // In a real implementation, this would fetch recent alerts from the database
      // For now, we'll simulate alerts based on monitoring activity
      if (monitoringData?.session_statistics.total_alerts_generated > alerts.length) {
        const newAlert: AlertData = {
          alert_level: 'medium',
          pattern_type: 'whale_accumulation',
          symbol: 'bitcoin',
          confidence_score: 0.75,
          detection_timestamp: new Date().toISOString(),
          message: 'Whale accumulation pattern detected for Bitcoin with 75% confidence'
        }
        setAlerts(prev => [newAlert, ...prev.slice(0, 9)]) // Keep last 10 alerts
      }
    } catch (err) {
      console.error('Error fetching alerts:', err)
    }
  }, [monitoringData, alerts.length])

  // Real-time polling (in production, this would use WebSockets)
  useEffect(() => {
    fetchMonitoringStatus()
    
    const statusInterval = setInterval(fetchMonitoringStatus, 5000) // Every 5 seconds
    const alertsInterval = setInterval(fetchAlerts, 10000) // Every 10 seconds

    return () => {
      clearInterval(statusInterval)
      clearInterval(alertsInterval)
    }
  }, [fetchMonitoringStatus, fetchAlerts])

  const startMonitoring = useCallback(async (config: any) => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/monitoring/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      })
      
      if (response.ok) {
        await fetchMonitoringStatus()
        return { success: true }
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (err) {
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to start monitoring' 
      }
    }
  }, [fetchMonitoringStatus])

  const stopMonitoring = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/monitoring/stop', {
        method: 'POST',
      })
      
      if (response.ok) {
        await fetchMonitoringStatus()
        return { success: true }
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (err) {
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to stop monitoring' 
      }
    }
  }, [fetchMonitoringStatus])

  const runPatternAnalysis = useCallback(async (symbols: string[]) => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/patterns/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbols,
          lookback_hours: 6,
          min_confidence: 0.5,
          real_time: false
        }),
      })
      
      if (response.ok) {
        const data = await response.json()
        return { success: true, data }
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (err) {
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to run pattern analysis' 
      }
    }
  }, [])

  const updateAlertConfig = useCallback(async (level: string, config: any) => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/monitoring/alerts/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          alert_level: level,
          ...config
        }),
      })
      
      if (response.ok) {
        return { success: true }
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (err) {
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update alert config' 
      }
    }
  }, [])

  return {
    monitoringData,
    alerts,
    isConnected,
    error,
    actions: {
      startMonitoring,
      stopMonitoring,
      runPatternAnalysis,
      updateAlertConfig,
      refreshStatus: fetchMonitoringStatus
    }
  }
}
