'use client'

import { useState, useEffect } from 'react'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  HeartIcon,
  ServerIcon,
  CircleStackIcon,
  BellIcon
} from '@heroicons/react/24/outline'

interface SystemHealthPanelProps {
  monitoringStatus: any
}

interface HealthData {
  status: string
  components: {
    monitoring_service: string
    pattern_detection: string
    database: string
    alert_system: string
    overall_status: string
  }
  monitoring_active: boolean
  uptime_seconds: number
}

interface PerformanceData {
  status: string
  health_score: number
  health_status: string
  performance_indicators: {
    uptime_hours: number
    patterns_per_hour: number
    avg_analysis_time_ms: number
    memory_efficiency: string
    cpu_efficiency: string
  }
  recommendations: string[]
}

export default function SystemHealthPanel({ monitoringStatus }: SystemHealthPanelProps) {
  const [healthData, setHealthData] = useState<HealthData | null>(null)
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchHealthData = async () => {
    try {
      const [healthResponse, performanceResponse] = await Promise.all([
        fetch('http://localhost:8000/api/v1/monitoring/health'),
        fetch('http://localhost:8000/api/v1/monitoring/performance')
      ])

      if (healthResponse.ok) {
        const health = await healthResponse.json()
        setHealthData(health)
      }

      if (performanceResponse.ok) {
        const performance = await performanceResponse.json()
        setPerformanceData(performance)
      }
    } catch (error) {
      console.error('Error fetching health data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchHealthData()
    const interval = setInterval(fetchHealthData, 10000) // Refresh every 10 seconds
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'active':
      case 'operational':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'warning':
      case 'inactive':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
      case 'error':
      case 'unhealthy':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'active':
      case 'operational':
        return 'text-green-600'
      case 'warning':
      case 'inactive':
        return 'text-yellow-600'
      case 'error':
      case 'unhealthy':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getHealthScoreColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getHealthScoreBg = (score: number) => {
    if (score >= 0.9) return 'bg-green-100 dark:bg-green-900/20'
    if (score >= 0.7) return 'bg-yellow-100 dark:bg-yellow-900/20'
    return 'bg-red-100 dark:bg-red-900/20'
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
            <HeartIcon className="h-5 w-5 mr-2 text-red-500" />
            System Health
          </h3>
          {performanceData && (
            <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${getHealthScoreBg(performanceData.health_score)}`}>
              <span className={getHealthScoreColor(performanceData.health_score)}>
                Health Score: {(performanceData.health_score * 100).toFixed(0)}%
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* System Components */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              System Components
            </h4>
            <div className="space-y-3">
              {healthData && Object.entries(healthData.components).map(([component, status]) => {
                const componentNames: { [key: string]: { name: string; icon: any } } = {
                  monitoring_service: { name: 'Monitoring Service', icon: ServerIcon },
                  pattern_detection: { name: 'Pattern Detection', icon: HeartIcon },
                  database: { name: 'Database', icon: CircleStackIcon },
                  alert_system: { name: 'Alert System', icon: BellIcon },
                  overall_status: { name: 'Overall Status', icon: CheckCircleIcon }
                }

                const componentInfo = componentNames[component] || { name: component, icon: ServerIcon }
                const Icon = componentInfo.icon

                return (
                  <div key={component} className="flex items-center justify-between py-2">
                    <div className="flex items-center">
                      <Icon className="h-4 w-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-900 dark:text-white">
                        {componentInfo.name}
                      </span>
                    </div>
                    <div className="flex items-center">
                      {getStatusIcon(status)}
                      <span className={`ml-2 text-sm font-medium ${getStatusColor(status)}`}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Performance Indicators */}
          {performanceData && performanceData.status === 'active' && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                Performance Indicators
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Uptime</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {performanceData.performance_indicators.uptime_hours.toFixed(2)} hours
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Patterns/Hour</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {performanceData.performance_indicators.patterns_per_hour.toFixed(1)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Avg Analysis Time</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {performanceData.performance_indicators.avg_analysis_time_ms.toFixed(1)}ms
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Memory Efficiency</span>
                  <span className={`text-sm font-medium ${
                    performanceData.performance_indicators.memory_efficiency === 'good' 
                      ? 'text-green-600' 
                      : 'text-yellow-600'
                  }`}>
                    {performanceData.performance_indicators.memory_efficiency}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">CPU Efficiency</span>
                  <span className={`text-sm font-medium ${
                    performanceData.performance_indicators.cpu_efficiency === 'good' 
                      ? 'text-green-600' 
                      : 'text-yellow-600'
                  }`}>
                    {performanceData.performance_indicators.cpu_efficiency}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Recommendations */}
        {performanceData && performanceData.recommendations && performanceData.recommendations.length > 0 && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Recommendations
            </h4>
            <ul className="space-y-2">
              {performanceData.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start">
                  <div className="flex-shrink-0 w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-3"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {recommendation}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Last Updated
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {new Date().toLocaleTimeString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
