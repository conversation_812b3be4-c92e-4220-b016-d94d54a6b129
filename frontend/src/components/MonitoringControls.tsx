'use client'

import { useState } from 'react'
import {
  PlayIcon,
  StopIcon,
  CogIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

interface MonitoringControlsProps {
  monitoringStatus: any
  onStatusChange: () => void
  startMonitoring: (config: any) => Promise<{ success: boolean; error?: string }>
  stopMonitoring: () => Promise<{ success: boolean; error?: string }>
}

export default function MonitoringControls({
  monitoringStatus,
  onStatusChange,
  startMonitoring: startMonitoringAction,
  stopMonitoring: stopMonitoringAction
}: MonitoringControlsProps) {
  const [isStarting, setIsStarting] = useState(false)
  const [isStopping, setIsStopping] = useState(false)
  const [showConfig, setShowConfig] = useState(false)
  const [config, setConfig] = useState({
    symbols: ['bitcoin', 'ethereum', 'solana', 'cardano'],
    monitoring_interval_seconds: 300,
    alert_levels: {
      critical: 0.9,
      high: 0.8,
      medium: 0.7
    }
  })

  const startMonitoring = async () => {
    setIsStarting(true)
    try {
      const result = await startMonitoringAction(config)
      if (result.success) {
        onStatusChange()
      } else {
        console.error('Failed to start monitoring:', result.error)
      }
    } catch (error) {
      console.error('Error starting monitoring:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const stopMonitoring = async () => {
    setIsStopping(true)
    try {
      const result = await stopMonitoringAction()
      if (result.success) {
        onStatusChange()
      } else {
        console.error('Failed to stop monitoring:', result.error)
      }
    } catch (error) {
      console.error('Error stopping monitoring:', error)
    } finally {
      setIsStopping(false)
    }
  }

  const addSymbol = () => {
    setConfig(prev => ({
      ...prev,
      symbols: [...prev.symbols, '']
    }))
  }

  const removeSymbol = (index: number) => {
    setConfig(prev => ({
      ...prev,
      symbols: prev.symbols.filter((_, i) => i !== index)
    }))
  }

  const updateSymbol = (index: number, value: string) => {
    setConfig(prev => ({
      ...prev,
      symbols: prev.symbols.map((symbol, i) => i === index ? value : symbol)
    }))
  }

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Monitoring Controls
          </h3>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowConfig(!showConfig)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <CogIcon className="h-4 w-4 mr-2" />
              Configure
            </button>
            
            <button
              onClick={onStatusChange}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Refresh
            </button>

            {monitoringStatus?.monitoring_active ? (
              <button
                onClick={stopMonitoring}
                disabled={isStopping}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              >
                {isStopping ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <StopIcon className="h-4 w-4 mr-2" />
                )}
                Stop Monitoring
              </button>
            ) : (
              <button
                onClick={startMonitoring}
                disabled={isStarting}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                {isStarting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <PlayIcon className="h-4 w-4 mr-2" />
                )}
                Start Monitoring
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Configuration Panel */}
      {showConfig && (
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
          <div className="space-y-6">
            {/* Symbols Configuration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Monitored Symbols
              </label>
              <div className="space-y-2">
                {config.symbols.map((symbol, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={symbol}
                      onChange={(e) => updateSymbol(index, e.target.value)}
                      className="flex-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm"
                      placeholder="e.g., bitcoin"
                    />
                    <button
                      onClick={() => removeSymbol(index)}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  onClick={addSymbol}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Add Symbol
                </button>
              </div>
            </div>

            {/* Monitoring Interval */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Monitoring Interval (seconds)
              </label>
              <input
                type="number"
                value={config.monitoring_interval_seconds}
                onChange={(e) => setConfig(prev => ({ ...prev, monitoring_interval_seconds: parseInt(e.target.value) }))}
                min="60"
                max="3600"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm"
              />
            </div>

            {/* Alert Thresholds */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Alert Confidence Thresholds
              </label>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                    Critical
                  </label>
                  <input
                    type="number"
                    value={config.alert_levels.critical}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      alert_levels: { ...prev.alert_levels, critical: parseFloat(e.target.value) }
                    }))}
                    min="0"
                    max="1"
                    step="0.1"
                    className="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                    High
                  </label>
                  <input
                    type="number"
                    value={config.alert_levels.high}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      alert_levels: { ...prev.alert_levels, high: parseFloat(e.target.value) }
                    }))}
                    min="0"
                    max="1"
                    step="0.1"
                    className="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                    Medium
                  </label>
                  <input
                    type="number"
                    value={config.alert_levels.medium}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      alert_levels: { ...prev.alert_levels, medium: parseFloat(e.target.value) }
                    }))}
                    min="0"
                    max="1"
                    step="0.1"
                    className="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status Information */}
      <div className="px-6 py-4">
        {monitoringStatus ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                {monitoringStatus.monitoring_active ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Inactive
                  </span>
                )}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Monitored Symbols</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                {monitoringStatus.monitored_symbols?.length || 0} symbols
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Uptime</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                {monitoringStatus.uptime_hours?.toFixed(2) || 0} hours
              </dd>
            </div>
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Loading monitoring status...
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
