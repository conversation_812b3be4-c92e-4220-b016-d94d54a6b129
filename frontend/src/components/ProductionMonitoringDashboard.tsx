'use client'

import { useState, useEffect } from 'react'
import {
  Play<PERSON>con,
  StopIcon,
  ChartBarIcon,
  BellIcon,
  CpuChipIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  WifiIcon
} from '@heroicons/react/24/outline'
import MonitoringControls from './MonitoringControls'
import PatternDetectionPanel from './PatternDetectionPanel'
import AlertManagementPanel from './AlertManagementPanel'
import PerformanceMetricsPanel from './PerformanceMetricsPanel'
import SystemHealthPanel from './SystemHealthPanel'
import { useRealTimeMonitoring } from '@/hooks/useRealTimeMonitoring'

interface MonitoringStatus {
  status: string
  monitoring_active: boolean
  uptime_hours: number
  monitored_symbols: string[]
  session_statistics: {
    total_patterns_detected: number
    total_alerts_generated: number
    detection_cycles_completed: number
    error_count: number
    detection_rate_per_hour: number
  }
  current_metrics: {
    memory_usage_mb: number
    cpu_usage_percent: number
    avg_analysis_time_ms: number
  }
}

export default function ProductionMonitoringDashboard() {
  const [activeTab, setActiveTab] = useState('overview')
  const { monitoringData, alerts, isConnected, error, actions } = useRealTimeMonitoring()

  // Use the real-time monitoring data
  const monitoringStatus = monitoringData
  const isLoading = !monitoringData && !error

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'patterns', name: 'Pattern Detection', icon: CpuChipIcon },
    { id: 'alerts', name: 'Alert Management', icon: BellIcon },
    { id: 'performance', name: 'Performance', icon: ClockIcon },
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <CpuChipIcon className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Quantum Market Intelligence
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Production Monitoring Dashboard
                </p>
              </div>
            </div>
            
            {/* System Status Indicator */}
            <div className="flex items-center space-x-4">
              {/* Connection Status */}
              <div className="flex items-center">
                <WifiIcon className={`h-4 w-4 mr-2 ${isConnected ? 'text-green-500' : 'text-red-500'}`} />
                <span className={`text-xs ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>

              {error ? (
                <div className="flex items-center text-red-600">
                  <XCircleIcon className="h-5 w-5 mr-2" />
                  <span className="text-sm font-medium">Connection Error</span>
                </div>
              ) : monitoringStatus?.monitoring_active ? (
                <div className="flex items-center text-green-600">
                  <CheckCircleIcon className="h-5 w-5 mr-2" />
                  <span className="text-sm font-medium">Monitoring Active</span>
                </div>
              ) : (
                <div className="flex items-center text-yellow-600">
                  <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                  <span className="text-sm font-medium">Monitoring Inactive</span>
                </div>
              )}

              {monitoringStatus && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Uptime: {monitoringStatus.uptime_hours.toFixed(2)}h
                </div>
              )}

              {/* Live Alert Indicator */}
              {alerts.length > 0 && (
                <div className="flex items-center text-orange-600">
                  <BellIcon className="h-5 w-5 mr-2 animate-pulse" />
                  <span className="text-sm font-medium">{alerts.length} Alert{alerts.length > 1 ? 's' : ''}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              )
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
              <XCircleIcon className="h-5 w-5 text-red-400 mr-2" />
              <div>
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Connection Error
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {error}. Make sure the backend server is running on localhost:8000.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <SystemHealthPanel monitoringStatus={monitoringStatus} />
            <MonitoringControls
              monitoringStatus={monitoringStatus}
              onStatusChange={actions.refreshStatus}
              startMonitoring={actions.startMonitoring}
              stopMonitoring={actions.stopMonitoring}
            />
            
            {/* Quick Stats Grid */}
            {monitoringStatus && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <CpuChipIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Patterns Detected
                          </dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">
                            {monitoringStatus.session_statistics.total_patterns_detected}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <BellIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Alerts Generated
                          </dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">
                            {monitoringStatus.session_statistics.total_alerts_generated}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ClockIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Detection Cycles
                          </dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">
                            {monitoringStatus.session_statistics.detection_cycles_completed}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ChartBarIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Detection Rate
                          </dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">
                            {monitoringStatus.session_statistics.detection_rate_per_hour.toFixed(1)}/h
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'patterns' && (
          <PatternDetectionPanel monitoringStatus={monitoringStatus} />
        )}

        {activeTab === 'alerts' && (
          <AlertManagementPanel />
        )}

        {activeTab === 'performance' && (
          <PerformanceMetricsPanel />
        )}
      </main>
    </div>
  )
}
