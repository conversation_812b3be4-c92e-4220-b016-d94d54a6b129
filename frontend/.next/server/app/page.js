/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Far%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Far%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/ar/frontend/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/ar/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/ar/frontend/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Far%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp%2Fpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp%2Fpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmFyJTJGZnJvbnRlbmQlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdWFudHVtLW5leHVzLWZyb250ZW5kLz8wZjk3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3MuY2h5L2FyL2Zyb250ZW5kL2FwcC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ProductionMonitoringDashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ProductionMonitoringDashboard */ \"(ssr)/./src/components/ProductionMonitoringDashboard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductionMonitoringDashboard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"/Users/<USER>/ar/frontend/app/page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/ar/frontend/app/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFHc0Y7QUFFdkUsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUtDLFdBQVU7a0JBQ2QsNEVBQUNILGlGQUE2QkE7Ozs7Ozs7Ozs7QUFHcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdWFudHVtLW5leHVzLWZyb250ZW5kLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBQcm9kdWN0aW9uTW9uaXRvcmluZ0Rhc2hib2FyZCBmcm9tICdAL2NvbXBvbmVudHMvUHJvZHVjdGlvbk1vbml0b3JpbmdEYXNoYm9hcmQnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPG1haW4gY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxuICAgICAgPFByb2R1Y3Rpb25Nb25pdG9yaW5nRGFzaGJvYXJkIC8+XG4gICAgPC9tYWluPlxuICApXG59XG4iXSwibmFtZXMiOlsiUHJvZHVjdGlvbk1vbml0b3JpbmdEYXNoYm9hcmQiLCJIb21lIiwibWFpbiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AlertManagementPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/AlertManagementPanel.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AlertManagementPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AlertManagementPanel() {\n    const [alertConfig, setAlertConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchAlertConfig = async ()=>{\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/monitoring/alerts/config\");\n            if (response.ok) {\n                const data = await response.json();\n                setAlertConfig(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching alert config:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateAlertConfig = async (level, updates)=>{\n        setIsSaving(true);\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/monitoring/alerts/config\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    alert_level: level,\n                    ...updates\n                })\n            });\n            if (response.ok) {\n                await fetchAlertConfig() // Refresh config\n                ;\n            }\n        } catch (error) {\n            console.error(\"Error updating alert config:\", error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchAlertConfig();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 shadow rounded-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                \"Alert Configuration\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: alertConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: Object.entries(alertConfig.alert_levels).map(([level, config])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${level === \"critical\" ? \"bg-red-100 text-red-800\" : level === \"high\" ? \"bg-orange-100 text-orange-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                                            children: level.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-sm font-medium text-gray-900 dark:text-white\",\n                                                            children: \"Alert Level\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        config.enabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `ml-2 text-sm ${config.enabled ? \"text-green-600\" : \"text-gray-500\"}`,\n                                                            children: config.enabled ? \"Enabled\" : \"Disabled\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                            children: \"Confidence Threshold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: config.confidence_threshold,\n                                                            onChange: (e)=>updateAlertConfig(level, {\n                                                                    confidence_threshold: parseFloat(e.target.value)\n                                                                }),\n                                                            min: \"0\",\n                                                            max: \"1\",\n                                                            step: \"0.05\",\n                                                            className: \"block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm\",\n                                                            disabled: isSaving\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                            children: [\n                                                                (config.confidence_threshold * 100).toFixed(0),\n                                                                \"% confidence required\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                            children: \"Cooldown Period\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: config.cooldown_minutes,\n                                                            onChange: (e)=>updateAlertConfig(level, {\n                                                                    cooldown_minutes: parseInt(e.target.value)\n                                                                }),\n                                                            min: \"1\",\n                                                            max: \"60\",\n                                                            className: \"block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm\",\n                                                            disabled: isSaving\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                            children: [\n                                                                config.cooldown_minutes,\n                                                                \" minutes between alerts\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>updateAlertConfig(level, {\n                                                                    enabled: !config.enabled\n                                                                }),\n                                                            disabled: isSaving,\n                                                            className: `w-full inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${config.enabled ? \"text-white bg-red-600 hover:bg-red-700 focus:ring-red-500\" : \"text-white bg-green-600 hover:bg-green-700 focus:ring-green-500\"}`,\n                                                            children: config.enabled ? \"Disable\" : \"Enable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, level, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-yellow-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                \"Alert Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: alertConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: alertConfig.total_alerts_generated || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Total Alerts Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: alertConfig.active_cooldowns || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Active Cooldowns\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: \"Recent Alerts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: \"No recent alerts\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 dark:text-gray-500 mt-1\",\n                                    children: \"Alerts will appear here when patterns are detected above configured thresholds\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/ar/frontend/src/components/AlertManagementPanel.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AlertManagementPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MonitoringControls.tsx":
/*!***********************************************!*\
  !*** ./src/components/MonitoringControls.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MonitoringControls)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MonitoringControls({ monitoringStatus, onStatusChange, startMonitoring: startMonitoringAction, stopMonitoring: stopMonitoringAction }) {\n    const [isStarting, setIsStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStopping, setIsStopping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfig, setShowConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        symbols: [\n            \"bitcoin\",\n            \"ethereum\",\n            \"solana\",\n            \"cardano\"\n        ],\n        monitoring_interval_seconds: 300,\n        alert_levels: {\n            critical: 0.9,\n            high: 0.8,\n            medium: 0.7\n        }\n    });\n    const startMonitoring = async ()=>{\n        setIsStarting(true);\n        try {\n            const result = await startMonitoringAction(config);\n            if (result.success) {\n                onStatusChange();\n            } else {\n                console.error(\"Failed to start monitoring:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"Error starting monitoring:\", error);\n        } finally{\n            setIsStarting(false);\n        }\n    };\n    const stopMonitoring = async ()=>{\n        setIsStopping(true);\n        try {\n            const result = await stopMonitoringAction();\n            if (result.success) {\n                onStatusChange();\n            } else {\n                console.error(\"Failed to stop monitoring:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"Error stopping monitoring:\", error);\n        } finally{\n            setIsStopping(false);\n        }\n    };\n    const addSymbol = ()=>{\n        setConfig((prev)=>({\n                ...prev,\n                symbols: [\n                    ...prev.symbols,\n                    \"\"\n                ]\n            }));\n    };\n    const removeSymbol = (index)=>{\n        setConfig((prev)=>({\n                ...prev,\n                symbols: prev.symbols.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateSymbol = (index, value)=>{\n        setConfig((prev)=>({\n                ...prev,\n                symbols: prev.symbols.map((symbol, i)=>i === index ? value : symbol)\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: \"Monitoring Controls\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowConfig(!showConfig),\n                                    className: \"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Configure\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onStatusChange,\n                                    className: \"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                monitoringStatus?.monitoring_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stopMonitoring,\n                                    disabled: isStopping,\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50\",\n                                    children: [\n                                        isStopping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Stop Monitoring\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startMonitoring,\n                                    disabled: isStarting,\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50\",\n                                    children: [\n                                        isStarting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Start Monitoring\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            showConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Monitored Symbols\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        config.symbols.map((symbol, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: symbol,\n                                                        onChange: (e)=>updateSymbol(index, e.target.value),\n                                                        className: \"flex-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm\",\n                                                        placeholder: \"e.g., bitcoin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeSymbol(index),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addSymbol,\n                                            className: \"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: \"Add Symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Monitoring Interval (seconds)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: config.monitoring_interval_seconds,\n                                    onChange: (e)=>setConfig((prev)=>({\n                                                ...prev,\n                                                monitoring_interval_seconds: parseInt(e.target.value)\n                                            })),\n                                    min: \"60\",\n                                    max: \"3600\",\n                                    className: \"block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Alert Confidence Thresholds\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1\",\n                                                    children: \"Critical\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.alert_levels.critical,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                alert_levels: {\n                                                                    ...prev.alert_levels,\n                                                                    critical: parseFloat(e.target.value)\n                                                                }\n                                                            })),\n                                                    min: \"0\",\n                                                    max: \"1\",\n                                                    step: \"0.1\",\n                                                    className: \"block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1\",\n                                                    children: \"High\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.alert_levels.high,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                alert_levels: {\n                                                                    ...prev.alert_levels,\n                                                                    high: parseFloat(e.target.value)\n                                                                }\n                                                            })),\n                                                    min: \"0\",\n                                                    max: \"1\",\n                                                    step: \"0.1\",\n                                                    className: \"block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.alert_levels.medium,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                alert_levels: {\n                                                                    ...prev.alert_levels,\n                                                                    medium: parseFloat(e.target.value)\n                                                                }\n                                                            })),\n                                                    min: \"0\",\n                                                    max: \"1\",\n                                                    step: \"0.1\",\n                                                    className: \"block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4\",\n                children: monitoringStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                    className: \"mt-1 text-sm text-gray-900 dark:text-white\",\n                                    children: monitoringStatus.monitoring_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                        children: \"Active\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                        children: \"Inactive\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                    children: \"Monitored Symbols\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                    className: \"mt-1 text-sm text-gray-900 dark:text-white\",\n                                    children: [\n                                        monitoringStatus.monitored_symbols?.length || 0,\n                                        \" symbols\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                    children: \"Uptime\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                    className: \"mt-1 text-sm text-gray-900 dark:text-white\",\n                                    children: [\n                                        monitoringStatus.uptime_hours?.toFixed(2) || 0,\n                                        \" hours\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                        children: \"Loading monitoring status...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/ar/frontend/src/components/MonitoringControls.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Nb25pdG9yaW5nQ29udHJvbHMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFNSTtBQVNyQixTQUFTSyxtQkFBbUIsRUFDekNDLGdCQUFnQixFQUNoQkMsY0FBYyxFQUNkQyxpQkFBaUJDLHFCQUFxQixFQUN0Q0MsZ0JBQWdCQyxvQkFBb0IsRUFDWjtJQUN4QixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2IsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYyxZQUFZQyxjQUFjLEdBQUdmLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2dCLFlBQVlDLGNBQWMsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2tCLFFBQVFDLFVBQVUsR0FBR25CLCtDQUFRQSxDQUFDO1FBQ25Db0IsU0FBUztZQUFDO1lBQVc7WUFBWTtZQUFVO1NBQVU7UUFDckRDLDZCQUE2QjtRQUM3QkMsY0FBYztZQUNaQyxVQUFVO1lBQ1ZDLE1BQU07WUFDTkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxNQUFNakIsa0JBQWtCO1FBQ3RCSyxjQUFjO1FBQ2QsSUFBSTtZQUNGLE1BQU1hLFNBQVMsTUFBTWpCLHNCQUFzQlM7WUFDM0MsSUFBSVEsT0FBT0MsT0FBTyxFQUFFO2dCQUNsQnBCO1lBQ0YsT0FBTztnQkFDTHFCLFFBQVFDLEtBQUssQ0FBQywrQkFBK0JILE9BQU9HLEtBQUs7WUFDM0Q7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEQsUUFBUUMsS0FBSyxDQUFDLDhCQUE4QkE7UUFDOUMsU0FBVTtZQUNSaEIsY0FBYztRQUNoQjtJQUNGO0lBRUEsTUFBTUgsaUJBQWlCO1FBQ3JCSyxjQUFjO1FBQ2QsSUFBSTtZQUNGLE1BQU1XLFNBQVMsTUFBTWY7WUFDckIsSUFBSWUsT0FBT0MsT0FBTyxFQUFFO2dCQUNsQnBCO1lBQ0YsT0FBTztnQkFDTHFCLFFBQVFDLEtBQUssQ0FBQyw4QkFBOEJILE9BQU9HLEtBQUs7WUFDMUQ7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEQsUUFBUUMsS0FBSyxDQUFDLDhCQUE4QkE7UUFDOUMsU0FBVTtZQUNSZCxjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFNZSxZQUFZO1FBQ2hCWCxVQUFVWSxDQUFBQSxPQUFTO2dCQUNqQixHQUFHQSxJQUFJO2dCQUNQWCxTQUFTO3VCQUFJVyxLQUFLWCxPQUFPO29CQUFFO2lCQUFHO1lBQ2hDO0lBQ0Y7SUFFQSxNQUFNWSxlQUFlLENBQUNDO1FBQ3BCZCxVQUFVWSxDQUFBQSxPQUFTO2dCQUNqQixHQUFHQSxJQUFJO2dCQUNQWCxTQUFTVyxLQUFLWCxPQUFPLENBQUNjLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxNQUFNSDtZQUMvQztJQUNGO0lBRUEsTUFBTUksZUFBZSxDQUFDSixPQUFlSztRQUNuQ25CLFVBQVVZLENBQUFBLE9BQVM7Z0JBQ2pCLEdBQUdBLElBQUk7Z0JBQ1BYLFNBQVNXLEtBQUtYLE9BQU8sQ0FBQ21CLEdBQUcsQ0FBQyxDQUFDQyxRQUFRSixJQUFNQSxNQUFNSCxRQUFRSyxRQUFRRTtZQUNqRTtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBb0Q7Ozs7OztzQ0FHbEUsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQ0NDLFNBQVMsSUFBTTVCLGNBQWMsQ0FBQ0Q7b0NBQzlCMEIsV0FBVTs7c0RBRVYsOERBQUN2QyxpSUFBT0E7NENBQUN1QyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUl0Qyw4REFBQ0U7b0NBQ0NDLFNBQVN0QztvQ0FDVG1DLFdBQVU7O3NEQUVWLDhEQUFDdEMsaUlBQWFBOzRDQUFDc0MsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7OztnQ0FJM0NwQyxrQkFBa0J3QyxrQ0FDakIsOERBQUNGO29DQUNDQyxTQUFTbkM7b0NBQ1RxQyxVQUFVakM7b0NBQ1Y0QixXQUFVOzt3Q0FFVDVCLDJCQUNDLDhEQUFDMkI7NENBQUlDLFdBQVU7Ozs7O2lFQUVmLDhEQUFDeEMsaUlBQVFBOzRDQUFDd0MsV0FBVTs7Ozs7O3dDQUNwQjs7Ozs7O3lEQUlKLDhEQUFDRTtvQ0FDQ0MsU0FBU3JDO29DQUNUdUMsVUFBVW5DO29DQUNWOEIsV0FBVTs7d0NBRVQ5QiwyQkFDQyw4REFBQzZCOzRDQUFJQyxXQUFVOzs7OztpRUFFZiw4REFBQ3pDLGlJQUFRQTs0Q0FBQ3lDLFdBQVU7Ozs7Ozt3Q0FDcEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNYMUIsNEJBQ0MsOERBQUN5QjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs7OENBQ0MsOERBQUNPO29DQUFNTixXQUFVOzhDQUFrRTs7Ozs7OzhDQUduRiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3dDQUNaeEIsT0FBT0UsT0FBTyxDQUFDbUIsR0FBRyxDQUFDLENBQUNDLFFBQVFQLHNCQUMzQiw4REFBQ1E7Z0RBQWdCQyxXQUFVOztrRUFDekIsOERBQUNPO3dEQUNDQyxNQUFLO3dEQUNMWixPQUFPRTt3REFDUFcsVUFBVSxDQUFDQyxJQUFNZixhQUFhSixPQUFPbUIsRUFBRUMsTUFBTSxDQUFDZixLQUFLO3dEQUNuREksV0FBVTt3REFDVlksYUFBWTs7Ozs7O2tFQUVkLDhEQUFDVjt3REFDQ0MsU0FBUyxJQUFNYixhQUFhQzt3REFDNUJTLFdBQVU7a0VBQ1g7Ozs7Ozs7K0NBWE9UOzs7OztzREFnQlosOERBQUNXOzRDQUNDQyxTQUFTZjs0Q0FDVFksV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9MLDhEQUFDRDs7OENBQ0MsOERBQUNPO29DQUFNTixXQUFVOzhDQUFrRTs7Ozs7OzhDQUduRiw4REFBQ087b0NBQ0NDLE1BQUs7b0NBQ0xaLE9BQU9wQixPQUFPRywyQkFBMkI7b0NBQ3pDOEIsVUFBVSxDQUFDQyxJQUFNakMsVUFBVVksQ0FBQUEsT0FBUztnREFBRSxHQUFHQSxJQUFJO2dEQUFFViw2QkFBNkJrQyxTQUFTSCxFQUFFQyxNQUFNLENBQUNmLEtBQUs7NENBQUU7b0NBQ3JHa0IsS0FBSTtvQ0FDSkMsS0FBSTtvQ0FDSmYsV0FBVTs7Ozs7Ozs7Ozs7O3NDQUtkLDhEQUFDRDs7OENBQ0MsOERBQUNPO29DQUFNTixXQUFVOzhDQUFrRTs7Ozs7OzhDQUduRiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUNPO29EQUFNTixXQUFVOzhEQUFrRTs7Ozs7OzhEQUduRiw4REFBQ087b0RBQ0NDLE1BQUs7b0RBQ0xaLE9BQU9wQixPQUFPSSxZQUFZLENBQUNDLFFBQVE7b0RBQ25DNEIsVUFBVSxDQUFDQyxJQUFNakMsVUFBVVksQ0FBQUEsT0FBUztnRUFDbEMsR0FBR0EsSUFBSTtnRUFDUFQsY0FBYztvRUFBRSxHQUFHUyxLQUFLVCxZQUFZO29FQUFFQyxVQUFVbUMsV0FBV04sRUFBRUMsTUFBTSxDQUFDZixLQUFLO2dFQUFFOzREQUM3RTtvREFDQWtCLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pFLE1BQUs7b0RBQ0xqQixXQUFVOzs7Ozs7Ozs7Ozs7c0RBR2QsOERBQUNEOzs4REFDQyw4REFBQ087b0RBQU1OLFdBQVU7OERBQWtFOzs7Ozs7OERBR25GLDhEQUFDTztvREFDQ0MsTUFBSztvREFDTFosT0FBT3BCLE9BQU9JLFlBQVksQ0FBQ0UsSUFBSTtvREFDL0IyQixVQUFVLENBQUNDLElBQU1qQyxVQUFVWSxDQUFBQSxPQUFTO2dFQUNsQyxHQUFHQSxJQUFJO2dFQUNQVCxjQUFjO29FQUFFLEdBQUdTLEtBQUtULFlBQVk7b0VBQUVFLE1BQU1rQyxXQUFXTixFQUFFQyxNQUFNLENBQUNmLEtBQUs7Z0VBQUU7NERBQ3pFO29EQUNBa0IsS0FBSTtvREFDSkMsS0FBSTtvREFDSkUsTUFBSztvREFDTGpCLFdBQVU7Ozs7Ozs7Ozs7OztzREFHZCw4REFBQ0Q7OzhEQUNDLDhEQUFDTztvREFBTU4sV0FBVTs4REFBa0U7Ozs7Ozs4REFHbkYsOERBQUNPO29EQUNDQyxNQUFLO29EQUNMWixPQUFPcEIsT0FBT0ksWUFBWSxDQUFDRyxNQUFNO29EQUNqQzBCLFVBQVUsQ0FBQ0MsSUFBTWpDLFVBQVVZLENBQUFBLE9BQVM7Z0VBQ2xDLEdBQUdBLElBQUk7Z0VBQ1BULGNBQWM7b0VBQUUsR0FBR1MsS0FBS1QsWUFBWTtvRUFBRUcsUUFBUWlDLFdBQVdOLEVBQUVDLE1BQU0sQ0FBQ2YsS0FBSztnRUFBRTs0REFDM0U7b0RBQ0FrQixLQUFJO29EQUNKQyxLQUFJO29EQUNKRSxNQUFLO29EQUNMakIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVXhCLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWnBDLGlDQUNDLDhEQUFDbUM7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNtQjtvQ0FBR2xCLFdBQVU7OENBQXVEOzs7Ozs7OENBQ3JFLDhEQUFDbUI7b0NBQUduQixXQUFVOzhDQUNYcEMsaUJBQWlCd0MsaUJBQWlCLGlCQUNqQyw4REFBQ2dCO3dDQUFLcEIsV0FBVTtrREFBc0c7Ozs7OzZEQUl0SCw4REFBQ29CO3dDQUFLcEIsV0FBVTtrREFBb0c7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU0xSCw4REFBQ0Q7OzhDQUNDLDhEQUFDbUI7b0NBQUdsQixXQUFVOzhDQUF1RDs7Ozs7OzhDQUNyRSw4REFBQ21CO29DQUFHbkIsV0FBVTs7d0NBQ1hwQyxpQkFBaUJ5RCxpQkFBaUIsRUFBRUMsVUFBVTt3Q0FBRTs7Ozs7Ozs7Ozs7OztzQ0FHckQsOERBQUN2Qjs7OENBQ0MsOERBQUNtQjtvQ0FBR2xCLFdBQVU7OENBQXVEOzs7Ozs7OENBQ3JFLDhEQUFDbUI7b0NBQUduQixXQUFVOzt3Q0FDWHBDLGlCQUFpQjJELFlBQVksRUFBRUMsUUFBUSxNQUFNO3dDQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7eUNBS3RELDhEQUFDekI7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUN5Qjt3QkFBRXpCLFdBQVU7a0NBQTJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXBFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVhbnR1bS1uZXh1cy1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL01vbml0b3JpbmdDb250cm9scy50c3g/M2QyNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFBsYXlJY29uLFxuICBTdG9wSWNvbixcbiAgQ29nSWNvbixcbiAgQXJyb3dQYXRoSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnXG5cbmludGVyZmFjZSBNb25pdG9yaW5nQ29udHJvbHNQcm9wcyB7XG4gIG1vbml0b3JpbmdTdGF0dXM6IGFueVxuICBvblN0YXR1c0NoYW5nZTogKCkgPT4gdm9pZFxuICBzdGFydE1vbml0b3Jpbmc6IChjb25maWc6IGFueSkgPT4gUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+XG4gIHN0b3BNb25pdG9yaW5nOiAoKSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT5cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTW9uaXRvcmluZ0NvbnRyb2xzKHtcbiAgbW9uaXRvcmluZ1N0YXR1cyxcbiAgb25TdGF0dXNDaGFuZ2UsXG4gIHN0YXJ0TW9uaXRvcmluZzogc3RhcnRNb25pdG9yaW5nQWN0aW9uLFxuICBzdG9wTW9uaXRvcmluZzogc3RvcE1vbml0b3JpbmdBY3Rpb25cbn06IE1vbml0b3JpbmdDb250cm9sc1Byb3BzKSB7XG4gIGNvbnN0IFtpc1N0YXJ0aW5nLCBzZXRJc1N0YXJ0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNTdG9wcGluZywgc2V0SXNTdG9wcGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Nob3dDb25maWcsIHNldFNob3dDb25maWddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtjb25maWcsIHNldENvbmZpZ10gPSB1c2VTdGF0ZSh7XG4gICAgc3ltYm9sczogWydiaXRjb2luJywgJ2V0aGVyZXVtJywgJ3NvbGFuYScsICdjYXJkYW5vJ10sXG4gICAgbW9uaXRvcmluZ19pbnRlcnZhbF9zZWNvbmRzOiAzMDAsXG4gICAgYWxlcnRfbGV2ZWxzOiB7XG4gICAgICBjcml0aWNhbDogMC45LFxuICAgICAgaGlnaDogMC44LFxuICAgICAgbWVkaXVtOiAwLjdcbiAgICB9XG4gIH0pXG5cbiAgY29uc3Qgc3RhcnRNb25pdG9yaW5nID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzU3RhcnRpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc3RhcnRNb25pdG9yaW5nQWN0aW9uKGNvbmZpZylcbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBvblN0YXR1c0NoYW5nZSgpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3RhcnQgbW9uaXRvcmluZzonLCByZXN1bHQuZXJyb3IpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN0YXJ0aW5nIG1vbml0b3Jpbmc6JywgZXJyb3IpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU3RhcnRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3Qgc3RvcE1vbml0b3JpbmcgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNTdG9wcGluZyh0cnVlKVxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzdG9wTW9uaXRvcmluZ0FjdGlvbigpXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgb25TdGF0dXNDaGFuZ2UoKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN0b3AgbW9uaXRvcmluZzonLCByZXN1bHQuZXJyb3IpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN0b3BwaW5nIG1vbml0b3Jpbmc6JywgZXJyb3IpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU3RvcHBpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgYWRkU3ltYm9sID0gKCkgPT4ge1xuICAgIHNldENvbmZpZyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgc3ltYm9sczogWy4uLnByZXYuc3ltYm9scywgJyddXG4gICAgfSkpXG4gIH1cblxuICBjb25zdCByZW1vdmVTeW1ib2wgPSAoaW5kZXg6IG51bWJlcikgPT4ge1xuICAgIHNldENvbmZpZyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgc3ltYm9sczogcHJldi5zeW1ib2xzLmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpXG4gICAgfSkpXG4gIH1cblxuICBjb25zdCB1cGRhdGVTeW1ib2wgPSAoaW5kZXg6IG51bWJlciwgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIHNldENvbmZpZyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgc3ltYm9sczogcHJldi5zeW1ib2xzLm1hcCgoc3ltYm9sLCBpKSA9PiBpID09PSBpbmRleCA/IHZhbHVlIDogc3ltYm9sKVxuICAgIH0pKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgc2hhZG93IHJvdW5kZWQtbGdcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICBNb25pdG9yaW5nIENvbnRyb2xzXG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q29uZmlnKCFzaG93Q29uZmlnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHNoYWRvdy1zbSB0ZXh0LXNtIGxlYWRpbmctNCBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCBkYXJrOmhvdmVyOmJnLWdyYXktNjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPENvZ0ljb24gY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgQ29uZmlndXJlXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtvblN0YXR1c0NoYW5nZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHNoYWRvdy1zbSB0ZXh0LXNtIGxlYWRpbmctNCBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCBkYXJrOmhvdmVyOmJnLWdyYXktNjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEFycm93UGF0aEljb24gY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIHttb25pdG9yaW5nU3RhdHVzPy5tb25pdG9yaW5nX2FjdGl2ZSA/IChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3N0b3BNb25pdG9yaW5nfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N0b3BwaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgc2hhZG93LXNtIHRleHQtd2hpdGUgYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLXJlZC01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNTdG9wcGluZyA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPFN0b3BJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICBTdG9wIE1vbml0b3JpbmdcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17c3RhcnRNb25pdG9yaW5nfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N0YXJ0aW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgc2hhZG93LXNtIHRleHQtd2hpdGUgYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1ncmVlbi01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNTdGFydGluZyA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPFBsYXlJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICBTdGFydCBNb25pdG9yaW5nXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENvbmZpZ3VyYXRpb24gUGFuZWwgKi99XG4gICAgICB7c2hvd0NvbmZpZyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBiZy1ncmF5LTUwIGRhcms6YmctZ3JheS03MDAvNTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgey8qIFN5bWJvbHMgQ29uZmlndXJhdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBNb25pdG9yZWQgU3ltYm9sc1xuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIHtjb25maWcuc3ltYm9scy5tYXAoKHN5bWJvbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c3ltYm9sfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlU3ltYm9sKGluZGV4LCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJsb2NrIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCBzaGFkb3ctc20gZm9jdXM6Ym9yZGVyLWJsdWUtNTAwIGZvY3VzOnJpbmctYmx1ZS01MDAgZGFyazpiZy1ncmF5LTgwMCBkYXJrOnRleHQtd2hpdGUgc206dGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBiaXRjb2luXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVN5bWJvbChpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHRleHQteHMgZm9udC1tZWRpdW0gcm91bmRlZCB0ZXh0LXJlZC03MDAgYmctcmVkLTEwMCBob3ZlcjpiZy1yZWQtMjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLXJlZC01MDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgUmVtb3ZlXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17YWRkU3ltYm9sfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHNoYWRvdy1zbSB0ZXh0LXNtIGxlYWRpbmctNCBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCBkYXJrOmhvdmVyOmJnLWdyYXktNjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBBZGQgU3ltYm9sXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBNb25pdG9yaW5nIEludGVydmFsICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIE1vbml0b3JpbmcgSW50ZXJ2YWwgKHNlY29uZHMpXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcubW9uaXRvcmluZ19pbnRlcnZhbF9zZWNvbmRzfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHByZXYgPT4gKHsgLi4ucHJldiwgbW9uaXRvcmluZ19pbnRlcnZhbF9zZWNvbmRzOiBwYXJzZUludChlLnRhcmdldC52YWx1ZSkgfSkpfVxuICAgICAgICAgICAgICAgIG1pbj1cIjYwXCJcbiAgICAgICAgICAgICAgICBtYXg9XCIzNjAwXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGRhcms6YmctZ3JheS04MDAgZGFyazp0ZXh0LXdoaXRlIHNtOnRleHQtc21cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBBbGVydCBUaHJlc2hvbGRzICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIEFsZXJ0IENvbmZpZGVuY2UgVGhyZXNob2xkc1xuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICBDcml0aWNhbFxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5hbGVydF9sZXZlbHMuY3JpdGljYWx9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgIGFsZXJ0X2xldmVsczogeyAuLi5wcmV2LmFsZXJ0X2xldmVscywgY3JpdGljYWw6IHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpIH1cbiAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgbWF4PVwiMVwiXG4gICAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjFcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGRhcms6YmctZ3JheS04MDAgZGFyazp0ZXh0LXdoaXRlIHNtOnRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICBIaWdoXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29uZmlnLmFsZXJ0X2xldmVscy5oaWdofVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyhwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICBhbGVydF9sZXZlbHM6IHsgLi4ucHJldi5hbGVydF9sZXZlbHMsIGhpZ2g6IHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpIH1cbiAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgbWF4PVwiMVwiXG4gICAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjFcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGRhcms6YmctZ3JheS04MDAgZGFyazp0ZXh0LXdoaXRlIHNtOnRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICBNZWRpdW1cbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcuYWxlcnRfbGV2ZWxzLm1lZGl1bX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb25maWcocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgYWxlcnRfbGV2ZWxzOiB7IC4uLnByZXYuYWxlcnRfbGV2ZWxzLCBtZWRpdW06IHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpIH1cbiAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgbWF4PVwiMVwiXG4gICAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjFcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgc2hhZG93LXNtIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGRhcms6YmctZ3JheS04MDAgZGFyazp0ZXh0LXdoaXRlIHNtOnRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogU3RhdHVzIEluZm9ybWF0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAge21vbml0b3JpbmdTdGF0dXMgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZHQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlN0YXR1czwvZHQ+XG4gICAgICAgICAgICAgIDxkZCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICB7bW9uaXRvcmluZ1N0YXR1cy5tb25pdG9yaW5nX2FjdGl2ZSA/IChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICBBY3RpdmVcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICBJbmFjdGl2ZVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+TW9uaXRvcmVkIFN5bWJvbHM8L2R0PlxuICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAge21vbml0b3JpbmdTdGF0dXMubW9uaXRvcmVkX3N5bWJvbHM/Lmxlbmd0aCB8fCAwfSBzeW1ib2xzXG4gICAgICAgICAgICAgIDwvZGQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+VXB0aW1lPC9kdD5cbiAgICAgICAgICAgICAgPGRkIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgIHttb25pdG9yaW5nU3RhdHVzLnVwdGltZV9ob3Vycz8udG9GaXhlZCgyKSB8fCAwfSBob3Vyc1xuICAgICAgICAgICAgICA8L2RkPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS00XCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgIExvYWRpbmcgbW9uaXRvcmluZyBzdGF0dXMuLi5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJQbGF5SWNvbiIsIlN0b3BJY29uIiwiQ29nSWNvbiIsIkFycm93UGF0aEljb24iLCJNb25pdG9yaW5nQ29udHJvbHMiLCJtb25pdG9yaW5nU3RhdHVzIiwib25TdGF0dXNDaGFuZ2UiLCJzdGFydE1vbml0b3JpbmciLCJzdGFydE1vbml0b3JpbmdBY3Rpb24iLCJzdG9wTW9uaXRvcmluZyIsInN0b3BNb25pdG9yaW5nQWN0aW9uIiwiaXNTdGFydGluZyIsInNldElzU3RhcnRpbmciLCJpc1N0b3BwaW5nIiwic2V0SXNTdG9wcGluZyIsInNob3dDb25maWciLCJzZXRTaG93Q29uZmlnIiwiY29uZmlnIiwic2V0Q29uZmlnIiwic3ltYm9scyIsIm1vbml0b3JpbmdfaW50ZXJ2YWxfc2Vjb25kcyIsImFsZXJ0X2xldmVscyIsImNyaXRpY2FsIiwiaGlnaCIsIm1lZGl1bSIsInJlc3VsdCIsInN1Y2Nlc3MiLCJjb25zb2xlIiwiZXJyb3IiLCJhZGRTeW1ib2wiLCJwcmV2IiwicmVtb3ZlU3ltYm9sIiwiaW5kZXgiLCJmaWx0ZXIiLCJfIiwiaSIsInVwZGF0ZVN5bWJvbCIsInZhbHVlIiwibWFwIiwic3ltYm9sIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwibW9uaXRvcmluZ19hY3RpdmUiLCJkaXNhYmxlZCIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJwYXJzZUludCIsIm1pbiIsIm1heCIsInBhcnNlRmxvYXQiLCJzdGVwIiwiZHQiLCJkZCIsInNwYW4iLCJtb25pdG9yZWRfc3ltYm9scyIsImxlbmd0aCIsInVwdGltZV9ob3VycyIsInRvRml4ZWQiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MonitoringControls.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PatternDetectionPanel.tsx":
/*!**************************************************!*\
  !*** ./src/components/PatternDetectionPanel.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PatternDetectionPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CpuChipIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CpuChipIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CpuChipIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CpuChipIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CpuChipIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CpuChipIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PatternDetectionPanel({ monitoringStatus }) {\n    const [patterns, setPatterns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const runPatternAnalysis = async ()=>{\n        setIsAnalyzing(true);\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/patterns/analyze\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    symbols: [\n                        \"bitcoin\",\n                        \"ethereum\",\n                        \"solana\",\n                        \"cardano\"\n                    ],\n                    lookback_hours: 6,\n                    min_confidence: 0.5,\n                    real_time: false\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setPatterns(data.patterns_detected || []);\n            }\n        } catch (error) {\n            console.error(\"Error running pattern analysis:\", error);\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        runPatternAnalysis();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CpuChipIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Pattern Detection Analysis\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: runPatternAnalysis,\n                                    disabled: isAnalyzing,\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                    children: [\n                                        isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CpuChipIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Run Analysis\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: \"Analyzing market patterns...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this) : patterns.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: patterns.map((pattern, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: pattern.pattern_type.replace(\"_\", \" \").toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm font-medium text-gray-900 dark:text-white\",\n                                                            children: pattern.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-green-600\",\n                                                    children: [\n                                                        (pattern.confidence_score * 100).toFixed(1),\n                                                        \"% confidence\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                \"Detected at \",\n                                                new Date(pattern.detection_timestamp).toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, this),\n                                        pattern.trigger_conditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    \"Trigger conditions: \",\n                                                    Object.keys(pattern.trigger_conditions).join(\", \")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CpuChipIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: \"No patterns detected in recent data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 dark:text-gray-500 mt-1\",\n                                    children: \"Try running the analysis again or check if monitoring is active\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            monitoringStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: \"Pattern Detection Statistics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: monitoringStatus.session_statistics?.total_patterns_detected || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Total Patterns\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: monitoringStatus.session_statistics?.detection_cycles_completed || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Detection Cycles\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: monitoringStatus.session_statistics?.detection_rate_per_hour?.toFixed(1) || \"0.0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Patterns/Hour\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/ar/frontend/src/components/PatternDetectionPanel.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PatternDetectionPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PerformanceMetricsPanel.tsx":
/*!****************************************************!*\
  !*** ./src/components/PerformanceMetricsPanel.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PerformanceMetricsPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CpuChipIcon,ServerIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CpuChipIcon,ServerIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CpuChipIcon,ServerIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ClockIcon,CpuChipIcon,ServerIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction PerformanceMetricsPanel() {\n    const [metricsData, setMetricsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [performanceData, setPerformanceData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchMetrics = async ()=>{\n        try {\n            const [metricsResponse, performanceResponse] = await Promise.all([\n                fetch(\"http://localhost:8000/api/v1/monitoring/metrics?hours_back=1\"),\n                fetch(\"http://localhost:8000/api/v1/monitoring/performance\")\n            ]);\n            if (metricsResponse.ok) {\n                const metrics = await metricsResponse.json();\n                setMetricsData(metrics);\n            }\n            if (performanceResponse.ok) {\n                const performance = await performanceResponse.json();\n                setPerformanceData(performance);\n            }\n        } catch (error) {\n            console.error(\"Error fetching metrics:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMetrics();\n        const interval = setInterval(fetchMetrics, 30000) // Refresh every 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 shadow rounded-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    // Prepare chart data\n    const chartData = metricsData?.time_series_data?.map((item)=>({\n            time: new Date(item.timestamp).toLocaleTimeString(),\n            memory: item.memory_usage_mb,\n            cpu: item.cpu_usage_percent,\n            analysisTime: item.avg_analysis_time_ms,\n            patterns: item.patterns_detected\n        })) || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            performanceData && performanceData.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this),\n                                \"Performance Summary\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `text-3xl font-bold ${performanceData.health_score >= 0.9 ? \"text-green-600\" : performanceData.health_score >= 0.7 ? \"text-yellow-600\" : \"text-red-600\"}`,\n                                            children: [\n                                                (performanceData.health_score * 100).toFixed(0),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Health Score\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `text-xs font-medium mt-1 ${performanceData.health_status === \"excellent\" ? \"text-green-600\" : performanceData.health_status === \"good\" ? \"text-yellow-600\" : \"text-red-600\"}`,\n                                            children: performanceData.health_status.toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: [\n                                                performanceData.performance_indicators.uptime_hours.toFixed(1),\n                                                \"h\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Uptime\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: performanceData.performance_indicators.patterns_per_hour.toFixed(1)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Patterns/Hour\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: [\n                                                performanceData.performance_indicators.avg_analysis_time_ms.toFixed(1),\n                                                \"ms\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: \"Avg Analysis Time\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this),\n            chartData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                \"Memory Usage\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.AreaChart, {\n                                    data: chartData,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.XAxis, {\n                                            dataKey: \"time\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            labelFormatter: (label)=>`Time: ${label}`,\n                                            formatter: (value)=>[\n                                                    `${value.toFixed(1)} MB`,\n                                                    \"Memory Usage\"\n                                                ]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Area, {\n                                            type: \"monotone\",\n                                            dataKey: \"memory\",\n                                            stroke: \"#3B82F6\",\n                                            fill: \"#3B82F6\",\n                                            fillOpacity: 0.3\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this),\n            chartData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                \"CPU Usage\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.LineChart, {\n                                    data: chartData,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.XAxis, {\n                                            dataKey: \"time\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            labelFormatter: (label)=>`Time: ${label}`,\n                                            formatter: (value)=>[\n                                                    `${value.toFixed(1)}%`,\n                                                    \"CPU Usage\"\n                                                ]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Line, {\n                                            type: \"monotone\",\n                                            dataKey: \"cpu\",\n                                            stroke: \"#8B5CF6\",\n                                            strokeWidth: 2,\n                                            dot: {\n                                                fill: \"#8B5CF6\",\n                                                strokeWidth: 2,\n                                                r: 4\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            chartData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ClockIcon_CpuChipIcon_ServerIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                \"Analysis Performance\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.LineChart, {\n                                    data: chartData,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.XAxis, {\n                                            dataKey: \"time\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                            labelFormatter: (label)=>`Time: ${label}`,\n                                            formatter: (value)=>[\n                                                    `${value.toFixed(1)}ms`,\n                                                    \"Analysis Time\"\n                                                ]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Line, {\n                                            type: \"monotone\",\n                                            dataKey: \"analysisTime\",\n                                            stroke: \"#F59E0B\",\n                                            strokeWidth: 2,\n                                            dot: {\n                                                fill: \"#F59E0B\",\n                                                strokeWidth: 2,\n                                                r: 4\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this),\n            metricsData?.aggregated_statistics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: \"Aggregated Statistics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                            className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                            children: \"Peak Memory Usage\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                            className: \"mt-1 text-lg font-semibold text-gray-900 dark:text-white\",\n                                            children: [\n                                                metricsData.aggregated_statistics.peak_memory_usage_mb.toFixed(1),\n                                                \" MB\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                            className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                            children: \"Peak CPU Usage\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                            className: \"mt-1 text-lg font-semibold text-gray-900 dark:text-white\",\n                                            children: [\n                                                metricsData.aggregated_statistics.peak_cpu_usage_percent.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                            className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                            children: \"Max Analysis Time\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                            className: \"mt-1 text-lg font-semibold text-gray-900 dark:text-white\",\n                                            children: [\n                                                metricsData.aggregated_statistics.max_analysis_time_ms.toFixed(1),\n                                                \" ms\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                            className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                            children: \"Total Patterns\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                            className: \"mt-1 text-lg font-semibold text-gray-900 dark:text-white\",\n                                            children: metricsData.aggregated_statistics.total_patterns_detected\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/ar/frontend/src/components/PerformanceMetricsPanel.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PerformanceMetricsPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProductionMonitoringDashboard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/ProductionMonitoringDashboard.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductionMonitoringDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/WifiIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,CpuChipIcon,ExclamationTriangleIcon,WifiIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _MonitoringControls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MonitoringControls */ \"(ssr)/./src/components/MonitoringControls.tsx\");\n/* harmony import */ var _PatternDetectionPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PatternDetectionPanel */ \"(ssr)/./src/components/PatternDetectionPanel.tsx\");\n/* harmony import */ var _AlertManagementPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AlertManagementPanel */ \"(ssr)/./src/components/AlertManagementPanel.tsx\");\n/* harmony import */ var _PerformanceMetricsPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PerformanceMetricsPanel */ \"(ssr)/./src/components/PerformanceMetricsPanel.tsx\");\n/* harmony import */ var _SystemHealthPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SystemHealthPanel */ \"(ssr)/./src/components/SystemHealthPanel.tsx\");\n/* harmony import */ var _hooks_useRealTimeMonitoring__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useRealTimeMonitoring */ \"(ssr)/./src/hooks/useRealTimeMonitoring.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction ProductionMonitoringDashboard() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const { monitoringData, alerts, isConnected, error, actions } = (0,_hooks_useRealTimeMonitoring__WEBPACK_IMPORTED_MODULE_7__.useRealTimeMonitoring)();\n    // Use the real-time monitoring data\n    const monitoringStatus = monitoringData;\n    const isLoading = !monitoringData && !error;\n    const tabs = [\n        {\n            id: \"overview\",\n            name: \"Overview\",\n            icon: _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: \"patterns\",\n            name: \"Pattern Detection\",\n            icon: _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: \"alerts\",\n            name: \"Alert Management\",\n            icon: _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"performance\",\n            name: \"Performance\",\n            icon: _barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: \"Quantum Market Intelligence\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: \"Production Monitoring Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${isConnected ? \"text-green-500\" : \"text-red-500\"}`\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-xs ${isConnected ? \"text-green-600\" : \"text-red-600\"}`,\n                                                children: isConnected ? \"Connected\" : \"Disconnected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-red-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Connection Error\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this) : monitoringStatus?.monitoring_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-green-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Monitoring Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-yellow-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Monitoring Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    monitoringStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            \"Uptime: \",\n                                            monitoringStatus.uptime_hours.toFixed(2),\n                                            \"h\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this),\n                                    alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-orange-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: [\n                                                    alerts.length,\n                                                    \" Alert\",\n                                                    alerts.length > 1 ? \"s\" : \"\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-8\",\n                        children: tabs.map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: `flex items-center py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? \"border-blue-500 text-blue-600 dark:text-blue-400\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 19\n                                    }, this),\n                                    tab.name\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-400 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-red-800 dark:text-red-200\",\n                                            children: \"Connection Error\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 dark:text-red-300 mt-1\",\n                                            children: [\n                                                error,\n                                                \". Make sure the backend server is running on localhost:8000.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SystemHealthPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                monitoringStatus: monitoringStatus\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MonitoringControls__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                monitoringStatus: monitoringStatus,\n                                onStatusChange: actions.refreshStatus,\n                                startMonitoring: actions.startMonitoring,\n                                stopMonitoring: actions.stopMonitoring\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            monitoringStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\",\n                                                                    children: \"Patterns Detected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                                    children: monitoringStatus.session_statistics.total_patterns_detected\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\",\n                                                                    children: \"Alerts Generated\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                                    children: monitoringStatus.session_statistics.total_alerts_generated\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\",\n                                                                    children: \"Detection Cycles\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                                    children: monitoringStatus.session_statistics.detection_cycles_completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_CpuChipIcon_ExclamationTriangleIcon_WifiIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-6 w-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-5 w-0 flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                    className: \"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\",\n                                                                    children: \"Detection Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        monitoringStatus.session_statistics.detection_rate_per_hour.toFixed(1),\n                                                                        \"/h\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"patterns\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PatternDetectionPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        monitoringStatus: monitoringStatus\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"alerts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AlertManagementPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"performance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PerformanceMetricsPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/ar/frontend/src/components/ProductionMonitoringDashboard.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProductionMonitoringDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SystemHealthPanel.tsx":
/*!**********************************************!*\
  !*** ./src/components/SystemHealthPanel.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemHealthPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CircleStackIcon,ExclamationTriangleIcon,HeartIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CircleStackIcon,ExclamationTriangleIcon,HeartIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CircleStackIcon,ExclamationTriangleIcon,HeartIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CircleStackIcon,ExclamationTriangleIcon,HeartIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CircleStackIcon,ExclamationTriangleIcon,HeartIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CircleStackIcon,ExclamationTriangleIcon,HeartIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CircleStackIcon,ExclamationTriangleIcon,HeartIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SystemHealthPanel({ monitoringStatus }) {\n    const [healthData, setHealthData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [performanceData, setPerformanceData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchHealthData = async ()=>{\n        try {\n            const [healthResponse, performanceResponse] = await Promise.all([\n                fetch(\"http://localhost:8000/api/v1/monitoring/health\"),\n                fetch(\"http://localhost:8000/api/v1/monitoring/performance\")\n            ]);\n            if (healthResponse.ok) {\n                const health = await healthResponse.json();\n                setHealthData(health);\n            }\n            if (performanceResponse.ok) {\n                const performance = await performanceResponse.json();\n                setPerformanceData(performance);\n            }\n        } catch (error) {\n            console.error(\"Error fetching health data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchHealthData();\n        const interval = setInterval(fetchHealthData, 10000) // Refresh every 10 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, []);\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"healthy\":\n            case \"active\":\n            case \"operational\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n            case \"inactive\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n            case \"unhealthy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"healthy\":\n            case \"active\":\n            case \"operational\":\n                return \"text-green-600\";\n            case \"warning\":\n            case \"inactive\":\n                return \"text-yellow-600\";\n            case \"error\":\n            case \"unhealthy\":\n                return \"text-red-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getHealthScoreColor = (score)=>{\n        if (score >= 0.9) return \"text-green-600\";\n        if (score >= 0.7) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const getHealthScoreBg = (score)=>{\n        if (score >= 0.9) return \"bg-green-100 dark:bg-green-900/20\";\n        if (score >= 0.7) return \"bg-yellow-100 dark:bg-yellow-900/20\";\n        return \"bg-red-100 dark:bg-red-900/20\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 shadow rounded-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                \"System Health\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        performanceData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-center px-3 py-1 rounded-full text-sm font-medium ${getHealthScoreBg(performanceData.health_score)}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: getHealthScoreColor(performanceData.health_score),\n                                children: [\n                                    \"Health Score: \",\n                                    (performanceData.health_score * 100).toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-900 dark:text-white mb-4\",\n                                        children: \"System Components\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: healthData && Object.entries(healthData.components).map(([component, status])=>{\n                                            const componentNames = {\n                                                monitoring_service: {\n                                                    name: \"Monitoring Service\",\n                                                    icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                                                },\n                                                pattern_detection: {\n                                                    name: \"Pattern Detection\",\n                                                    icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                                },\n                                                database: {\n                                                    name: \"Database\",\n                                                    icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                                },\n                                                alert_system: {\n                                                    name: \"Alert System\",\n                                                    icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                                },\n                                                overall_status: {\n                                                    name: \"Overall Status\",\n                                                    icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n                                                }\n                                            };\n                                            const componentInfo = componentNames[component] || {\n                                                name: component,\n                                                icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CircleStackIcon_ExclamationTriangleIcon_HeartIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                                            };\n                                            const Icon = componentInfo.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-4 w-4 text-gray-400 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900 dark:text-white\",\n                                                                children: componentInfo.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            getStatusIcon(status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `ml-2 text-sm font-medium ${getStatusColor(status)}`,\n                                                                children: status.charAt(0).toUpperCase() + status.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, component, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            performanceData && performanceData.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-900 dark:text-white mb-4\",\n                                        children: \"Performance Indicators\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Uptime\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                        children: [\n                                                            performanceData.performance_indicators.uptime_hours.toFixed(2),\n                                                            \" hours\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Patterns/Hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                        children: performanceData.performance_indicators.patterns_per_hour.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Avg Analysis Time\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                        children: [\n                                                            performanceData.performance_indicators.avg_analysis_time_ms.toFixed(1),\n                                                            \"ms\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Memory Efficiency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-sm font-medium ${performanceData.performance_indicators.memory_efficiency === \"good\" ? \"text-green-600\" : \"text-yellow-600\"}`,\n                                                        children: performanceData.performance_indicators.memory_efficiency\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"CPU Efficiency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-sm font-medium ${performanceData.performance_indicators.cpu_efficiency === \"good\" ? \"text-green-600\" : \"text-yellow-600\"}`,\n                                                        children: performanceData.performance_indicators.cpu_efficiency\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    performanceData && performanceData.recommendations && performanceData.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 dark:text-white mb-3\",\n                                children: \"Recommendations\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: performanceData.recommendations.map((recommendation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: recommendation\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                    children: \"Last Updated\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: new Date().toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/ar/frontend/src/components/SystemHealthPanel.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SystemHealthPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useRealTimeMonitoring.ts":
/*!********************************************!*\
  !*** ./src/hooks/useRealTimeMonitoring.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRealTimeMonitoring: () => (/* binding */ useRealTimeMonitoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useRealTimeMonitoring auto */ \nfunction useRealTimeMonitoring() {\n    const [monitoringData, setMonitoringData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchMonitoringStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/monitoring/status\");\n            if (response.ok) {\n                const data = await response.json();\n                setMonitoringData(data);\n                setError(null);\n                setIsConnected(true);\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to fetch monitoring status\");\n            setIsConnected(false);\n        }\n    }, []);\n    const fetchAlerts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            // In a real implementation, this would fetch recent alerts from the database\n            // For now, we'll simulate alerts based on monitoring activity\n            if (monitoringData?.session_statistics.total_alerts_generated > alerts.length) {\n                const newAlert = {\n                    alert_level: \"medium\",\n                    pattern_type: \"whale_accumulation\",\n                    symbol: \"bitcoin\",\n                    confidence_score: 0.75,\n                    detection_timestamp: new Date().toISOString(),\n                    message: \"Whale accumulation pattern detected for Bitcoin with 75% confidence\"\n                };\n                setAlerts((prev)=>[\n                        newAlert,\n                        ...prev.slice(0, 9)\n                    ]) // Keep last 10 alerts\n                ;\n            }\n        } catch (err) {\n            console.error(\"Error fetching alerts:\", err);\n        }\n    }, [\n        monitoringData,\n        alerts.length\n    ]);\n    // Real-time polling (in production, this would use WebSockets)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchMonitoringStatus();\n        const statusInterval = setInterval(fetchMonitoringStatus, 5000) // Every 5 seconds\n        ;\n        const alertsInterval = setInterval(fetchAlerts, 10000) // Every 10 seconds\n        ;\n        return ()=>{\n            clearInterval(statusInterval);\n            clearInterval(alertsInterval);\n        };\n    }, [\n        fetchMonitoringStatus,\n        fetchAlerts\n    ]);\n    const startMonitoring = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (config)=>{\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/monitoring/start\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(config)\n            });\n            if (response.ok) {\n                await fetchMonitoringStatus();\n                return {\n                    success: true\n                };\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (err) {\n            return {\n                success: false,\n                error: err instanceof Error ? err.message : \"Failed to start monitoring\"\n            };\n        }\n    }, [\n        fetchMonitoringStatus\n    ]);\n    const stopMonitoring = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/monitoring/stop\", {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                await fetchMonitoringStatus();\n                return {\n                    success: true\n                };\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (err) {\n            return {\n                success: false,\n                error: err instanceof Error ? err.message : \"Failed to stop monitoring\"\n            };\n        }\n    }, [\n        fetchMonitoringStatus\n    ]);\n    const runPatternAnalysis = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (symbols)=>{\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/patterns/analyze\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    symbols,\n                    lookback_hours: 6,\n                    min_confidence: 0.5,\n                    real_time: false\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                return {\n                    success: true,\n                    data\n                };\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (err) {\n            return {\n                success: false,\n                error: err instanceof Error ? err.message : \"Failed to run pattern analysis\"\n            };\n        }\n    }, []);\n    const updateAlertConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (level, config)=>{\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/monitoring/alerts/config\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    alert_level: level,\n                    ...config\n                })\n            });\n            if (response.ok) {\n                return {\n                    success: true\n                };\n            } else {\n                throw new Error(`HTTP ${response.status}`);\n            }\n        } catch (err) {\n            return {\n                success: false,\n                error: err instanceof Error ? err.message : \"Failed to update alert config\"\n            };\n        }\n    }, []);\n    return {\n        monitoringData,\n        alerts,\n        isConnected,\n        error,\n        actions: {\n            startMonitoring,\n            stopMonitoring,\n            runPatternAnalysis,\n            updateAlertConfig,\n            refreshStatus: fetchMonitoringStatus\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useRealTimeMonitoring.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"955ec95b3e6f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdWFudHVtLW5leHVzLWZyb250ZW5kLy4vYXBwL2dsb2JhbHMuY3NzPzNlMTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NTVlYzk1YjNlNmZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Quantum Market Intelligence - Production Monitoring\",\n    description: \"Real-time crypto pattern detection monitoring and alert management dashboard\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/ar/frontend/app/layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/ar/frontend/app/layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUdNQTtBQUhnQjtBQUtmLE1BQU1DLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsMkpBQWU7c0JBQUdLOzs7Ozs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsid2VicGFjazovL3F1YW50dW0tbmV4dXMtZnJvbnRlbmQvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnUXVhbnR1bSBNYXJrZXQgSW50ZWxsaWdlbmNlIC0gUHJvZHVjdGlvbiBNb25pdG9yaW5nJyxcbiAgZGVzY3JpcHRpb246ICdSZWFsLXRpbWUgY3J5cHRvIHBhdHRlcm4gZGV0ZWN0aW9uIG1vbml0b3JpbmcgYW5kIGFsZXJ0IG1hbmFnZW1lbnQgZGFzaGJvYXJkJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/ar/frontend/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lodash","vendor-chunks/recharts","vendor-chunks/d3-shape","vendor-chunks/d3-scale","vendor-chunks/d3-array","vendor-chunks/@heroicons","vendor-chunks/d3-format","vendor-chunks/d3-interpolate","vendor-chunks/d3-time","vendor-chunks/react-smooth","vendor-chunks/react-transition-group","vendor-chunks/@babel","vendor-chunks/prop-types","vendor-chunks/recharts-scale","vendor-chunks/d3-time-format","vendor-chunks/d3-color","vendor-chunks/victory-vendor","vendor-chunks/react-is","vendor-chunks/tiny-invariant","vendor-chunks/internmap","vendor-chunks/fast-equals","vendor-chunks/decimal.js-light","vendor-chunks/d3-path","vendor-chunks/clsx","vendor-chunks/object-assign","vendor-chunks/eventemitter3"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Far%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Far%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();