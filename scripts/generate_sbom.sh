#!/bin/bash

# SBOM (Software Bill of Materials) Generation Script
# Implements Sigstore binary provenance for Quantum Market Intelligence Hub

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if Syft is installed
check_syft() {
    if ! command -v syft >/dev/null 2>&1; then
        log "Syft not found. Installing Syft..."
        
        # Install Syft
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if command -v brew >/dev/null 2>&1; then
                brew install syft
            else
                curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh -s -- -b /usr/local/bin
            fi
        else
            # Linux
            curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh -s -- -b /usr/local/bin
        fi
        
        success "Syft installed successfully"
    else
        success "Syft is already installed"
    fi
}

# Check if Cosign is installed (for Sigstore)
check_cosign() {
    if ! command -v cosign >/dev/null 2>&1; then
        log "Cosign not found. Installing Cosign..."
        
        # Install Cosign
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if command -v brew >/dev/null 2>&1; then
                brew install cosign
            else
                LATEST_VERSION=$(curl -s https://api.github.com/repos/sigstore/cosign/releases/latest | grep tag_name | cut -d '"' -f 4)
                curl -O -L "https://github.com/sigstore/cosign/releases/latest/download/cosign-darwin-amd64"
                sudo mv cosign-darwin-amd64 /usr/local/bin/cosign
                sudo chmod +x /usr/local/bin/cosign
            fi
        else
            # Linux
            LATEST_VERSION=$(curl -s https://api.github.com/repos/sigstore/cosign/releases/latest | grep tag_name | cut -d '"' -f 4)
            curl -O -L "https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64"
            sudo mv cosign-linux-amd64 /usr/local/bin/cosign
            sudo chmod +x /usr/local/bin/cosign
        fi
        
        success "Cosign installed successfully"
    else
        success "Cosign is already installed"
    fi
}

# Generate SBOM for backend
generate_backend_sbom() {
    log "Generating SBOM for backend application..."
    
    # Create SBOM directory
    mkdir -p sbom
    
    # Generate SBOM for Python dependencies
    log "Scanning Python dependencies..."
    syft backend/requirements.txt -o spdx-json=sbom/backend-python-deps.spdx.json
    syft backend/requirements.txt -o syft-json=sbom/backend-python-deps.syft.json
    
    # Generate SBOM for backend Docker image (if built)
    if docker images | grep -q quantum-backend; then
        log "Scanning backend Docker image..."
        syft quantum-backend:latest -o spdx-json=sbom/backend-docker.spdx.json
        syft quantum-backend:latest -o syft-json=sbom/backend-docker.syft.json
    fi
    
    # Generate SBOM for source code
    log "Scanning backend source code..."
    syft backend/ -o spdx-json=sbom/backend-source.spdx.json
    syft backend/ -o syft-json=sbom/backend-source.syft.json
    
    success "Backend SBOM generated"
}

# Generate SBOM for infrastructure
generate_infrastructure_sbom() {
    log "Generating SBOM for infrastructure components..."
    
    # Scan Docker Compose file
    log "Scanning Docker Compose configuration..."
    syft docker-compose.yml -o spdx-json=sbom/infrastructure-compose.spdx.json
    
    # Scan individual Docker images
    local images=("timescale/timescaledb:2.13.1-pg15" "confluentinc/cp-kafka:7.5.1" "redis:7.2.3-alpine")
    
    for image in "${images[@]}"; do
        local image_name=$(echo "$image" | sed 's/[^a-zA-Z0-9]/-/g')
        log "Scanning Docker image: $image"
        
        # Pull image if not present
        docker pull "$image" >/dev/null 2>&1 || true
        
        # Generate SBOM
        syft "$image" -o spdx-json="sbom/infrastructure-${image_name}.spdx.json" 2>/dev/null || warning "Failed to scan $image"
    done
    
    success "Infrastructure SBOM generated"
}

# Generate comprehensive SBOM
generate_comprehensive_sbom() {
    log "Generating comprehensive SBOM..."
    
    # Scan entire project
    syft . -o spdx-json=sbom/quantum-hub-complete.spdx.json
    syft . -o syft-json=sbom/quantum-hub-complete.syft.json
    syft . -o cyclonedx-json=sbom/quantum-hub-complete.cyclonedx.json
    
    success "Comprehensive SBOM generated"
}

# Sign SBOMs with Cosign (Sigstore)
sign_sboms() {
    log "Signing SBOMs with Cosign (Sigstore)..."
    
    # Initialize cosign if needed (keyless signing)
    export COSIGN_EXPERIMENTAL=1
    
    # Sign each SBOM file
    for sbom_file in sbom/*.json; do
        if [ -f "$sbom_file" ]; then
            log "Signing $sbom_file..."
            
            # Sign with keyless signing (uses OIDC)
            cosign sign-blob --yes "$sbom_file" --output-signature "${sbom_file}.sig" --output-certificate "${sbom_file}.crt" 2>/dev/null || {
                warning "Failed to sign $sbom_file with keyless signing, trying local key..."
                
                # Generate local key if it doesn't exist
                if [ ! -f cosign.key ]; then
                    log "Generating local Cosign key pair..."
                    cosign generate-key-pair
                fi
                
                # Sign with local key
                cosign sign-blob --key cosign.key "$sbom_file" --output-signature "${sbom_file}.sig" 2>/dev/null || warning "Failed to sign $sbom_file"
            }
        fi
    done
    
    success "SBOMs signed successfully"
}

# Verify SBOM signatures
verify_sboms() {
    log "Verifying SBOM signatures..."
    
    export COSIGN_EXPERIMENTAL=1
    
    for sbom_file in sbom/*.json; do
        if [ -f "$sbom_file" ] && [ -f "${sbom_file}.sig" ]; then
            log "Verifying signature for $sbom_file..."
            
            if [ -f "${sbom_file}.crt" ]; then
                # Verify keyless signature
                cosign verify-blob --certificate "${sbom_file}.crt" --signature "${sbom_file}.sig" "$sbom_file" >/dev/null 2>&1 && {
                    success "✓ Signature verified for $sbom_file"
                } || warning "✗ Signature verification failed for $sbom_file"
            elif [ -f cosign.pub ]; then
                # Verify with public key
                cosign verify-blob --key cosign.pub --signature "${sbom_file}.sig" "$sbom_file" >/dev/null 2>&1 && {
                    success "✓ Signature verified for $sbom_file"
                } || warning "✗ Signature verification failed for $sbom_file"
            fi
        fi
    done
}

# Generate SBOM report
generate_sbom_report() {
    log "Generating SBOM report..."
    
    local report_file="sbom/quantum-hub-sbom-report.md"
    
    cat > "$report_file" << EOF
# Quantum Market Intelligence Hub - Software Bill of Materials Report

Generated on: $(date)
Project Version: 1.0.0
Environment: Development

## Overview

This report contains the Software Bill of Materials (SBOM) for the Quantum Market Intelligence Hub project, including all dependencies, infrastructure components, and security signatures.

## SBOM Files Generated

EOF
    
    # List all SBOM files
    for sbom_file in sbom/*.json; do
        if [ -f "$sbom_file" ]; then
            local filename=$(basename "$sbom_file")
            local filesize=$(du -h "$sbom_file" | cut -f1)
            local components=$(jq '.packages | length' "$sbom_file" 2>/dev/null || echo "N/A")
            
            echo "### $filename" >> "$report_file"
            echo "- Size: $filesize" >> "$report_file"
            echo "- Components: $components" >> "$report_file"
            echo "- Signed: $([ -f "${sbom_file}.sig" ] && echo "Yes" || echo "No")" >> "$report_file"
            echo "" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

## Security Signatures

All SBOM files are cryptographically signed using Sigstore/Cosign for integrity verification.

## Compliance

This SBOM generation follows:
- SPDX 2.3 specification
- CycloneDX 1.4 specification
- NIST guidelines for software supply chain security
- Financial industry best practices for software provenance

## Verification

To verify SBOM signatures:
\`\`\`bash
cosign verify-blob --certificate <sbom-file>.crt --signature <sbom-file>.sig <sbom-file>
\`\`\`

## Contact

For questions about this SBOM report, contact: <EMAIL>
EOF
    
    success "SBOM report generated: $report_file"
}

# Generate dependency hash manifest
generate_dependency_manifest() {
    log "Generating dependency hash manifest..."
    
    local manifest_file="sbom/dependency-hashes.txt"
    
    echo "# Quantum Market Intelligence Hub - Dependency Hash Manifest" > "$manifest_file"
    echo "# Generated on: $(date)" >> "$manifest_file"
    echo "" >> "$manifest_file"
    
    # Hash Python requirements
    if [ -f "backend/requirements.txt" ]; then
        echo "## Python Dependencies" >> "$manifest_file"
        sha256sum backend/requirements.txt >> "$manifest_file"
        echo "" >> "$manifest_file"
    fi
    
    # Hash Docker Compose files
    echo "## Docker Compose Files" >> "$manifest_file"
    find . -name "docker-compose*.yml" -exec sha256sum {} \; >> "$manifest_file"
    echo "" >> "$manifest_file"
    
    # Hash configuration files
    echo "## Configuration Files" >> "$manifest_file"
    find infrastructure/ -name "*.conf" -o -name "*.yaml" -o -name "*.yml" | xargs sha256sum >> "$manifest_file"
    echo "" >> "$manifest_file"
    
    # Hash SBOM files
    echo "## SBOM Files" >> "$manifest_file"
    find sbom/ -name "*.json" | xargs sha256sum >> "$manifest_file"
    
    success "Dependency manifest generated: $manifest_file"
}

# Main function
main() {
    log "Starting SBOM generation for Quantum Market Intelligence Hub..."
    
    # Check prerequisites
    check_syft
    check_cosign
    
    # Generate SBOMs
    generate_backend_sbom
    generate_infrastructure_sbom
    generate_comprehensive_sbom
    
    # Sign SBOMs
    sign_sboms
    
    # Verify signatures
    verify_sboms
    
    # Generate reports
    generate_sbom_report
    generate_dependency_manifest
    
    success "SBOM generation completed successfully!"
    log "SBOM files are available in the 'sbom/' directory"
    log "View the report: cat sbom/quantum-hub-sbom-report.md"
}

# Run main function
main "$@"
