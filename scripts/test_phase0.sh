#!/bin/bash

# Phase 0 Testing Script for Quantum Market Intelligence Hub
# Comprehensive validation of foundation infrastructure

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test execution function
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_RUN=$((TESTS_RUN + 1))
    log "Running test: $test_name"
    
    if eval "$test_command"; then
        success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        error "✗ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Infrastructure tests
test_docker_services() {
    log "Testing Docker services..."
    
    run_test "TimescaleDB container running" \
        "docker-compose ps timescaledb | grep -q 'Up'"
    
    run_test "Kafka container running" \
        "docker-compose ps kafka | grep -q 'Up'"
    
    run_test "Schema Registry container running" \
        "docker-compose ps schema-registry | grep -q 'Up'"
    
    run_test "Redis container running" \
        "docker-compose ps redis | grep -q 'Up'"
    
    run_test "Backend container running" \
        "docker-compose ps backend | grep -q 'Up'"
}

# Database connectivity tests
test_database_connectivity() {
    log "Testing database connectivity..."
    
    run_test "TimescaleDB connection" \
        "docker-compose exec -T timescaledb pg_isready -U quantum_user -d quantum_market_db"
    
    run_test "TimescaleDB extension loaded" \
        "docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT extname FROM pg_extension WHERE extname = 'timescaledb';\" | grep -q timescaledb"
    
    run_test "Market data schema exists" \
        "docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'market_data';\" | grep -q market_data"
    
    run_test "Price events hypertable exists" \
        "docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT hypertable_name FROM timescaledb_information.hypertable WHERE hypertable_name = 'price_events';\" | grep -q price_events"
}

# Kafka connectivity tests
test_kafka_connectivity() {
    log "Testing Kafka connectivity..."
    
    run_test "Kafka broker accessible" \
        "docker-compose exec -T kafka kafka-broker-api-versions --bootstrap-server localhost:9092 >/dev/null 2>&1"
    
    run_test "Schema Registry accessible" \
        "curl -f http://localhost:8081/subjects >/dev/null 2>&1"
    
    run_test "Financial events topic exists" \
        "docker-compose exec -T kafka kafka-topics --list --bootstrap-server localhost:9092 | grep -q financial_events"
    
    run_test "Risk analysis topic exists" \
        "docker-compose exec -T kafka kafka-topics --list --bootstrap-server localhost:9092 | grep -q risk_analysis"
    
    run_test "Validation topic exists" \
        "docker-compose exec -T kafka kafka-topics --list --bootstrap-server localhost:9092 | grep -q validation_topic"
}

# Redis connectivity tests
test_redis_connectivity() {
    log "Testing Redis connectivity..."
    
    run_test "Redis ping" \
        "docker-compose exec -T redis redis-cli ping | grep -q PONG"
    
    run_test "Redis set/get test" \
        "docker-compose exec -T redis redis-cli set test_key test_value && docker-compose exec -T redis redis-cli get test_key | grep -q test_value"
    
    run_test "Redis cleanup test key" \
        "docker-compose exec -T redis redis-cli del test_key"
}

# Backend API tests
test_backend_api() {
    log "Testing backend API..."
    
    run_test "Backend health endpoint" \
        "curl -f http://localhost:8000/health >/dev/null 2>&1"
    
    run_test "Backend detailed health endpoint" \
        "curl -f http://localhost:8000/health/detailed >/dev/null 2>&1"
    
    run_test "Backend readiness endpoint" \
        "curl -f http://localhost:8000/health/readiness >/dev/null 2>&1"
    
    run_test "Backend liveness endpoint" \
        "curl -f http://localhost:8000/health/liveness >/dev/null 2>&1"
    
    run_test "Backend database health endpoint" \
        "curl -f http://localhost:8000/health/database >/dev/null 2>&1"
    
    run_test "Backend Kafka health endpoint" \
        "curl -f http://localhost:8000/health/kafka >/dev/null 2>&1"
    
    run_test "API documentation accessible" \
        "curl -f http://localhost:8000/docs >/dev/null 2>&1"
}

# Performance tests
test_performance() {
    log "Testing performance benchmarks..."
    
    # Database performance test
    run_test "Database query performance (<100ms)" \
        "timeout 5s docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT COUNT(*) FROM information_schema.tables;\" >/dev/null 2>&1"
    
    # API response time test
    run_test "API response time (<500ms)" \
        "timeout 2s curl -f http://localhost:8000/health >/dev/null 2>&1"
    
    # Kafka producer performance test
    run_test "Kafka message production test" \
        "timeout 10s docker-compose exec -T kafka kafka-console-producer --bootstrap-server localhost:9092 --topic validation_topic <<< 'test_message' >/dev/null 2>&1"
    
    # Kafka consumer performance test
    run_test "Kafka message consumption test" \
        "timeout 5s docker-compose exec -T kafka kafka-console-consumer --bootstrap-server localhost:9092 --topic validation_topic --from-beginning --max-messages 1 >/dev/null 2>&1"
}

# Security tests
test_security() {
    log "Testing security configurations..."
    
    run_test "Database password protection" \
        "! docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT 1;\" 2>&1 | grep -q 'password authentication failed'"
    
    run_test "Redis password protection" \
        "docker-compose exec -T redis redis-cli ping | grep -q PONG"
    
    run_test "Backend security headers" \
        "curl -I http://localhost:8000/health 2>/dev/null | grep -q 'X-Content-Type-Options'"
    
    run_test "Backend CORS configuration" \
        "curl -I http://localhost:8000/health 2>/dev/null | grep -q 'Access-Control-Allow-Origin' || true"  # May not be present in basic health check
}

# Data integrity tests
test_data_integrity() {
    log "Testing data integrity and validation..."
    
    # Test database hash function
    run_test "Database hash function available" \
        "docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT validate_data_hash('test', encode(digest('test', 'sha256'), 'hex'));\" | grep -q 't'"
    
    # Test cryptographic functions
    run_test "Database cryptographic functions available" \
        "docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT LENGTH(encode(digest('test', 'sha256'), 'hex'));\" | grep -q '64'"
    
    # Test TimescaleDB specific functions
    run_test "TimescaleDB hypertable functions available" \
        "docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c \"SELECT COUNT(*) FROM timescaledb_information.hypertable;\" >/dev/null 2>&1"
}

# Schema validation tests
test_schema_validation() {
    log "Testing schema validation..."
    
    run_test "Financial events schema registered" \
        "curl -f http://localhost:8081/subjects/financial_events-value/versions >/dev/null 2>&1"
    
    run_test "Risk analysis schema registered" \
        "curl -f http://localhost:8081/subjects/risk_analysis-value/versions >/dev/null 2>&1"
    
    # Test schema compatibility
    run_test "Schema Registry compatibility check" \
        "curl -f http://localhost:8081/config >/dev/null 2>&1"
}

# Monitoring and observability tests
test_monitoring() {
    log "Testing monitoring and observability..."
    
    run_test "Backend metrics endpoint" \
        "curl -f http://localhost:8000/metrics >/dev/null 2>&1"
    
    run_test "Backend logs structured format" \
        "docker-compose logs backend --tail=10 | grep -q 'timestamp' || docker-compose logs backend --tail=10 | grep -q 'INFO'"
    
    run_test "Database logs accessible" \
        "docker-compose logs timescaledb --tail=5 >/dev/null 2>&1"
}

# Cleanup and resource tests
test_resource_usage() {
    log "Testing resource usage and cleanup..."
    
    # Check memory usage
    run_test "Backend memory usage reasonable" \
        "docker stats --no-stream --format 'table {{.MemUsage}}' quantum-backend | tail -n +2 | grep -v 'N/A'"
    
    # Check disk usage
    run_test "Database disk usage reasonable" \
        "docker exec quantum-timescaledb df -h /var/lib/postgresql/data | tail -1 | awk '{print \$5}' | sed 's/%//' | awk '{if(\$1 < 90) exit 0; else exit 1}'"
    
    # Check network connectivity
    run_test "Container network connectivity" \
        "docker-compose exec -T backend ping -c 1 timescaledb >/dev/null 2>&1"
}

# Generate test report
generate_report() {
    log "Generating test report..."
    
    echo ""
    echo "=========================================="
    echo "Phase 0 Foundation Test Report"
    echo "=========================================="
    echo "Total tests run: $TESTS_RUN"
    echo "Tests passed: $TESTS_PASSED"
    echo "Tests failed: $TESTS_FAILED"
    echo "Success rate: $(( TESTS_PASSED * 100 / TESTS_RUN ))%"
    echo "=========================================="
    
    if [ $TESTS_FAILED -eq 0 ]; then
        success "All tests passed! Phase 0 foundation is ready."
        echo ""
        echo "Next steps:"
        echo "1. Proceed to Phase 1: Data Ingestion Pipeline"
        echo "2. Review logs: docker-compose logs -f"
        echo "3. Monitor health: curl http://localhost:8000/health/detailed"
        return 0
    else
        error "Some tests failed. Please review and fix issues before proceeding."
        echo ""
        echo "Troubleshooting:"
        echo "1. Check service logs: docker-compose logs [service-name]"
        echo "2. Verify environment configuration: cat .env"
        echo "3. Restart services: docker-compose restart"
        return 1
    fi
}

# Main test execution
main() {
    log "Starting Phase 0 foundation testing..."
    
    # Check if services are running
    if ! docker-compose ps | grep -q "Up"; then
        error "Services are not running. Please run ./scripts/setup.sh first."
        exit 1
    fi
    
    # Run all test suites
    test_docker_services
    test_database_connectivity
    test_kafka_connectivity
    test_redis_connectivity
    test_backend_api
    test_performance
    test_security
    test_data_integrity
    test_schema_validation
    test_monitoring
    test_resource_usage
    
    # Generate final report
    generate_report
}

# Run main function
main "$@"
