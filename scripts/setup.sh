#!/bin/bash

# Quantum Market Intelligence Hub - Foundation Setup Script
# Implements financial-grade infrastructure with zero-trust validation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log "Checking Docker availability..."
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    success "Docker is running"
}

# Check if Docker Compose is available
check_docker_compose() {
    log "Checking Docker Compose availability..."
    if ! command -v docker-compose >/dev/null 2>&1; then
        error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    success "Docker Compose is available"
}

# Create .env file if it doesn't exist
setup_environment() {
    log "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        log "Creating .env file from template..."
        cp .env.example .env
        
        # Generate secure passwords
        POSTGRES_PASSWORD=$(openssl rand -hex 16)
        REDIS_PASSWORD=$(openssl rand -hex 16)
        JWT_SECRET=$(openssl rand -hex 32)
        ENCRYPTION_KEY=$(openssl rand -hex 16)

        # Update .env file with generated passwords using a more robust approach
        python3 -c "
import re
with open('.env', 'r') as f:
    content = f.read()
content = re.sub(r'quantum_secure_pass_2024', '$POSTGRES_PASSWORD', content)
content = re.sub(r'quantum_redis_pass_2024', '$REDIS_PASSWORD', content)
content = re.sub(r'your_jwt_secret_key_here', '$JWT_SECRET', content)
content = re.sub(r'your_encryption_key_here', '$ENCRYPTION_KEY', content)
with open('.env', 'w') as f:
    f.write(content)
"
        
        success "Environment file created with secure passwords"
        warning "Please update .env file with your API keys before proceeding"
    else
        success "Environment file already exists"
    fi
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p infrastructure/timescaledb/init
    mkdir -p infrastructure/timescaledb/config
    mkdir -p infrastructure/kafka/schemas
    mkdir -p infrastructure/kafka/config
    mkdir -p infrastructure/monitoring/prometheus
    mkdir -p infrastructure/monitoring/grafana
    mkdir -p logs
    mkdir -p data
    
    success "Directories created"
}

# Start infrastructure services
start_infrastructure() {
    log "Starting infrastructure services..."
    
    # Start core services first
    docker-compose up -d timescaledb redis zookeeper
    
    log "Waiting for core services to be ready..."
    sleep 10
    
    # Start Kafka and Schema Registry
    docker-compose up -d kafka schema-registry
    
    log "Waiting for Kafka services to be ready..."
    sleep 15
    
    success "Infrastructure services started"
}

# Wait for services to be healthy
wait_for_services() {
    log "Waiting for services to be healthy..."
    
    # Wait for TimescaleDB
    log "Checking TimescaleDB health..."
    for i in {1..30}; do
        if docker-compose exec -T timescaledb pg_isready -U quantum_user -d quantum_market_db >/dev/null 2>&1; then
            success "TimescaleDB is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            error "TimescaleDB failed to start within timeout"
            exit 1
        fi
        sleep 2
    done
    
    # Wait for Kafka
    log "Checking Kafka health..."
    for i in {1..30}; do
        if docker-compose exec -T kafka kafka-broker-api-versions --bootstrap-server localhost:9092 >/dev/null 2>&1; then
            success "Kafka is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            error "Kafka failed to start within timeout"
            exit 1
        fi
        sleep 2
    done
    
    # Wait for Schema Registry
    log "Checking Schema Registry health..."
    for i in {1..30}; do
        if curl -f http://localhost:8081/subjects >/dev/null 2>&1; then
            success "Schema Registry is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            error "Schema Registry failed to start within timeout"
            exit 1
        fi
        sleep 2
    done
    
    # Wait for Redis
    log "Checking Redis health..."
    for i in {1..30}; do
        if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
            success "Redis is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            error "Redis failed to start within timeout"
            exit 1
        fi
        sleep 2
    done
}

# Initialize database
initialize_database() {
    log "Initializing TimescaleDB with quantum market schema..."
    
    # The database initialization script should run automatically
    # via the init script in docker-compose, but we can verify it worked
    
    if docker-compose exec -T timescaledb psql -U quantum_user -d quantum_market_db -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'market_data';" >/dev/null 2>&1; then
        success "Database schema initialized successfully"
    else
        error "Database schema initialization failed"
        exit 1
    fi
}

# Create Kafka topics
create_kafka_topics() {
    log "Creating Kafka topics for financial data streams..."
    
    # Financial events topic
    docker-compose exec -T kafka kafka-topics --create \
        --bootstrap-server localhost:9092 \
        --topic financial_events \
        --partitions 3 \
        --replication-factor 1 \
        --config retention.ms=604800000 \
        --config segment.ms=86400000 \
        --config compression.type=snappy \
        --if-not-exists
    
    # Risk analysis topic
    docker-compose exec -T kafka kafka-topics --create \
        --bootstrap-server localhost:9092 \
        --topic risk_analysis \
        --partitions 3 \
        --replication-factor 1 \
        --config retention.ms=604800000 \
        --config segment.ms=86400000 \
        --config compression.type=snappy \
        --if-not-exists
    
    # Validation topic for smoke tests
    docker-compose exec -T kafka kafka-topics --create \
        --bootstrap-server localhost:9092 \
        --topic validation_topic \
        --partitions 1 \
        --replication-factor 1 \
        --config retention.ms=3600000 \
        --if-not-exists
    
    success "Kafka topics created"
}

# Register Avro schemas
register_schemas() {
    log "Registering Avro schemas with Schema Registry..."
    
    # Register financial events schema
    if [ -f "infrastructure/kafka/schemas/financial_events.avsc" ]; then
        curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" \
            --data @infrastructure/kafka/schemas/financial_events.avsc \
            http://localhost:8081/subjects/financial_events-value/versions
        success "Financial events schema registered"
    fi
    
    # Register risk analysis schema
    if [ -f "infrastructure/kafka/schemas/risk_analysis.avsc" ]; then
        curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" \
            --data @infrastructure/kafka/schemas/risk_analysis.avsc \
            http://localhost:8081/subjects/risk_analysis-value/versions
        success "Risk analysis schema registered"
    fi
}

# Start backend service
start_backend() {
    log "Starting backend service..."
    
    docker-compose up -d backend
    
    log "Waiting for backend to be ready..."
    for i in {1..60}; do
        if curl -f http://localhost:8000/health >/dev/null 2>&1; then
            success "Backend service is ready"
            break
        fi
        if [ $i -eq 60 ]; then
            error "Backend service failed to start within timeout"
            exit 1
        fi
        sleep 2
    done
}

# Run comprehensive health check
run_health_check() {
    log "Running comprehensive health check..."
    
    # Check detailed health endpoint
    if curl -f http://localhost:8000/health/detailed >/dev/null 2>&1; then
        success "All services are healthy"
    else
        error "Health check failed"
        exit 1
    fi
}

# Main setup function
main() {
    log "Starting Quantum Market Intelligence Hub foundation setup..."
    
    check_docker
    check_docker_compose
    setup_environment
    create_directories
    start_infrastructure
    wait_for_services
    initialize_database
    create_kafka_topics
    register_schemas
    start_backend
    run_health_check
    
    success "Foundation setup completed successfully!"
    log "Services are running at:"
    log "  - Backend API: http://localhost:8000"
    log "  - API Documentation: http://localhost:8000/docs"
    log "  - Health Check: http://localhost:8000/health"
    log "  - TimescaleDB: localhost:5433"
    log "  - Kafka: localhost:9092"
    log "  - Schema Registry: http://localhost:8081"
    log "  - Redis: localhost:6381"
    log ""
    log "To view logs: docker-compose logs -f"
    log "To stop services: docker-compose down"
}

# Run main function
main "$@"
