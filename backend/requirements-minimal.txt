# Database and core dependencies
fastapi==0.104.1
uvicorn==0.24.0
python-dotenv==1.0.0

# Database dependencies
sqlalchemy==2.0.23
asyncpg==0.29.0
psycopg2-binary==2.9.9
alembic==1.13.1

# Configuration and logging
pydantic==2.5.0
pydantic-settings==2.1.0
structlog==23.2.0

# HTTP client and WebSocket
aiohttp==3.9.1
websockets==12.0

# Testing and validation
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# OpenTelemetry dependencies
opentelemetry-api
opentelemetry-sdk
opentelemetry-exporter-otlp
opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-redis
opentelemetry-instrumentation-sqlalchemy

# Monitoring and rate limiting
prometheus-client
slowapi

# Redis client
redis

# Logging
python-json-logger

# Security
cryptography
python-jose
passlib
