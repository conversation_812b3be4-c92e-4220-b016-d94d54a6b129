-- Create pattern_alerts table for storing production alerts
-- This table stores all alerts generated by the production monitoring system

CREATE TABLE IF NOT EXISTS pattern_alerts (
    id SERIAL PRIMARY KEY,
    pattern_id VARCHAR(255) NOT NULL,
    alert_type VARCHAR(100) NOT NULL,
    priority VARCHAR(20) NOT NULL CHECK (priority IN ('critical', 'high', 'medium', 'low')),
    message TEXT NOT NULL,
    alert_data JSONB,
    sent_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by <PERSON><PERSON><PERSON><PERSON>(255),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_pattern_alerts_pattern_id ON pattern_alerts(pattern_id);
CREATE INDEX IF NOT EXISTS idx_pattern_alerts_priority ON pattern_alerts(priority);
CREATE INDEX IF NOT EXISTS idx_pattern_alerts_sent_timestamp ON pattern_alerts(sent_timestamp);
CREATE INDEX IF NOT EXISTS idx_pattern_alerts_acknowledged ON pattern_alerts(acknowledged);
CREATE INDEX IF NOT EXISTS idx_pattern_alerts_resolved ON pattern_alerts(resolved);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_pattern_alerts_priority_timestamp ON pattern_alerts(priority, sent_timestamp DESC);

-- Add comments for documentation
COMMENT ON TABLE pattern_alerts IS 'Stores production monitoring alerts generated by pattern detection system';
COMMENT ON COLUMN pattern_alerts.pattern_id IS 'Unique identifier of the pattern that triggered the alert';
COMMENT ON COLUMN pattern_alerts.alert_type IS 'Type of alert (e.g., cex_listing_production_alert)';
COMMENT ON COLUMN pattern_alerts.priority IS 'Alert priority level: critical, high, medium, low';
COMMENT ON COLUMN pattern_alerts.message IS 'Human-readable alert message';
COMMENT ON COLUMN pattern_alerts.alert_data IS 'JSON data containing detailed alert information';
COMMENT ON COLUMN pattern_alerts.sent_timestamp IS 'When the alert was sent/generated';
COMMENT ON COLUMN pattern_alerts.acknowledged IS 'Whether the alert has been acknowledged by a user';
COMMENT ON COLUMN pattern_alerts.resolved IS 'Whether the underlying issue has been resolved';
