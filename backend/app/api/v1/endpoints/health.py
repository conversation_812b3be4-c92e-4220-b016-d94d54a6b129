"""
Health check endpoints for Quantum Market Intelligence Hub.
Implements comprehensive system health monitoring for financial-grade reliability.
"""

import asyncio
import time
from typing import Any, Dict

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.db.session import get_db, db_manager
from app.services.kafka_service import kafka_smoke_test

logger = structlog.get_logger(__name__)

router = APIRouter()


@router.get("/")
async def basic_health_check() -> Dict[str, Any]:
    """Basic health check for load balancers."""
    return {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "timestamp": time.time()
    }


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    Comprehensive health check including all system components.
    
    Returns:
        Detailed health status of all components
    """
    start_time = time.time()
    health_status = {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "timestamp": time.time(),
        "components": {}
    }
    
    # Check database connectivity
    try:
        db_start = time.time()
        await db.execute("SELECT 1")
        db_time = time.time() - db_start
        
        health_status["components"]["database"] = {
            "status": "healthy",
            "response_time_ms": db_time * 1000,
            "type": "TimescaleDB"
        }
        
        logger.debug("Database health check passed", response_time_ms=db_time * 1000)
        
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "type": "TimescaleDB"
        }
        logger.error("Database health check failed", error=str(e))
    
    # Check Kafka connectivity
    try:
        kafka_start = time.time()
        kafka_healthy = await kafka_smoke_test()
        kafka_time = time.time() - kafka_start
        
        if kafka_healthy:
            health_status["components"]["kafka"] = {
                "status": "healthy",
                "response_time_ms": kafka_time * 1000,
                "type": "Apache Kafka"
            }
            logger.debug("Kafka health check passed", response_time_ms=kafka_time * 1000)
        else:
            health_status["status"] = "unhealthy"
            health_status["components"]["kafka"] = {
                "status": "unhealthy",
                "error": "Smoke test failed",
                "type": "Apache Kafka"
            }
            
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["components"]["kafka"] = {
            "status": "unhealthy",
            "error": str(e),
            "type": "Apache Kafka"
        }
        logger.error("Kafka health check failed", error=str(e))
    
    # Check Redis connectivity (if configured)
    try:
        import redis.asyncio as redis
        redis_start = time.time()
        
        redis_client = redis.from_url(settings.get_redis_url())
        await redis_client.ping()
        await redis_client.close()
        
        redis_time = time.time() - redis_start
        
        health_status["components"]["redis"] = {
            "status": "healthy",
            "response_time_ms": redis_time * 1000,
            "type": "Redis"
        }
        
        logger.debug("Redis health check passed", response_time_ms=redis_time * 1000)
        
    except Exception as e:
        health_status["status"] = "degraded"  # Redis is not critical
        health_status["components"]["redis"] = {
            "status": "unhealthy",
            "error": str(e),
            "type": "Redis"
        }
        logger.warning("Redis health check failed", error=str(e))
    
    # Check external API connectivity (sample)
    external_apis = []
    
    if settings.THIRDWEB_CLIENT_ID:
        try:
            import httpx
            async with httpx.AsyncClient(timeout=5.0) as client:
                api_start = time.time()
                response = await client.get("https://api.thirdweb.com/v1/chains")
                api_time = time.time() - api_start
                
                if response.status_code == 200:
                    external_apis.append({
                        "name": "thirdweb",
                        "status": "healthy",
                        "response_time_ms": api_time * 1000
                    })
                else:
                    external_apis.append({
                        "name": "thirdweb",
                        "status": "unhealthy",
                        "error": f"HTTP {response.status_code}"
                    })
                    
        except Exception as e:
            external_apis.append({
                "name": "thirdweb",
                "status": "unhealthy",
                "error": str(e)
            })
    
    if external_apis:
        health_status["components"]["external_apis"] = external_apis
    
    # Overall health determination
    component_statuses = [
        comp.get("status", "unknown") 
        for comp in health_status["components"].values()
        if isinstance(comp, dict)
    ]
    
    # Flatten external API statuses
    for comp in health_status["components"].values():
        if isinstance(comp, list):
            component_statuses.extend([api.get("status", "unknown") for api in comp])
    
    if "unhealthy" in component_statuses:
        health_status["status"] = "unhealthy"
    elif "degraded" in component_statuses:
        health_status["status"] = "degraded"
    
    total_time = time.time() - start_time
    health_status["total_check_time_ms"] = total_time * 1000
    
    logger.info(
        "Health check completed",
        status=health_status["status"],
        total_time_ms=total_time * 1000,
        components_checked=len(health_status["components"])
    )
    
    # Return appropriate HTTP status
    if health_status["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_status
        )
    
    return health_status


@router.get("/database")
async def database_health_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    Detailed database health check with TimescaleDB-specific metrics.
    
    Returns:
        Database health status and performance metrics
    """
    start_time = time.time()
    
    try:
        # Basic connectivity test
        await db.execute("SELECT 1")
        
        # TimescaleDB specific checks
        timescaledb_version = await db.execute("SELECT extversion FROM pg_extension WHERE extname = 'timescaledb'")
        version_result = timescaledb_version.fetchone()
        
        # Check hypertable status
        hypertables_query = """
        SELECT schemaname, tablename, num_chunks, table_bytes, index_bytes, total_bytes
        FROM timescaledb_information.hypertable
        LIMIT 5
        """
        hypertables_result = await db.execute(hypertables_query)
        hypertables = [dict(row) for row in hypertables_result.fetchall()]
        
        # Database performance metrics
        performance_query = """
        SELECT 
            pg_database_size(current_database()) as database_size_bytes,
            (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
            (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections
        """
        perf_result = await db.execute(performance_query)
        performance = dict(perf_result.fetchone())
        
        response_time = time.time() - start_time
        
        return {
            "status": "healthy",
            "response_time_ms": response_time * 1000,
            "timescaledb_version": version_result[0] if version_result else "unknown",
            "hypertables": hypertables,
            "performance": performance,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


@router.get("/kafka")
async def kafka_health_check() -> Dict[str, Any]:
    """
    Kafka connectivity and performance health check.
    
    Returns:
        Kafka health status and metrics
    """
    start_time = time.time()
    
    try:
        # Perform smoke test
        smoke_test_passed = await kafka_smoke_test()
        
        if not smoke_test_passed:
            raise Exception("Kafka smoke test failed")
        
        # Additional Kafka metrics could be added here
        # such as topic metadata, consumer lag, etc.
        
        response_time = time.time() - start_time
        
        return {
            "status": "healthy",
            "response_time_ms": response_time * 1000,
            "smoke_test": "passed",
            "bootstrap_servers": settings.KAFKA_BOOTSTRAP_SERVERS,
            "schema_registry": settings.SCHEMA_REGISTRY_URL,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error("Kafka health check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


@router.get("/readiness")
async def readiness_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    Kubernetes readiness probe - checks if service is ready to accept traffic.
    
    Returns:
        Readiness status
    """
    try:
        # Check critical dependencies
        await db.execute("SELECT 1")
        kafka_ready = await kafka_smoke_test()
        
        if not kafka_ready:
            raise Exception("Kafka not ready")
        
        return {
            "status": "ready",
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "not_ready",
                "error": str(e),
                "timestamp": time.time()
            }
        )


@router.get("/liveness")
async def liveness_check() -> Dict[str, Any]:
    """
    Kubernetes liveness probe - checks if service is alive.
    
    Returns:
        Liveness status
    """
    return {
        "status": "alive",
        "timestamp": time.time()
    }
