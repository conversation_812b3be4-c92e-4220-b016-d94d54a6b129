"""
Pattern Detection API endpoints for Quantum Market Intelligence.
Provides REST API for pattern analysis, monitoring, and alerts.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

# from app.api.deps import get_current_user  # Not needed for now
from app.db.deps import get_db_session
from app.models.pattern_detection import (
    PatternType, PatternDetectionRequest, PatternAnalysisResponse,
    PatternMonitoringStatus, PatternAlertRequest, PatternAlertResponse,
    DetectedPattern, PatternAlert
)
from app.services.pattern_detection import pattern_detection_service

logger = logging.getLogger(__name__)

router = APIRouter()


# ============================================================================
# PATTERN ANALYSIS ENDPOINTS
# ============================================================================

@router.post("/analyze", response_model=PatternAnalysisResponse)
async def analyze_patterns(
    request: PatternDetectionRequest,
    db: AsyncSession = Depends(get_db_session)
) -> PatternAnalysisResponse:
    """
    Analyze patterns for the specified symbols and criteria.
    
    This endpoint performs comprehensive pattern analysis using all available
    detectors and returns detailed results with confidence scores.
    """
    try:
        logger.info(f"Pattern analysis requested for symbols: {request.symbols}")
        
        # Validate request
        if not request.symbols:
            raise HTTPException(status_code=400, detail="At least one symbol must be specified")
        
        if len(request.symbols) > 20:
            raise HTTPException(status_code=400, detail="Maximum 20 symbols allowed per request")
        
        # Ensure pattern detection service is started
        if not pattern_detection_service.db_session:
            await pattern_detection_service.start()
        
        # Run pattern analysis
        response = await pattern_detection_service.analyze_patterns(request)
        
        logger.info(f"Pattern analysis completed: {response.total_patterns} patterns detected")
        return response
        
    except Exception as e:
        logger.error(f"Pattern analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Pattern analysis failed: {str(e)}")


@router.get("/patterns", response_model=List[Dict[str, Any]])
async def get_detected_patterns(
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    pattern_type: Optional[PatternType] = Query(None, description="Filter by pattern type"),
    min_confidence: float = Query(0.0, ge=0.0, le=1.0, description="Minimum confidence score"),
    hours_back: int = Query(24, ge=1, le=168, description="Hours of history to retrieve"),
    limit: int = Query(50, ge=1, le=500, description="Maximum number of patterns to return"),
    db: AsyncSession = Depends(get_db_session)
) -> List[Dict[str, Any]]:
    """
    Retrieve detected patterns with optional filtering.
    
    Returns a list of patterns detected within the specified time range,
    optionally filtered by symbol, pattern type, and confidence threshold.
    """
    try:
        # Build query conditions
        conditions = []
        params = {}
        
        # Time filter
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours_back)
        conditions.append("detection_timestamp >= :cutoff_time")
        params["cutoff_time"] = cutoff_time
        
        # Symbol filter
        if symbol:
            conditions.append("symbol = :symbol")
            params["symbol"] = symbol.upper()
        
        # Pattern type filter
        if pattern_type:
            conditions.append("pattern_type = :pattern_type")
            params["pattern_type"] = pattern_type.value
        
        # Confidence filter
        if min_confidence > 0:
            conditions.append("confidence_score >= :min_confidence")
            params["min_confidence"] = min_confidence
        
        # Build query
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        query = text(f"""
            SELECT 
                id, pattern_type, symbol, confidence_score, status,
                pattern_data, detection_timestamp, expiry_timestamp,
                trigger_conditions, supporting_indicators, risk_factors,
                detector_version, created_at
            FROM detected_patterns 
            WHERE {where_clause}
            ORDER BY detection_timestamp DESC 
            LIMIT :limit
        """)
        
        params["limit"] = limit
        
        result = await db.execute(query, params)
        rows = result.fetchall()
        
        # Convert to response format
        patterns = []
        for row in rows:
            pattern = {
                "pattern_id": str(row.id),
                "pattern_type": row.pattern_type,
                "symbol": row.symbol,
                "confidence_score": float(row.confidence_score),
                "status": row.status,
                "pattern_data": row.pattern_data,
                "detection_timestamp": row.detection_timestamp.isoformat(),
                "expiry_timestamp": row.expiry_timestamp.isoformat() if row.expiry_timestamp else None,
                "trigger_conditions": row.trigger_conditions,
                "supporting_indicators": row.supporting_indicators,
                "risk_factors": row.risk_factors,
                "detector_version": row.detector_version,
                "created_at": row.created_at.isoformat()
            }
            patterns.append(pattern)
        
        logger.info(f"Retrieved {len(patterns)} patterns")
        return patterns
        
    except Exception as e:
        logger.error(f"Error retrieving patterns: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve patterns: {str(e)}")


@router.get("/patterns/{pattern_id}", response_model=Dict[str, Any])
async def get_pattern_details(
    pattern_id: UUID,
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Get detailed information about a specific pattern.
    
    Returns comprehensive details about a detected pattern including
    all analysis data, risk factors, and performance metrics.
    """
    try:
        query = text("""
            SELECT 
                id, pattern_type, symbol, confidence_score, status,
                pattern_data, detection_timestamp, expiry_timestamp,
                trigger_conditions, supporting_indicators, risk_factors,
                accuracy_score, outcome_verified, verification_timestamp,
                detector_version, created_at, updated_at
            FROM detected_patterns 
            WHERE id = :pattern_id
        """)
        
        result = await db.execute(query, {"pattern_id": pattern_id})
        row = result.fetchone()
        
        if not row:
            raise HTTPException(status_code=404, detail="Pattern not found")
        
        # Get associated alerts
        alerts_query = text("""
            SELECT id, alert_type, priority, message, alert_data, 
                   sent_timestamp, acknowledged, created_at
            FROM pattern_alerts 
            WHERE pattern_id = :pattern_id
            ORDER BY created_at DESC
        """)
        
        alerts_result = await db.execute(alerts_query, {"pattern_id": pattern_id})
        alerts_rows = alerts_result.fetchall()
        
        alerts = []
        for alert_row in alerts_rows:
            alert = {
                "alert_id": str(alert_row.id),
                "alert_type": alert_row.alert_type,
                "priority": alert_row.priority,
                "message": alert_row.message,
                "alert_data": alert_row.alert_data,
                "sent_timestamp": alert_row.sent_timestamp.isoformat() if alert_row.sent_timestamp else None,
                "acknowledged": alert_row.acknowledged,
                "created_at": alert_row.created_at.isoformat()
            }
            alerts.append(alert)
        
        # Build response
        pattern_details = {
            "pattern_id": str(row.id),
            "pattern_type": row.pattern_type,
            "symbol": row.symbol,
            "confidence_score": float(row.confidence_score),
            "status": row.status,
            "pattern_data": row.pattern_data,
            "detection_timestamp": row.detection_timestamp.isoformat(),
            "expiry_timestamp": row.expiry_timestamp.isoformat() if row.expiry_timestamp else None,
            "trigger_conditions": row.trigger_conditions,
            "supporting_indicators": row.supporting_indicators,
            "risk_factors": row.risk_factors,
            "accuracy_score": float(row.accuracy_score) if row.accuracy_score else None,
            "outcome_verified": row.outcome_verified,
            "verification_timestamp": row.verification_timestamp.isoformat() if row.verification_timestamp else None,
            "detector_version": row.detector_version,
            "created_at": row.created_at.isoformat(),
            "updated_at": row.updated_at.isoformat(),
            "alerts": alerts
        }
        
        return pattern_details
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving pattern details: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve pattern details: {str(e)}")


# ============================================================================
# PATTERN MONITORING ENDPOINTS
# ============================================================================

@router.post("/monitoring/start")
async def start_pattern_monitoring(
    symbols: List[str],
    interval_seconds: int = Query(300, ge=60, le=3600, description="Monitoring interval in seconds")
) -> Dict[str, Any]:
    """
    Start real-time pattern monitoring for specified symbols.
    
    Begins continuous monitoring of the specified symbols for pattern detection
    with the given interval. Generates alerts when patterns are detected.
    """
    try:
        if not symbols:
            raise HTTPException(status_code=400, detail="At least one symbol must be specified")
        
        if len(symbols) > 50:
            raise HTTPException(status_code=400, detail="Maximum 50 symbols allowed for monitoring")
        
        # Ensure pattern detection service is started
        if not pattern_detection_service.db_session:
            await pattern_detection_service.start()
        
        # Start monitoring
        await pattern_detection_service.start_monitoring(symbols, interval_seconds)
        
        return {
            "status": "success",
            "message": f"Pattern monitoring started for {len(symbols)} symbols",
            "symbols": symbols,
            "interval_seconds": interval_seconds,
            "started_at": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to start pattern monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")


@router.post("/monitoring/stop")
async def stop_pattern_monitoring() -> Dict[str, Any]:
    """
    Stop real-time pattern monitoring.
    
    Stops the continuous pattern monitoring process and cleans up resources.
    """
    try:
        await pattern_detection_service.stop_monitoring()
        
        return {
            "status": "success",
            "message": "Pattern monitoring stopped",
            "stopped_at": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to stop pattern monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop monitoring: {str(e)}")


@router.get("/monitoring/status", response_model=PatternMonitoringStatus)
async def get_monitoring_status() -> PatternMonitoringStatus:
    """
    Get current pattern monitoring status.
    
    Returns detailed information about the monitoring service including
    active symbols, detection rates, and performance metrics.
    """
    try:
        status = await pattern_detection_service.get_monitoring_status()
        return status
        
    except Exception as e:
        logger.error(f"Failed to get monitoring status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


# ============================================================================
# PATTERN STATISTICS ENDPOINTS
# ============================================================================

@router.get("/statistics")
async def get_pattern_statistics(
    hours_back: int = Query(24, ge=1, le=168, description="Hours of history for statistics"),
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Get pattern detection statistics and performance metrics.
    
    Returns comprehensive statistics about pattern detection including
    detection rates, accuracy metrics, and pattern distribution.
    """
    try:
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours_back)
        
        # Get pattern counts by type
        pattern_counts_query = text("""
            SELECT pattern_type, COUNT(*) as count, AVG(confidence_score) as avg_confidence
            FROM detected_patterns 
            WHERE detection_timestamp >= :cutoff_time
            GROUP BY pattern_type
            ORDER BY count DESC
        """)
        
        result = await db.execute(pattern_counts_query, {"cutoff_time": cutoff_time})
        pattern_counts = result.fetchall()
        
        # Get total statistics
        total_stats_query = text("""
            SELECT 
                COUNT(*) as total_patterns,
                AVG(confidence_score) as avg_confidence,
                COUNT(CASE WHEN confidence_score >= 0.8 THEN 1 END) as high_confidence_patterns,
                COUNT(CASE WHEN outcome_verified = true THEN 1 END) as verified_patterns,
                AVG(CASE WHEN accuracy_score IS NOT NULL THEN accuracy_score END) as avg_accuracy
            FROM detected_patterns 
            WHERE detection_timestamp >= :cutoff_time
        """)
        
        result = await db.execute(total_stats_query, {"cutoff_time": cutoff_time})
        total_stats = result.fetchone()
        
        # Get symbol distribution
        symbol_stats_query = text("""
            SELECT symbol, COUNT(*) as pattern_count
            FROM detected_patterns 
            WHERE detection_timestamp >= :cutoff_time
            GROUP BY symbol
            ORDER BY pattern_count DESC
            LIMIT 10
        """)
        
        result = await db.execute(symbol_stats_query, {"cutoff_time": cutoff_time})
        symbol_stats = result.fetchall()
        
        # Build response
        statistics = {
            "time_period_hours": hours_back,
            "total_patterns": total_stats.total_patterns or 0,
            "average_confidence": float(total_stats.avg_confidence or 0.0),
            "high_confidence_patterns": total_stats.high_confidence_patterns or 0,
            "verified_patterns": total_stats.verified_patterns or 0,
            "average_accuracy": float(total_stats.avg_accuracy or 0.0),
            "pattern_distribution": {
                row.pattern_type: {
                    "count": row.count,
                    "avg_confidence": float(row.avg_confidence)
                }
                for row in pattern_counts
            },
            "top_symbols": {
                row.symbol: row.pattern_count
                for row in symbol_stats
            },
            "generated_at": datetime.now(timezone.utc).isoformat()
        }
        
        return statistics
        
    except Exception as e:
        logger.error(f"Error retrieving pattern statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve statistics: {str(e)}")
