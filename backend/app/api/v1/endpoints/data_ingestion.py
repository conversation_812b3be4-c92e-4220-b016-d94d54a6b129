"""
Data ingestion API endpoints for Quantum Market Intelligence Hub.
Provides endpoints for triggering and monitoring data ingestion processes.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.db.deps import get_db_session
from app.services.data_ingestion import data_ingestion_service
from app.services.crypto_validation import CryptoValidator

logger = logging.getLogger(__name__)
router = APIRouter()


class IngestionRequest(BaseModel):
    """Request model for data ingestion."""
    symbols: List[str] = Field(..., description="List of cryptocurrency symbols to ingest")
    sources: List[str] = Field(default=["coingecko"], description="Data sources to use")
    continuous: bool = Field(default=False, description="Enable continuous ingestion")


class IngestionResponse(BaseModel):
    """Response model for data ingestion."""
    status: str
    message: str
    symbols_requested: List[str]
    sources_used: List[str]
    results: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class ValidationRequest(BaseModel):
    """Request model for data validation."""
    data: Dict[str, Any] = Field(..., description="Data to validate")
    expected_hash: Optional[str] = Field(None, description="Expected hash for integrity check")
    signature: Optional[str] = Field(None, description="Signature for authenticity check")


class ValidationResponse(BaseModel):
    """Response model for data validation."""
    is_valid: bool
    confidence_score: float
    error_message: Optional[str] = None
    anomaly_detected: bool = False
    validation_timestamp: datetime


class IngestionStats(BaseModel):
    """Statistics for data ingestion."""
    total_records: int
    records_by_source: Dict[str, int]
    records_by_symbol: Dict[str, int]
    latest_timestamp: Optional[datetime]
    validation_stats: Dict[str, Any]


@router.post("/ingest", response_model=IngestionResponse)
async def trigger_data_ingestion(
    request: IngestionRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db_session)
) -> IngestionResponse:
    """
    Trigger data ingestion for specified symbols and sources.
    """
    try:
        # Validate symbols
        if not request.symbols:
            raise HTTPException(status_code=400, detail="At least one symbol must be specified")
        
        # Validate symbols format
        valid_symbols = []
        for symbol in request.symbols:
            if isinstance(symbol, str) and len(symbol) >= 2:
                valid_symbols.append(symbol.upper())
            else:
                logger.warning(f"Invalid symbol format: {symbol}")
        
        if not valid_symbols:
            raise HTTPException(status_code=400, detail="No valid symbols provided")
        
        # Start the ingestion service if not already running
        if not data_ingestion_service.running:
            await data_ingestion_service.start()
        
        results = {}
        
        # Process each source
        for source in request.sources:
            if source == "coingecko":
                # Trigger CoinGecko ingestion
                coingecko_results = await data_ingestion_service.ingest_coingecko_data(valid_symbols)
                results["coingecko"] = coingecko_results
                
            elif source == "binance":
                # Start Binance WebSocket in background for continuous ingestion
                if request.continuous:
                    background_tasks.add_task(
                        data_ingestion_service.ingest_binance_websocket,
                        valid_symbols
                    )
                    results["binance"] = {"status": "websocket_started", "symbols": valid_symbols}
                else:
                    results["binance"] = {"status": "one_time_ingestion_not_supported"}
                    
            else:
                logger.warning(f"Unsupported data source: {source}")
                results[source] = {"status": "unsupported"}
        
        # Start continuous ingestion if requested
        if request.continuous:
            background_tasks.add_task(
                data_ingestion_service.run_continuous_ingestion,
                valid_symbols
            )
        
        return IngestionResponse(
            status="success",
            message=f"Data ingestion triggered for {len(valid_symbols)} symbols",
            symbols_requested=valid_symbols,
            sources_used=request.sources,
            results=results
        )
        
    except Exception as e:
        logger.error(f"Data ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=f"Ingestion failed: {str(e)}")


@router.post("/validate", response_model=ValidationResponse)
async def validate_data(request: ValidationRequest) -> ValidationResponse:
    """
    Validate data integrity and authenticity using cryptographic methods.
    """
    try:
        validator = CryptoValidator()
        
        # Perform financial data validation
        financial_result = validator.validate_financial_data(request.data)
        
        # Perform integrity validation if hash provided
        integrity_result = None
        if request.expected_hash:
            integrity_result = validator.validate_data_integrity(request.data, request.expected_hash)
        
        # Perform signature validation if signature provided
        signature_result = None
        if request.signature and request.expected_hash:
            signature_result = validator.validate_signature(request.expected_hash, request.signature)
        
        # Combine validation results
        overall_valid = financial_result.is_valid
        if integrity_result:
            overall_valid = overall_valid and integrity_result.is_valid
        if signature_result:
            overall_valid = overall_valid and signature_result.is_valid
        
        # Calculate combined confidence score
        confidence_scores = [financial_result.confidence_score]
        if integrity_result:
            confidence_scores.append(integrity_result.confidence_score)
        if signature_result:
            confidence_scores.append(signature_result.confidence_score)
        
        combined_confidence = sum(confidence_scores) / len(confidence_scores)
        
        # Collect error messages
        error_messages = []
        if financial_result.error_message:
            error_messages.append(f"Financial: {financial_result.error_message}")
        if integrity_result and integrity_result.error_message:
            error_messages.append(f"Integrity: {integrity_result.error_message}")
        if signature_result and signature_result.error_message:
            error_messages.append(f"Signature: {signature_result.error_message}")
        
        return ValidationResponse(
            is_valid=overall_valid,
            confidence_score=combined_confidence,
            error_message="; ".join(error_messages) if error_messages else None,
            anomaly_detected=financial_result.anomaly_detected,
            validation_timestamp=datetime.now(timezone.utc)
        )
        
    except Exception as e:
        logger.error(f"Data validation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.get("/stats", response_model=IngestionStats)
async def get_ingestion_stats(
    db: AsyncSession = Depends(get_db_session)
) -> IngestionStats:
    """
    Get statistics about ingested data.
    """
    try:
        # Get total records count
        result = await db.execute(text("SELECT COUNT(*) FROM market_data.price_events"))
        total_records = result.scalar() or 0
        
        # Get records by source
        result = await db.execute(text("""
            SELECT source, COUNT(*) as count 
            FROM market_data.price_events 
            GROUP BY source
        """))
        records_by_source = {row[0]: row[1] for row in result.fetchall()}
        
        # Get records by symbol
        result = await db.execute(text("""
            SELECT symbol, COUNT(*) as count 
            FROM market_data.price_events 
            GROUP BY symbol 
            ORDER BY count DESC 
            LIMIT 20
        """))
        records_by_symbol = {row[0]: row[1] for row in result.fetchall()}
        
        # Get latest timestamp
        result = await db.execute(text("""
            SELECT MAX(timestamp) 
            FROM market_data.price_events
        """))
        latest_timestamp = result.scalar()
        
        # Get validation stats
        validator = CryptoValidator()
        validation_stats = validator.get_validation_stats()
        
        return IngestionStats(
            total_records=total_records,
            records_by_source=records_by_source,
            records_by_symbol=records_by_symbol,
            latest_timestamp=latest_timestamp,
            validation_stats=validation_stats
        )
        
    except Exception as e:
        logger.error(f"Failed to get ingestion stats: {e}")
        raise HTTPException(status_code=500, detail=f"Stats retrieval failed: {str(e)}")


@router.get("/status")
async def get_ingestion_status() -> Dict[str, Any]:
    """
    Get current status of data ingestion service.
    """
    return {
        "service_running": data_ingestion_service.running,
        "session_active": data_ingestion_service.session is not None,
        "db_connected": data_ingestion_service.db_session is not None,
        "timestamp": datetime.now(timezone.utc)
    }


@router.post("/stop")
async def stop_ingestion() -> Dict[str, str]:
    """
    Stop continuous data ingestion.
    """
    try:
        await data_ingestion_service.stop()
        return {"status": "stopped", "message": "Data ingestion service stopped"}
    except Exception as e:
        logger.error(f"Failed to stop ingestion service: {e}")
        raise HTTPException(status_code=500, detail=f"Stop failed: {str(e)}")


@router.get("/recent-data")
async def get_recent_data(
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Get recent price data from the database.
    """
    try:
        # Build query
        query = """
            SELECT timestamp, symbol, exchange, price, volume, source, data_hash
            FROM market_data.price_events
        """
        params = {}
        
        if symbol:
            query += " WHERE symbol = :symbol"
            params["symbol"] = symbol.upper()
        
        query += " ORDER BY timestamp DESC LIMIT :limit"
        params["limit"] = limit
        
        result = await db.execute(text(query), params)
        rows = result.fetchall()
        
        # Convert to list of dictionaries
        data = []
        for row in rows:
            data.append({
                "timestamp": row[0].isoformat(),
                "symbol": row[1],
                "exchange": row[2],
                "price": float(row[3]),
                "volume": float(row[4]),
                "source": row[5],
                "data_hash": row[6]
            })
        
        return {
            "count": len(data),
            "symbol_filter": symbol,
            "data": data
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent data: {e}")
        raise HTTPException(status_code=500, detail=f"Data retrieval failed: {str(e)}")
