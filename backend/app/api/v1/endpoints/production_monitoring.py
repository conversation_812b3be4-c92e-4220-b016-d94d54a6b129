"""
Production Monitoring API endpoints for Quantum Market Intelligence.
Provides REST API for production monitoring control, metrics, and alerts.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from app.services.production_monitoring import production_monitoring_service

logger = logging.getLogger(__name__)

router = APIRouter()


# ============================================================================
# REQUEST/RESPONSE MODELS
# ============================================================================

class MonitoringStartRequest(BaseModel):
    """Request model for starting production monitoring."""
    symbols: Optional[List[str]] = Field(
        default=None,
        description="Symbols to monitor (default: BTC, ETH, SOL, ADA, LINK, MATIC)"
    )
    monitoring_interval_seconds: int = Field(
        default=300,
        ge=60,
        le=3600,
        description="Monitoring interval in seconds (1-60 minutes)"
    )
    alert_levels: Optional[Dict[str, float]] = Field(
        default=None,
        description="Custom alert confidence thresholds"
    )


class AlertConfigUpdate(BaseModel):
    """Request model for updating alert configuration."""
    alert_level: str = Field(..., description="Alert level: critical, high, medium")
    confidence_threshold: Optional[float] = Field(None, ge=0.0, le=1.0)
    enabled: Optional[bool] = None
    cooldown_minutes: Optional[int] = Field(None, ge=1, le=60)
    webhook_url: Optional[str] = None


# ============================================================================
# PRODUCTION MONITORING CONTROL ENDPOINTS
# ============================================================================

@router.post("/start")
async def start_production_monitoring(
    request: MonitoringStartRequest = MonitoringStartRequest()
) -> Dict[str, Any]:
    """
    Start production monitoring with real-time pattern detection.
    
    Begins continuous monitoring of specified symbols with configurable
    intervals and alert thresholds. Includes performance monitoring,
    error tracking, and comprehensive logging.
    """
    try:
        logger.info(f"Starting production monitoring with config: {request.dict()}")
        
        # Update configuration if provided
        if request.symbols:
            production_monitoring_service.monitored_symbols = request.symbols
        
        if request.monitoring_interval_seconds:
            production_monitoring_service.monitoring_interval = request.monitoring_interval_seconds
        
        if request.alert_levels:
            for level, threshold in request.alert_levels.items():
                if level in production_monitoring_service.alert_configs:
                    production_monitoring_service.alert_configs[level].confidence_threshold = threshold
        
        # Start monitoring
        result = await production_monitoring_service.start_production_monitoring()
        
        logger.info(f"Production monitoring started: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Failed to start production monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")


@router.post("/stop")
async def stop_production_monitoring() -> Dict[str, Any]:
    """
    Stop production monitoring and generate session report.
    
    Stops all monitoring tasks, generates comprehensive session statistics,
    and cleans up resources. Returns detailed performance report.
    """
    try:
        result = await production_monitoring_service.stop_production_monitoring()
        logger.info(f"Production monitoring stopped: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Failed to stop production monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop monitoring: {str(e)}")


@router.get("/status")
async def get_production_monitoring_status() -> Dict[str, Any]:
    """
    Get comprehensive production monitoring status.
    
    Returns detailed information about monitoring state, performance metrics,
    alert configuration, session statistics, and system health.
    """
    try:
        status = await production_monitoring_service.get_monitoring_status()
        return status
        
    except Exception as e:
        logger.error(f"Failed to get monitoring status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


# ============================================================================
# ALERT CONFIGURATION ENDPOINTS
# ============================================================================

@router.get("/alerts/config")
async def get_alert_configuration() -> Dict[str, Any]:
    """
    Get current alert configuration.
    
    Returns alert thresholds, enabled status, cooldown periods,
    and notification settings for all alert levels.
    """
    try:
        config = {}
        for level, alert_config in production_monitoring_service.alert_configs.items():
            config[level] = {
                "confidence_threshold": alert_config.confidence_threshold,
                "enabled": alert_config.enabled,
                "cooldown_minutes": alert_config.cooldown_minutes,
                "webhook_url": alert_config.webhook_url,
                "email_recipients": alert_config.email_recipients,
                "slack_webhook": alert_config.slack_webhook
            }
        
        return {
            "alert_levels": config,
            "active_cooldowns": len(production_monitoring_service.alert_cooldowns),
            "total_alerts_generated": production_monitoring_service.total_alerts_generated
        }
        
    except Exception as e:
        logger.error(f"Failed to get alert configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get alert config: {str(e)}")


@router.put("/alerts/config")
async def update_alert_configuration(config: AlertConfigUpdate) -> Dict[str, Any]:
    """
    Update alert configuration for specific alert level.
    
    Allows real-time adjustment of alert thresholds, enabling/disabling
    alerts, cooldown periods, and notification endpoints.
    """
    try:
        if config.alert_level not in production_monitoring_service.alert_configs:
            raise HTTPException(status_code=400, detail=f"Invalid alert level: {config.alert_level}")
        
        alert_config = production_monitoring_service.alert_configs[config.alert_level]
        
        # Update configuration
        if config.confidence_threshold is not None:
            alert_config.confidence_threshold = config.confidence_threshold
        
        if config.enabled is not None:
            alert_config.enabled = config.enabled
        
        if config.cooldown_minutes is not None:
            alert_config.cooldown_minutes = config.cooldown_minutes
        
        if config.webhook_url is not None:
            alert_config.webhook_url = config.webhook_url
        
        logger.info(f"Updated alert configuration for {config.alert_level}: {config.dict()}")
        
        return {
            "status": "updated",
            "alert_level": config.alert_level,
            "new_configuration": {
                "confidence_threshold": alert_config.confidence_threshold,
                "enabled": alert_config.enabled,
                "cooldown_minutes": alert_config.cooldown_minutes,
                "webhook_url": alert_config.webhook_url
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update alert configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update config: {str(e)}")


# ============================================================================
# METRICS AND PERFORMANCE ENDPOINTS
# ============================================================================

@router.get("/metrics")
async def get_monitoring_metrics(
    hours_back: int = Query(1, ge=1, le=24, description="Hours of metrics history to retrieve")
) -> Dict[str, Any]:
    """
    Get monitoring metrics and performance data.
    
    Returns time-series metrics including memory usage, CPU usage,
    detection latency, pattern counts, and alert statistics.
    """
    try:
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours_back)
        
        # Filter metrics by time range
        recent_metrics = [
            m for m in production_monitoring_service.metrics_history
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {
                "message": "No metrics available for the specified time range",
                "hours_back": hours_back,
                "metrics_count": 0
            }
        
        # Calculate aggregated statistics
        total_patterns = sum(m.patterns_detected for m in recent_metrics)
        total_alerts = sum(m.alerts_generated for m in recent_metrics)
        avg_memory = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
        avg_cpu = sum(m.cpu_usage_percent for m in recent_metrics) / len(recent_metrics)
        avg_analysis_time = sum(m.avg_analysis_time_ms for m in recent_metrics) / len(recent_metrics)
        
        # Get peak values
        peak_memory = max(m.memory_usage_mb for m in recent_metrics)
        peak_cpu = max(m.cpu_usage_percent for m in recent_metrics)
        max_analysis_time = max(m.avg_analysis_time_ms for m in recent_metrics)
        
        return {
            "time_range": {
                "hours_back": hours_back,
                "start_time": cutoff_time.isoformat(),
                "end_time": datetime.now(timezone.utc).isoformat(),
                "metrics_count": len(recent_metrics)
            },
            "aggregated_statistics": {
                "total_patterns_detected": total_patterns,
                "total_alerts_generated": total_alerts,
                "avg_memory_usage_mb": round(avg_memory, 2),
                "avg_cpu_usage_percent": round(avg_cpu, 2),
                "avg_analysis_time_ms": round(avg_analysis_time, 2),
                "peak_memory_usage_mb": round(peak_memory, 2),
                "peak_cpu_usage_percent": round(peak_cpu, 2),
                "max_analysis_time_ms": round(max_analysis_time, 2)
            },
            "time_series_data": [
                {
                    "timestamp": m.timestamp.isoformat(),
                    "patterns_detected": m.patterns_detected,
                    "alerts_generated": m.alerts_generated,
                    "memory_usage_mb": round(m.memory_usage_mb, 2),
                    "cpu_usage_percent": round(m.cpu_usage_percent, 2),
                    "avg_analysis_time_ms": round(m.avg_analysis_time_ms, 2),
                    "detection_rate_per_hour": round(m.detection_rate_per_hour, 2)
                }
                for m in recent_metrics[-100:]  # Last 100 data points max
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get monitoring metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.get("/performance")
async def get_performance_summary() -> Dict[str, Any]:
    """
    Get performance summary and health indicators.
    
    Returns key performance indicators, system health status,
    and recommendations for optimization.
    """
    try:
        status = await production_monitoring_service.get_monitoring_status()
        
        if status.get("status") != "active":
            return {
                "status": "inactive",
                "message": "Production monitoring not active",
                "health_score": 0
            }
        
        # Calculate health score based on various factors
        current_metrics = status["current_metrics"]
        performance_averages = status["performance_averages"]
        
        health_factors = {
            "memory_usage": 1.0 if current_metrics["memory_usage_mb"] < 500 else 0.5,
            "cpu_usage": 1.0 if current_metrics["cpu_usage_percent"] < 50 else 0.5,
            "analysis_time": 1.0 if current_metrics["avg_analysis_time_ms"] < 1000 else 0.5,
            "error_rate": 1.0 if current_metrics["error_count"] == 0 else 0.3,
            "uptime": 1.0 if current_metrics["uptime_seconds"] > 3600 else 0.7
        }
        
        health_score = sum(health_factors.values()) / len(health_factors)
        
        # Generate recommendations
        recommendations = []
        if current_metrics["memory_usage_mb"] > 500:
            recommendations.append("High memory usage detected - consider optimizing data retention")
        if current_metrics["cpu_usage_percent"] > 50:
            recommendations.append("High CPU usage - consider increasing monitoring interval")
        if current_metrics["avg_analysis_time_ms"] > 1000:
            recommendations.append("Slow analysis times - check database performance")
        if current_metrics["error_count"] > 0:
            recommendations.append("Errors detected - check logs for issues")
        
        if not recommendations:
            recommendations.append("System performing optimally")
        
        return {
            "status": "active",
            "health_score": round(health_score, 2),
            "health_status": "excellent" if health_score >= 0.9 else "good" if health_score >= 0.7 else "warning" if health_score >= 0.5 else "critical",
            "performance_indicators": {
                "uptime_hours": round(current_metrics["uptime_seconds"] / 3600, 2),
                "patterns_per_hour": round(current_metrics["detection_rate_per_hour"], 2),
                "avg_analysis_time_ms": round(performance_averages["avg_analysis_time_ms"], 2),
                "memory_efficiency": "good" if current_metrics["memory_usage_mb"] < 500 else "warning",
                "cpu_efficiency": "good" if current_metrics["cpu_usage_percent"] < 50 else "warning"
            },
            "health_factors": health_factors,
            "recommendations": recommendations,
            "monitoring_statistics": status["session_statistics"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance summary: {str(e)}")


# ============================================================================
# ALERT HISTORY ENDPOINTS
# ============================================================================

@router.get("/alerts/history")
async def get_alert_history(
    hours_back: int = Query(24, ge=1, le=168, description="Hours of alert history"),
    alert_level: Optional[str] = Query(None, description="Filter by alert level"),
    symbol: Optional[str] = Query(None, description="Filter by symbol")
) -> Dict[str, Any]:
    """
    Get alert history with optional filtering.
    
    Returns historical alerts with filtering options by time range,
    alert level, and symbol. Includes alert statistics and trends.
    """
    try:
        # This would typically query the database for alert history
        # For now, return a placeholder response
        
        return {
            "message": "Alert history endpoint - would query database for historical alerts",
            "filters": {
                "hours_back": hours_back,
                "alert_level": alert_level,
                "symbol": symbol
            },
            "placeholder": True,
            "note": "This endpoint would be implemented with database queries in production"
        }
        
    except Exception as e:
        logger.error(f"Failed to get alert history: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get alert history: {str(e)}")


# ============================================================================
# SYSTEM HEALTH ENDPOINTS
# ============================================================================

@router.get("/health")
async def get_monitoring_health() -> Dict[str, Any]:
    """
    Get monitoring system health check.
    
    Returns basic health status for monitoring components,
    database connectivity, and service availability.
    """
    try:
        status = await production_monitoring_service.get_monitoring_status()
        
        health_status = {
            "monitoring_service": "healthy" if status.get("status") == "active" else "inactive",
            "pattern_detection": "healthy",  # Would check pattern detection service
            "database": "healthy",  # Would check database connectivity
            "alert_system": "healthy",  # Would check alert system
            "overall_status": "healthy" if status.get("status") == "active" else "inactive"
        }
        
        return {
            "status": health_status["overall_status"],
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "components": health_status,
            "monitoring_active": status.get("monitoring_active", False),
            "uptime_seconds": status.get("uptime_seconds", 0) if status.get("status") == "active" else 0
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": str(e),
            "components": {
                "monitoring_service": "error",
                "pattern_detection": "unknown",
                "database": "unknown",
                "alert_system": "unknown",
                "overall_status": "error"
            }
        }
