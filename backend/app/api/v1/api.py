"""
API router for Quantum Market Intelligence Hub v1.
Implements financial-grade REST API with comprehensive validation.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import health, market_data, risk_analysis

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(market_data.router, prefix="/market", tags=["market-data"])
api_router.include_router(risk_analysis.router, prefix="/risk", tags=["risk-analysis"])
