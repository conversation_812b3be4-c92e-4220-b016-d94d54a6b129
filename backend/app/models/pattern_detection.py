"""
Pattern Detection Models for Quantum Market Intelligence.
Defines data models for pattern detection, analysis, and alerts.
"""

from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import uuid4

from pydantic import BaseModel, Field
from sqlalchemy import Column, String, DateTime, Numeric, Text, <PERSON><PERSON>an, Integer, JSO<PERSON>
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class PatternType(str, Enum):
    """Types of market patterns that can be detected."""
    CEX_LISTING = "cex_listing"
    WHALE_ACCUMULATION = "whale_accumulation"
    SOCIAL_SENTIMENT_SPIKE = "social_sentiment_spike"
    VOLUME_ANOMALY = "volume_anomaly"
    PRICE_BREAKOUT = "price_breakout"
    CORRELATION_DIVERGENCE = "correlation_divergence"


class PatternConfidence(str, Enum):
    """Confidence levels for pattern detection."""
    LOW = "low"          # 0.3 - 0.5
    MEDIUM = "medium"    # 0.5 - 0.7
    HIGH = "high"        # 0.7 - 0.85
    VERY_HIGH = "very_high"  # 0.85 - 1.0


class PatternStatus(str, Enum):
    """Status of detected patterns."""
    DETECTED = "detected"
    CONFIRMED = "confirmed"
    INVALIDATED = "invalidated"
    EXPIRED = "expired"


# ============================================================================
# DATABASE MODELS
# ============================================================================

class DetectedPattern(Base):
    """Database model for detected market patterns."""
    __tablename__ = "detected_patterns"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    pattern_type = Column(String(50), nullable=False)
    symbol = Column(String(20), nullable=False)
    confidence_score = Column(Numeric(5, 4), nullable=False)  # 0.0000 - 1.0000
    status = Column(String(20), nullable=False, default=PatternStatus.DETECTED.value)
    
    # Pattern metadata
    pattern_data = Column(JSON, nullable=False)  # Pattern-specific data
    detection_timestamp = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    expiry_timestamp = Column(DateTime(timezone=True), nullable=True)
    
    # Analysis details
    trigger_conditions = Column(JSON, nullable=False)  # Conditions that triggered detection
    supporting_indicators = Column(JSON, nullable=True)  # Additional supporting data
    risk_factors = Column(JSON, nullable=True)  # Identified risk factors
    
    # Performance tracking
    accuracy_score = Column(Numeric(5, 4), nullable=True)  # Post-analysis accuracy
    outcome_verified = Column(Boolean, default=False)
    verification_timestamp = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    detector_version = Column(String(20), nullable=False)
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)


class PatternAlert(Base):
    """Database model for pattern-based alerts."""
    __tablename__ = "pattern_alerts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    pattern_id = Column(UUID(as_uuid=True), nullable=False)  # FK to DetectedPattern
    
    alert_type = Column(String(50), nullable=False)
    priority = Column(String(20), nullable=False)  # low, medium, high, critical
    message = Column(Text, nullable=False)
    
    # Alert metadata
    alert_data = Column(JSON, nullable=True)
    sent_timestamp = Column(DateTime(timezone=True), nullable=True)
    acknowledged = Column(Boolean, default=False)
    acknowledged_timestamp = Column(DateTime(timezone=True), nullable=True)
    
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)


# ============================================================================
# PYDANTIC MODELS
# ============================================================================

class PatternDetectionRequest(BaseModel):
    """Request model for pattern detection analysis."""
    symbols: List[str] = Field(..., description="List of symbols to analyze")
    pattern_types: Optional[List[PatternType]] = Field(
        default=None, 
        description="Specific pattern types to detect (all if None)"
    )
    lookback_hours: int = Field(
        default=24, 
        ge=1, 
        le=168,  # 1 week max
        description="Hours of historical data to analyze"
    )
    min_confidence: float = Field(
        default=0.7, 
        ge=0.0, 
        le=1.0,
        description="Minimum confidence threshold"
    )
    real_time: bool = Field(
        default=False,
        description="Enable real-time pattern monitoring"
    )


class PatternDetectionResult(BaseModel):
    """Result model for pattern detection."""
    pattern_id: str
    pattern_type: PatternType
    symbol: str
    confidence_score: float
    status: PatternStatus
    
    # Pattern details
    detection_timestamp: datetime
    expiry_timestamp: Optional[datetime]
    pattern_data: Dict[str, Any]
    
    # Analysis
    trigger_conditions: Dict[str, Any]
    supporting_indicators: Optional[Dict[str, Any]]
    risk_factors: Optional[Dict[str, Any]]
    
    # Metadata
    detector_version: str


class PatternAnalysisResponse(BaseModel):
    """Response model for pattern analysis."""
    request_id: str
    analysis_timestamp: datetime
    symbols_analyzed: List[str]
    patterns_detected: List[PatternDetectionResult]
    
    # Summary statistics
    total_patterns: int
    high_confidence_patterns: int
    pattern_distribution: Dict[PatternType, int]
    
    # Performance metrics
    analysis_duration_ms: float
    data_points_analyzed: int


class PatternMonitoringStatus(BaseModel):
    """Status model for pattern monitoring service."""
    service_active: bool
    monitored_symbols: List[str]
    active_patterns: int
    detection_rate_per_hour: float
    last_detection_timestamp: Optional[datetime]
    
    # Performance metrics
    avg_detection_latency_ms: float
    accuracy_rate: float
    false_positive_rate: float


class PatternAlertRequest(BaseModel):
    """Request model for creating pattern alerts."""
    pattern_id: str
    alert_type: str
    priority: str = Field(..., pattern="^(low|medium|high|critical)$")
    message: str
    alert_data: Optional[Dict[str, Any]] = None


class PatternAlertResponse(BaseModel):
    """Response model for pattern alerts."""
    alert_id: str
    pattern_id: str
    alert_type: str
    priority: str
    message: str
    created_at: datetime
    sent_timestamp: Optional[datetime]
    acknowledged: bool


# ============================================================================
# PATTERN-SPECIFIC DATA MODELS
# ============================================================================

class CEXListingPatternData(BaseModel):
    """Data model for CEX listing pattern detection."""
    volume_spike_ratio: float  # Current volume / average volume
    price_movement_24h: float  # 24h price change percentage
    social_mentions_spike: float  # Social mentions increase ratio
    exchange_announcement_detected: bool
    listing_probability: float  # 0.0 - 1.0


class WhaleAccumulationPatternData(BaseModel):
    """Data model for whale accumulation pattern detection."""
    large_transaction_count: int  # Number of large transactions
    accumulation_volume: float  # Total accumulation volume
    price_stability_score: float  # Price stability during accumulation
    wallet_concentration_change: float  # Change in wallet concentration
    accumulation_duration_hours: int


class SocialSentimentPatternData(BaseModel):
    """Data model for social sentiment pattern detection."""
    sentiment_score: float  # -1.0 (negative) to 1.0 (positive)
    mention_volume_change: float  # Change in mention volume
    sentiment_velocity: float  # Rate of sentiment change
    key_influencer_mentions: int  # Mentions by key influencers
    correlation_with_price: float  # Correlation coefficient


# ============================================================================
# CONFIGURATION MODELS
# ============================================================================

class PatternDetectorConfig(BaseModel):
    """Configuration for pattern detectors."""
    enabled: bool = True
    confidence_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    detection_interval_seconds: int = Field(default=300, ge=60, le=3600)  # 1-60 minutes
    lookback_hours: int = Field(default=24, ge=1, le=168)
    max_patterns_per_symbol: int = Field(default=10, ge=1, le=100)
    
    # Pattern-specific configurations
    pattern_configs: Dict[PatternType, Dict[str, Any]] = Field(default_factory=dict)


class PatternDetectionSystemConfig(BaseModel):
    """System-wide configuration for pattern detection."""
    enabled: bool = True
    max_concurrent_analyses: int = Field(default=5, ge=1, le=20)
    pattern_retention_days: int = Field(default=30, ge=1, le=365)
    alert_retention_days: int = Field(default=7, ge=1, le=30)
    
    # Detector configurations
    detectors: Dict[PatternType, PatternDetectorConfig] = Field(default_factory=dict)
    
    # Performance settings
    batch_size: int = Field(default=100, ge=10, le=1000)
    max_memory_usage_mb: int = Field(default=512, ge=128, le=2048)
