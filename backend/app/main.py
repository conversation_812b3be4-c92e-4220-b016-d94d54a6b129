"""
Main FastAPI application for Quantum Market Intelligence Hub.
Implements financial-grade real-time analytics with comprehensive monitoring.
"""

import time
from contextlib import asynccontextmanager
from typing import Any, Dict

import structlog
import uvicorn
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from prometheus_client import Counter, Histogram, generate_latest
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from slowapi.util import get_remote_address

from app.api.v1.api import api_router
from app.core.config import settings
from app.core.logging import setup_logging
from app.core.security import crypto_validator, QuantumValidationError
from app.db.session import engine

# Setup structured logging
setup_logging()
logger = structlog.get_logger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter(
    'quantum_hub_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'quantum_hub_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

VALIDATION_ERRORS = Counter(
    'quantum_hub_validation_errors_total',
    'Total number of validation errors',
    ['error_type']
)

# Rate limiting
limiter = Limiter(key_func=get_remote_address)


def setup_opentelemetry() -> None:
    """Configure OpenTelemetry for distributed tracing."""
    if settings.ENVIRONMENT == "production":
        resource = Resource.create({
            "service.name": settings.OTEL_SERVICE_NAME,
            "service.version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
        })
        
        trace.set_tracer_provider(TracerProvider(resource=resource))
        
        otlp_exporter = OTLPSpanExporter(
            endpoint=settings.OTEL_EXPORTER_OTLP_ENDPOINT,
            insecure=True
        )
        
        span_processor = BatchSpanProcessor(otlp_exporter)
        trace.get_tracer_provider().add_span_processor(span_processor)
        
        logger.info("OpenTelemetry configured", endpoint=settings.OTEL_EXPORTER_OTLP_ENDPOINT)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting Quantum Market Intelligence Hub", version=settings.VERSION)
    
    # Initialize OpenTelemetry
    setup_opentelemetry()
    
    # Validate cryptographic framework
    try:
        test_data = {"test": "validation", "timestamp": time.time()}
        security_metadata = crypto_validator.create_security_metadata(test_data)
        is_valid = crypto_validator.validate_security_metadata(test_data, security_metadata)
        
        if not is_valid:
            raise QuantumValidationError("Cryptographic validation framework failed startup test")
        
        logger.info("Cryptographic validation framework initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize cryptographic validation", error=str(e))
        raise
    
    # Test database connection
    try:
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        logger.info("Database connection validated")
    except Exception as e:
        logger.error("Database connection failed", error=str(e))
        raise
    
    logger.info("Application startup completed successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Quantum Market Intelligence Hub")
    await engine.dispose()
    logger.info("Application shutdown completed")


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json" if settings.DEBUG else None,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
)

# Add middleware
app.add_middleware(SlowAPIMiddleware)

# CORS middleware
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Trusted host middleware for security
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)


@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    """Add security headers to all responses."""
    response = await call_next(request)
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    
    return response


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time and request tracking."""
    start_time = time.time()
    
    # Generate request ID for tracing
    request_id = crypto_validator.generate_data_hash(f"{request.url}:{start_time}")[:16]
    
    with structlog.contextvars.bound_contextvars(request_id=request_id):
        logger.info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None
        )
        
        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # Add headers
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = request_id
            
            # Update metrics
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status_code=response.status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=request.method,
                endpoint=request.url.path
            ).observe(process_time)
            
            logger.info(
                "Request completed",
                method=request.method,
                url=str(request.url),
                status_code=response.status_code,
                process_time=process_time
            )
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            
            logger.error(
                "Request failed",
                method=request.method,
                url=str(request.url),
                error=str(e),
                process_time=process_time
            )
            
            VALIDATION_ERRORS.labels(error_type=type(e).__name__).inc()
            raise


# Rate limiting error handler
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint for load balancers."""
    return {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "timestamp": time.time()
    }


@app.get("/metrics")
async def metrics() -> Response:
    """Prometheus metrics endpoint."""
    return Response(
        content=generate_latest(),
        media_type="text/plain"
    )


@app.get("/")
@limiter.limit("10/minute")
async def root(request: Request) -> Dict[str, Any]:
    """Root endpoint with API information."""
    return {
        "message": "Quantum Market Intelligence Hub API",
        "version": settings.VERSION,
        "docs_url": "/docs" if settings.DEBUG else None,
        "health_url": "/health",
        "metrics_url": "/metrics",
        "api_v1": settings.API_V1_STR,
    }


# Instrument FastAPI with OpenTelemetry
if settings.ENVIRONMENT == "production":
    FastAPIInstrumentor.instrument_app(app)
    SQLAlchemyInstrumentor().instrument(engine=engine.sync_engine)
    RedisInstrumentor().instrument()


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        server_header=False,
        date_header=False,
    )
