"""
Minimal FastAPI application with database connectivity
"""
import os
from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from dotenv import load_dotenv

from app.db.deps import get_db
from app.db.session import engine

# Load environment variables
load_dotenv()

app = FastAPI(
    title="Quantum Market Intelligence Hub",
    description="Minimal version with database connectivity",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Quantum Market Intelligence Hub - Minimal Version"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": "0.1.0"}

@app.get("/api/v1/status")
async def api_status():
    """API status endpoint"""
    return {
        "status": "running",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "version": "0.1.0"
    }

@app.get("/api/v1/db-status")
async def database_status(db: AsyncSession = Depends(get_db)):
    """Database connectivity test endpoint"""
    try:
        # Test database connection
        result = await db.execute("SELECT 1 as test")
        test_value = result.scalar()

        # Test TimescaleDB extension
        result = await db.execute("SELECT extname FROM pg_extension WHERE extname = 'timescaledb'")
        timescaledb_installed = result.scalar() is not None

        return {
            "database_connected": test_value == 1,
            "timescaledb_enabled": timescaledb_installed,
            "connection_info": {
                "host": os.getenv("POSTGRES_SERVER", "localhost"),
                "port": os.getenv("POSTGRES_PORT", "5433"),
                "database": os.getenv("POSTGRES_DB", "quantum_market_db")
            }
        }
    except Exception as e:
        return {
            "database_connected": False,
            "error": str(e),
            "connection_info": {
                "host": os.getenv("POSTGRES_SERVER", "localhost"),
                "port": os.getenv("POSTGRES_PORT", "5433"),
                "database": os.getenv("POSTGRES_DB", "quantum_market_db")
            }
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
