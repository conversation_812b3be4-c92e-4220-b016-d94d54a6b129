"""
Database session management for Quantum Market Intelligence Hub.
Implements async SQLAlchemy with TimescaleDB optimizations.
"""

from typing import AsyncGenerator

import structlog
from sqlalchemy import event, pool
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import declarative_base

from app.core.config import settings

logger = structlog.get_logger(__name__)

# Create async engine with optimized settings for financial data
engine = create_async_engine(
    settings.get_database_url(),
    echo=settings.DEBUG,
    echo_pool=settings.DEBUG,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_POOL_SIZE * 2,
    pool_timeout=30,
    pool_recycle=3600,  # Recycle connections every hour
    pool_pre_ping=True,  # Validate connections before use
    poolclass=pool.QueuePool,
    connect_args={
        "server_settings": {
            "application_name": "quantum_market_intelligence",
            "timezone": "UTC",
        },
        "command_timeout": 60,
    },
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False,
)

# Base class for SQLAlchemy models
Base = declarative_base()


@event.listens_for(engine.sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set database-specific optimizations on connection."""
    if hasattr(dbapi_connection, "execute"):
        # PostgreSQL/TimescaleDB specific optimizations
        cursor = dbapi_connection.cursor()
        
        # Set timezone to UTC
        cursor.execute("SET timezone = 'UTC'")
        
        # Optimize for time-series workloads
        cursor.execute("SET work_mem = '32MB'")
        cursor.execute("SET maintenance_work_mem = '128MB'")
        cursor.execute("SET effective_cache_size = '1GB'")
        
        # Enable parallel queries for analytics
        cursor.execute("SET max_parallel_workers_per_gather = 2")
        
        # Optimize for financial data precision
        cursor.execute("SET extra_float_digits = 3")
        
        cursor.close()
        
        logger.debug("Database connection optimized for financial workloads")


@event.listens_for(engine.sync_engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log database connection checkout for monitoring."""
    logger.debug("Database connection checked out")


@event.listens_for(engine.sync_engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Log database connection checkin for monitoring."""
    logger.debug("Database connection checked in")


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    
    Yields:
        AsyncSession: Database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error("Database session error", error=str(e))
            raise
        finally:
            await session.close()


class DatabaseManager:
    """Database management utilities for financial data operations."""
    
    @staticmethod
    async def execute_raw_sql(query: str, params: dict = None) -> list:
        """
        Execute raw SQL query with parameters.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Query results as list of dictionaries
        """
        async with AsyncSessionLocal() as session:
            try:
                result = await session.execute(query, params or {})
                return [dict(row) for row in result.fetchall()]
            except Exception as e:
                await session.rollback()
                logger.error("Raw SQL execution failed", query=query, error=str(e))
                raise
    
    @staticmethod
    async def create_hypertable(table_name: str, time_column: str, chunk_interval: str = "1 hour"):
        """
        Create TimescaleDB hypertable for time-series data.
        
        Args:
            table_name: Name of the table to convert
            time_column: Name of the time column
            chunk_interval: Chunk interval for partitioning
        """
        query = f"""
        SELECT create_hypertable('{table_name}', '{time_column}', 
                                chunk_time_interval => INTERVAL '{chunk_interval}',
                                if_not_exists => TRUE);
        """
        
        try:
            await DatabaseManager.execute_raw_sql(query)
            logger.info("Hypertable created", table=table_name, time_column=time_column)
        except Exception as e:
            logger.error("Failed to create hypertable", table=table_name, error=str(e))
            raise
    
    @staticmethod
    async def add_retention_policy(table_name: str, retention_period: str):
        """
        Add data retention policy to hypertable.
        
        Args:
            table_name: Name of the hypertable
            retention_period: Retention period (e.g., '1 year')
        """
        query = f"""
        SELECT add_retention_policy('{table_name}', INTERVAL '{retention_period}');
        """
        
        try:
            await DatabaseManager.execute_raw_sql(query)
            logger.info("Retention policy added", table=table_name, period=retention_period)
        except Exception as e:
            logger.error("Failed to add retention policy", table=table_name, error=str(e))
            raise
    
    @staticmethod
    async def add_compression_policy(table_name: str, compress_after: str = "7 days"):
        """
        Add compression policy to hypertable for storage optimization.
        
        Args:
            table_name: Name of the hypertable
            compress_after: Time after which to compress data
        """
        query = f"""
        SELECT add_compression_policy('{table_name}', INTERVAL '{compress_after}');
        """
        
        try:
            await DatabaseManager.execute_raw_sql(query)
            logger.info("Compression policy added", table=table_name, compress_after=compress_after)
        except Exception as e:
            logger.error("Failed to add compression policy", table=table_name, error=str(e))
            raise
    
    @staticmethod
    async def create_continuous_aggregate(
        view_name: str,
        query: str,
        refresh_policy_start: str = "1 hour",
        refresh_policy_end: str = "1 minute"
    ):
        """
        Create continuous aggregate for real-time analytics.
        
        Args:
            view_name: Name of the materialized view
            query: SQL query for the aggregate
            refresh_policy_start: Start offset for refresh policy
            refresh_policy_end: End offset for refresh policy
        """
        create_query = f"""
        CREATE MATERIALIZED VIEW {view_name}
        WITH (timescaledb.continuous) AS
        {query};
        """
        
        policy_query = f"""
        SELECT add_continuous_aggregate_policy('{view_name}',
            start_offset => INTERVAL '{refresh_policy_start}',
            end_offset => INTERVAL '{refresh_policy_end}',
            schedule_interval => INTERVAL '1 hour');
        """
        
        try:
            await DatabaseManager.execute_raw_sql(create_query)
            await DatabaseManager.execute_raw_sql(policy_query)
            logger.info("Continuous aggregate created", view=view_name)
        except Exception as e:
            logger.error("Failed to create continuous aggregate", view=view_name, error=str(e))
            raise
    
    @staticmethod
    async def get_hypertable_stats(table_name: str) -> dict:
        """
        Get statistics for a hypertable.
        
        Args:
            table_name: Name of the hypertable
            
        Returns:
            Dictionary with hypertable statistics
        """
        query = f"""
        SELECT 
            hypertable_name,
            num_chunks,
            table_bytes,
            index_bytes,
            toast_bytes,
            total_bytes
        FROM timescaledb_information.hypertable
        WHERE hypertable_name = '{table_name}';
        """
        
        try:
            result = await DatabaseManager.execute_raw_sql(query)
            return result[0] if result else {}
        except Exception as e:
            logger.error("Failed to get hypertable stats", table=table_name, error=str(e))
            return {}
    
    @staticmethod
    async def validate_data_integrity(table_name: str, hash_column: str = "data_hash") -> bool:
        """
        Validate data integrity using hash verification.
        
        Args:
            table_name: Name of the table to validate
            hash_column: Name of the hash column
            
        Returns:
            True if all data integrity checks pass
        """
        query = f"""
        SELECT COUNT(*) as total_records,
               COUNT(CASE WHEN {hash_column} IS NULL THEN 1 END) as missing_hashes,
               COUNT(CASE WHEN LENGTH({hash_column}) != 64 THEN 1 END) as invalid_hashes
        FROM {table_name}
        WHERE created_at >= NOW() - INTERVAL '1 hour';
        """
        
        try:
            result = await DatabaseManager.execute_raw_sql(query)
            stats = result[0]
            
            is_valid = (
                stats['missing_hashes'] == 0 and 
                stats['invalid_hashes'] == 0 and
                stats['total_records'] > 0
            )
            
            logger.info(
                "Data integrity validation completed",
                table=table_name,
                total_records=stats['total_records'],
                missing_hashes=stats['missing_hashes'],
                invalid_hashes=stats['invalid_hashes'],
                is_valid=is_valid
            )
            
            return is_valid
            
        except Exception as e:
            logger.error("Data integrity validation failed", table=table_name, error=str(e))
            return False


# Global database manager instance
db_manager = DatabaseManager()
