"""
Database models for Quantum Market Intelligence Hub.
SQLAlchemy models for TimescaleDB with financial-grade data integrity.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Integer, String, Text, DECIMAL, 
    CheckConstraint, Index, ForeignKey, JSON
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.session import Base


class PriceEvent(Base):
    """Market price events with cryptographic integrity."""
    __tablename__ = "price_events"
    __table_args__ = (
        {"schema": "market_data"},
        CheckConstraint("price > 0", name="price_positive"),
        CheckConstraint("volume >= 0", name="volume_positive"),
        Index("idx_price_events_symbol_time", "symbol", "timestamp"),
        Index("idx_price_events_exchange_time", "exchange", "timestamp"),
        Index("idx_price_events_hash", "data_hash"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    symbol = Column(String(20), nullable=False)
    exchange = Column(String(50), nullable=False)
    price = Column(DECIMAL(20, 8), nullable=False)
    volume = Column(DECIMAL(20, 8), nullable=False)
    market_cap = Column(DECIMAL(20, 2), nullable=True)
    source = Column(String(50), nullable=False)
    data_hash = Column(String(64), nullable=False)  # SHA-256 hash
    signature = Column(String(128), nullable=True)  # Cryptographic signature
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class SocialSentiment(Base):
    """Social media sentiment analysis data."""
    __tablename__ = "social_sentiment"
    __table_args__ = (
        {"schema": "market_data"},
        CheckConstraint("sentiment_score >= -1.0 AND sentiment_score <= 1.0", name="sentiment_range"),
        Index("idx_social_sentiment_symbol_time", "symbol", "timestamp"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    symbol = Column(String(20), nullable=False)
    platform = Column(String(50), nullable=False)  # reddit, twitter, etc.
    sentiment_score = Column(DECIMAL(5, 4), nullable=False)  # -1.0 to 1.0
    mention_count = Column(Integer, nullable=False)
    engagement_score = Column(DECIMAL(10, 4), nullable=True)
    keywords = Column(JSONB, nullable=True)
    data_hash = Column(String(64), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class OnchainMetrics(Base):
    """On-chain metrics from thirdweb Insight."""
    __tablename__ = "onchain_metrics"
    __table_args__ = (
        {"schema": "market_data"},
        Index("idx_onchain_metrics_symbol_time", "symbol", "timestamp"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    symbol = Column(String(20), nullable=False)
    chain = Column(String(50), nullable=False)
    whale_movements = Column(DECIMAL(20, 8), nullable=True)
    large_transactions = Column(Integer, nullable=True)
    active_addresses = Column(Integer, nullable=True)
    transaction_volume = Column(DECIMAL(20, 8), nullable=True)
    gas_usage = Column(Integer, nullable=True)
    data_hash = Column(String(64), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class MutualInformationScore(Base):
    """Mutual information scores between symbol pairs."""
    __tablename__ = "mutual_information_scores"
    __table_args__ = (
        {"schema": "risk_analysis"},
        CheckConstraint("mi_score >= 0", name="mi_score_positive"),
        Index("idx_mi_scores_symbol_pair_time", "symbol_pair", "timestamp"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    symbol_pair = Column(String(50), nullable=False)  # e.g., "BTC-ETH"
    mi_score = Column(DECIMAL(10, 8), nullable=False)
    threshold_exceeded = Column(Boolean, nullable=False)
    window_size = Column(Integer, nullable=False)  # in minutes
    calculation_method = Column(String(50), nullable=False)
    data_hash = Column(String(64), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class QuantumPrediction(Base):
    """Quantum tunneling predictions and volatility forecasts."""
    __tablename__ = "quantum_predictions"
    __table_args__ = (
        {"schema": "risk_analysis"},
        CheckConstraint("volatility_prediction >= 0", name="volatility_positive"),
        CheckConstraint("quantum_tunneling_prob >= 0.0 AND quantum_tunneling_prob <= 1.0", name="quantum_prob_range"),
        CheckConstraint("confidence_score >= 0.0 AND confidence_score <= 1.0", name="confidence_range"),
        Index("idx_quantum_predictions_symbol_time", "symbol", "timestamp"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    symbol = Column(String(20), nullable=False)
    prediction_horizon = Column(Integer, nullable=False)  # in days
    volatility_prediction = Column(DECIMAL(10, 8), nullable=False)
    quantum_tunneling_prob = Column(DECIMAL(5, 4), nullable=False)
    confidence_score = Column(DECIMAL(5, 4), nullable=False)
    model_version = Column(String(20), nullable=False)
    data_hash = Column(String(64), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class DataIntegrityCheck(Base):
    """Audit logs for data integrity validation."""
    __tablename__ = "data_integrity_checks"
    __table_args__ = (
        {"schema": "audit_logs"},
        Index("idx_integrity_checks_table_time", "table_name", "timestamp"),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    table_name = Column(String(100), nullable=False)
    check_type = Column(String(50), nullable=False)  # hash_validation, signature_verification, etc.
    status = Column(String(20), nullable=False)  # PASS, FAIL, WARNING
    details = Column(JSONB, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class ModelParameters(Base):
    """Quantum model configuration and parameters."""
    __tablename__ = "model_parameters"
    __table_args__ = (
        {"schema": "quantum_models"},
        Index("idx_model_params_name_version", "model_name", "version", unique=True),
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    model_name = Column(String(100), nullable=False)
    version = Column(String(20), nullable=False)
    parameters = Column(JSONB, nullable=False)
    performance_metrics = Column(JSONB, nullable=True)
    is_active = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
