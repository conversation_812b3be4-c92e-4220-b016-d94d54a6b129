"""
Database dependencies for FastAPI dependency injection.
"""

from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>

import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import AsyncSessionLocal

logger = structlog.get_logger(__name__)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    
    Yields:
        AsyncSession: Database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error("Database session error", error=str(e))
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db_session() -> AsyncSession:
    """
    Get a database session for use outside of FastAPI dependency injection.
    
    Returns:
        AsyncSession: Database session
    """
    return AsyncSessionLocal()
