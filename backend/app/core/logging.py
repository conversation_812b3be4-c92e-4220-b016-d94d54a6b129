"""
Structured logging configuration for Quantum Market Intelligence Hub.
Implements financial-grade audit logging with OpenTelemetry integration.
"""

import logging
import sys
from typing import Any, Dict

import structlog
from opentelemetry import trace
from pythonjsonlogger import jsonlogger

from app.core.config import settings


def add_trace_info(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add OpenTelemetry trace information to log records."""
    span = trace.get_current_span()
    if span:
        span_context = span.get_span_context()
        if span_context.is_valid:
            event_dict["trace_id"] = format(span_context.trace_id, "032x")
            event_dict["span_id"] = format(span_context.span_id, "016x")
    return event_dict


def add_service_info(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add service information to log records."""
    event_dict["service"] = settings.PROJECT_NAME
    event_dict["version"] = settings.VERSION
    event_dict["environment"] = settings.ENVIRONMENT
    return event_dict


def setup_logging() -> None:
    """Configure structured logging for the application."""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL),
    )
    
    # Configure structlog
    processors = [
        structlog.contextvars.merge_contextvars,
        add_service_info,
        add_trace_info,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if settings.ENVIRONMENT == "production":
        # JSON logging for production
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Pretty console logging for development
        processors.append(structlog.dev.ConsoleRenderer(colors=True))
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure specific loggers
    configure_third_party_loggers()


def configure_third_party_loggers() -> None:
    """Configure logging levels for third-party libraries."""
    
    # Reduce noise from third-party libraries
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("confluent_kafka").setLevel(logging.WARNING)
    
    # Financial data APIs - keep INFO for audit trail
    logging.getLogger("ccxt").setLevel(logging.INFO)
    logging.getLogger("yfinance").setLevel(logging.INFO)
    
    # Database logging
    if settings.DEBUG:
        logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)
    else:
        logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


class AuditLogger:
    """Specialized logger for financial audit trails."""
    
    def __init__(self):
        self.logger = structlog.get_logger("audit")
    
    def log_data_ingestion(
        self,
        source: str,
        symbol: str,
        record_count: int,
        data_hash: str,
        **kwargs
    ) -> None:
        """Log data ingestion events for audit compliance."""
        self.logger.info(
            "Data ingestion completed",
            event_type="data_ingestion",
            source=source,
            symbol=symbol,
            record_count=record_count,
            data_hash=data_hash,
            **kwargs
        )
    
    def log_risk_calculation(
        self,
        symbol: str,
        model_type: str,
        risk_score: float,
        confidence: float,
        **kwargs
    ) -> None:
        """Log risk calculation events."""
        self.logger.info(
            "Risk calculation completed",
            event_type="risk_calculation",
            symbol=symbol,
            model_type=model_type,
            risk_score=risk_score,
            confidence=confidence,
            **kwargs
        )
    
    def log_api_access(
        self,
        user_id: str,
        endpoint: str,
        method: str,
        status_code: int,
        **kwargs
    ) -> None:
        """Log API access for security auditing."""
        self.logger.info(
            "API access",
            event_type="api_access",
            user_id=user_id,
            endpoint=endpoint,
            method=method,
            status_code=status_code,
            **kwargs
        )
    
    def log_security_event(
        self,
        event_type: str,
        severity: str,
        description: str,
        **kwargs
    ) -> None:
        """Log security-related events."""
        log_method = getattr(self.logger, severity.lower(), self.logger.warning)
        log_method(
            description,
            event_type="security_event",
            security_event_type=event_type,
            severity=severity,
            **kwargs
        )
    
    def log_model_performance(
        self,
        model_name: str,
        accuracy: float,
        precision: float,
        recall: float,
        **kwargs
    ) -> None:
        """Log model performance metrics."""
        self.logger.info(
            "Model performance evaluation",
            event_type="model_performance",
            model_name=model_name,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            **kwargs
        )
    
    def log_data_validation_failure(
        self,
        data_source: str,
        validation_type: str,
        error_details: str,
        **kwargs
    ) -> None:
        """Log data validation failures for compliance."""
        self.logger.error(
            "Data validation failed",
            event_type="validation_failure",
            data_source=data_source,
            validation_type=validation_type,
            error_details=error_details,
            **kwargs
        )


class PerformanceLogger:
    """Logger for performance monitoring and optimization."""
    
    def __init__(self):
        self.logger = structlog.get_logger("performance")
    
    def log_query_performance(
        self,
        query_type: str,
        execution_time: float,
        record_count: int,
        **kwargs
    ) -> None:
        """Log database query performance."""
        self.logger.info(
            "Query executed",
            event_type="query_performance",
            query_type=query_type,
            execution_time_ms=execution_time * 1000,
            record_count=record_count,
            **kwargs
        )
    
    def log_kafka_performance(
        self,
        topic: str,
        operation: str,
        latency_ms: float,
        throughput: float,
        **kwargs
    ) -> None:
        """Log Kafka performance metrics."""
        self.logger.info(
            "Kafka operation",
            event_type="kafka_performance",
            topic=topic,
            operation=operation,
            latency_ms=latency_ms,
            throughput_msgs_per_sec=throughput,
            **kwargs
        )
    
    def log_model_computation_time(
        self,
        model_name: str,
        computation_time: float,
        data_points: int,
        **kwargs
    ) -> None:
        """Log model computation performance."""
        self.logger.info(
            "Model computation completed",
            event_type="model_computation",
            model_name=model_name,
            computation_time_ms=computation_time * 1000,
            data_points=data_points,
            throughput_points_per_sec=data_points / computation_time if computation_time > 0 else 0,
            **kwargs
        )


# Global logger instances
audit_logger = AuditLogger()
performance_logger = PerformanceLogger()
