"""
Cryptographic validation framework for Quantum Market Intelligence Hub.
Implements Sigstore binary provenance and zero-trust validation.
"""

import hashlib
import hmac
import json
import time
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

import structlog
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.hazmat.primitives.kdf.pbkdf2 import P<PERSON><PERSON>DF2HMAC
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

from app.core.config import settings

logger = structlog.get_logger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30


class SecurityData(BaseModel):
    """Security metadata for data integrity validation."""
    
    data_hash: str
    signature: Optional[str] = None
    public_key_id: Optional[str] = None
    timestamp: datetime
    algorithm: str = "SHA-256"


class CryptographicValidator:
    """
    Cryptographic validation framework implementing zero-trust principles.
    Provides data integrity, authenticity, and provenance validation.
    """
    
    def __init__(self):
        self.private_key = self._load_or_generate_private_key()
        self.public_key = self.private_key.public_key()
        self.fernet = self._initialize_fernet()
        
    def _load_or_generate_private_key(self) -> rsa.RSAPrivateKey:
        """Load existing private key or generate new one for signing."""
        try:
            # In production, load from secure key management system
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )
            logger.info("Generated new RSA private key for cryptographic validation")
            return private_key
        except Exception as e:
            logger.error("Failed to initialize private key", error=str(e))
            raise
    
    def _initialize_fernet(self) -> Fernet:
        """Initialize Fernet encryption for sensitive data."""
        key = settings.ENCRYPTION_KEY.encode()
        if len(key) != 32:
            # Derive key from settings if not exactly 32 bytes
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'quantum_market_salt',
                iterations=100000,
            )
            key = kdf.derive(key)
        return Fernet(key)
    
    def generate_data_hash(self, data: Union[str, bytes, Dict[str, Any]]) -> str:
        """
        Generate SHA-256 hash of data for integrity verification.
        
        Args:
            data: Data to hash (string, bytes, or dictionary)
            
        Returns:
            Hexadecimal hash string
        """
        if isinstance(data, dict):
            # Sort keys for consistent hashing
            data_str = json.dumps(data, sort_keys=True, separators=(',', ':'))
            data_bytes = data_str.encode('utf-8')
        elif isinstance(data, str):
            data_bytes = data.encode('utf-8')
        else:
            data_bytes = data
            
        hash_obj = hashlib.sha256(data_bytes)
        return hash_obj.hexdigest()
    
    def sign_data(self, data: Union[str, bytes]) -> str:
        """
        Create cryptographic signature for data authenticity.
        
        Args:
            data: Data to sign
            
        Returns:
            Base64-encoded signature
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
            
        try:
            signature = self.private_key.sign(
                data,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return signature.hex()
        except Exception as e:
            logger.error("Failed to sign data", error=str(e))
            raise
    
    def verify_signature(self, data: Union[str, bytes], signature: str) -> bool:
        """
        Verify cryptographic signature for data authenticity.
        
        Args:
            data: Original data
            signature: Signature to verify (hex-encoded)
            
        Returns:
            True if signature is valid, False otherwise
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
            
        try:
            signature_bytes = bytes.fromhex(signature)
            self.public_key.verify(
                signature_bytes,
                data,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception as e:
            logger.warning("Signature verification failed", error=str(e))
            return False
    
    def validate_data_integrity(
        self, 
        data: Union[str, bytes, Dict[str, Any]], 
        expected_hash: str
    ) -> bool:
        """
        Validate data integrity using hash comparison.
        
        Args:
            data: Data to validate
            expected_hash: Expected hash value
            
        Returns:
            True if data integrity is valid, False otherwise
        """
        actual_hash = self.generate_data_hash(data)
        is_valid = hmac.compare_digest(actual_hash, expected_hash)
        
        if not is_valid:
            logger.warning(
                "Data integrity validation failed",
                expected_hash=expected_hash,
                actual_hash=actual_hash
            )
        
        return is_valid
    
    def create_security_metadata(
        self, 
        data: Union[str, bytes, Dict[str, Any]]
    ) -> SecurityData:
        """
        Create comprehensive security metadata for data.
        
        Args:
            data: Data to create metadata for
            
        Returns:
            SecurityData object with hash, signature, and metadata
        """
        data_hash = self.generate_data_hash(data)
        
        # Create signature payload
        signature_payload = f"{data_hash}:{int(time.time())}"
        signature = self.sign_data(signature_payload)
        
        return SecurityData(
            data_hash=data_hash,
            signature=signature,
            public_key_id="quantum_hub_v1",
            timestamp=datetime.utcnow(),
            algorithm="SHA-256"
        )
    
    def validate_security_metadata(
        self, 
        data: Union[str, bytes, Dict[str, Any]], 
        security_data: SecurityData
    ) -> bool:
        """
        Validate complete security metadata for data.
        
        Args:
            data: Original data
            security_data: Security metadata to validate
            
        Returns:
            True if all validations pass, False otherwise
        """
        # Validate data integrity
        if not self.validate_data_integrity(data, security_data.data_hash):
            return False
        
        # Validate signature if present
        if security_data.signature:
            signature_payload = f"{security_data.data_hash}:{int(security_data.timestamp.timestamp())}"
            if not self.verify_signature(signature_payload, security_data.signature):
                return False
        
        # Validate timestamp (not too old)
        age = datetime.utcnow() - security_data.timestamp
        if age > timedelta(hours=24):
            logger.warning("Security metadata too old", age_hours=age.total_seconds() / 3600)
            return False
        
        return True
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data using Fernet."""
        return self.fernet.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data using Fernet."""
        return self.fernet.decrypt(encrypted_data.encode()).decode()


class JWTManager:
    """JWT token management for API authentication."""
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except JWTError as e:
            logger.warning("JWT verification failed", error=str(e))
            return None


class PasswordManager:
    """Password hashing and verification."""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate password hash."""
        return pwd_context.hash(password)


# Global instances
crypto_validator = CryptographicValidator()
jwt_manager = JWTManager()
password_manager = PasswordManager()


def validate_kafka_receipt(receipt_data: Dict[str, Any]) -> bool:
    """
    Validate Kafka message receipt for zero-trust verification.
    
    Args:
        receipt_data: Kafka receipt metadata
        
    Returns:
        True if receipt is valid, False otherwise
    """
    try:
        # Validate required fields
        required_fields = ['topic', 'partition', 'offset', 'timestamp']
        if not all(field in receipt_data for field in required_fields):
            logger.error("Missing required fields in Kafka receipt", receipt=receipt_data)
            return False
        
        # Validate timestamp is recent (within last hour)
        receipt_time = datetime.fromtimestamp(receipt_data['timestamp'] / 1000)
        age = datetime.utcnow() - receipt_time
        if age > timedelta(hours=1):
            logger.warning("Kafka receipt too old", age_minutes=age.total_seconds() / 60)
            return False
        
        # Additional validation logic can be added here
        logger.info("Kafka receipt validation passed", receipt=receipt_data)
        return True
        
    except Exception as e:
        logger.error("Kafka receipt validation failed", error=str(e))
        return False


class QuantumValidationError(Exception):
    """Custom exception for quantum validation failures."""
    pass
