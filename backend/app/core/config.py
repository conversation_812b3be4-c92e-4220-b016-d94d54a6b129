"""
Configuration management for Quantum Market Intelligence Hub.
Implements secure configuration with environment variable validation.
"""

import os
from typing import Any, Dict, List, Optional, Union

from pydantic import Field, validator
from pydantic_settings import BaseSettings
from pydantic.networks import AnyHttpUrl, PostgresDsn, RedisDsn


class Settings(BaseSettings):
    """Application settings with validation and security."""
    
    # Application
    PROJECT_NAME: str = "Quantum Market Intelligence Hub"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Financial-grade real-time analytics with quantum finance models"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = Field(default="dev_secret_key", env="SECRET_KEY")
    JWT_SECRET_KEY: str = Field(default="dev_jwt_secret", env="JWT_SECRET_KEY")
    ENCRYPTION_KEY: str = Field(default="dev_encryption_key_32_chars_long", env="ENCRYPTION_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Database Configuration
    POSTGRES_SERVER: str = Field(default="localhost", env="POSTGRES_SERVER")
    POSTGRES_USER: str = Field(default="quantum_user", env="POSTGRES_USER")
    POSTGRES_PASSWORD: str = Field(default="quantum_secure_pass_2024", env="POSTGRES_PASSWORD")
    POSTGRES_DB: str = Field(default="quantum_market_db", env="POSTGRES_DB")
    POSTGRES_PORT: int = Field(default=5433, env="POSTGRES_PORT")
    DATABASE_URL: Optional[PostgresDsn] = None
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            user=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            port=str(values.get("POSTGRES_PORT")),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )
    
    # Redis Configuration
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6381, env="REDIS_PORT")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_URL: Optional[RedisDsn] = None
    
    @validator("REDIS_URL", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        
        password = values.get("REDIS_PASSWORD")
        auth_part = f":{password}@" if password else ""
        
        return f"redis://{auth_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT')}/{values.get('REDIS_DB')}"
    
    # Kafka Configuration
    KAFKA_BOOTSTRAP_SERVERS: str = Field(default="localhost:9092", env="KAFKA_BOOTSTRAP_SERVERS")
    SCHEMA_REGISTRY_URL: str = Field(default="http://localhost:8081", env="SCHEMA_REGISTRY_URL")
    KAFKA_CONSUMER_GROUP: str = Field(default="quantum-market-consumers", env="KAFKA_CONSUMER_GROUP")
    
    # External API Keys
    THIRDWEB_CLIENT_ID: Optional[str] = Field(default=None, env="THIRDWEB_CLIENT_ID")
    THIRDWEB_SECRET_KEY: Optional[str] = Field(default=None, env="THIRDWEB_SECRET_KEY")
    NEBULA_API_KEY: Optional[str] = Field(default=None, env="NEBULA_API_KEY")
    
    # Financial Data APIs
    BINANCE_API_KEY: Optional[str] = Field(default=None, env="BINANCE_API_KEY")
    BINANCE_SECRET_KEY: Optional[str] = Field(default=None, env="BINANCE_SECRET_KEY")
    COINGECKO_API_KEY: Optional[str] = Field(default=None, env="COINGECKO_API_KEY")
    ALPHA_VANTAGE_API_KEY: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    QUANDL_API_KEY: Optional[str] = Field(default=None, env="QUANDL_API_KEY")
    
    # Social Media APIs
    REDDIT_CLIENT_ID: Optional[str] = Field(default=None, env="REDDIT_CLIENT_ID")
    REDDIT_CLIENT_SECRET: Optional[str] = Field(default=None, env="REDDIT_CLIENT_SECRET")
    TWITTER_BEARER_TOKEN: Optional[str] = Field(default=None, env="TWITTER_BEARER_TOKEN")
    
    # Blockchain RPC URLs
    ETHEREUM_RPC_URL: Optional[str] = Field(default=None, env="ETHEREUM_RPC_URL")
    POLYGON_RPC_URL: Optional[str] = Field(default=None, env="POLYGON_RPC_URL")
    BSC_RPC_URL: Optional[str] = Field(default=None, env="BSC_RPC_URL")
    
    # CrossRef API for Citation Verification
    CROSSREF_API_URL: str = Field(default="https://api.crossref.org", env="CROSSREF_API_URL")
    CROSSREF_MAILTO: Optional[str] = Field(default=None, env="CROSSREF_MAILTO")
    
    # OpenTelemetry Configuration
    OTEL_EXPORTER_OTLP_ENDPOINT: str = Field(default="http://localhost:4317", env="OTEL_EXPORTER_OTLP_ENDPOINT")
    OTEL_SERVICE_NAME: str = Field(default="quantum-market-intelligence", env="OTEL_SERVICE_NAME")
    OTEL_RESOURCE_ATTRIBUTES: str = Field(
        default="service.name=quantum-market-intelligence,service.version=1.0.0",
        env="OTEL_RESOURCE_ATTRIBUTES"
    )
    
    # Monitoring
    PROMETHEUS_PORT: int = Field(default=9090, env="PROMETHEUS_PORT")
    GRAFANA_PORT: int = Field(default=3001, env="GRAFANA_PORT")
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    RATE_LIMIT_BURST: int = Field(default=10, env="RATE_LIMIT_BURST")
    
    # Quantum Model Configuration
    MUTUAL_INFORMATION_THRESHOLD: float = Field(default=0.25, env="MUTUAL_INFORMATION_THRESHOLD")
    QUANTUM_TUNNELING_THRESHOLD: float = Field(default=0.65, env="QUANTUM_TUNNELING_THRESHOLD")
    VOLATILITY_WINDOW_DAYS: int = Field(default=30, env="VOLATILITY_WINDOW_DAYS")
    
    # Data Retention
    PRICE_DATA_RETENTION_DAYS: int = Field(default=365, env="PRICE_DATA_RETENTION_DAYS")
    SENTIMENT_DATA_RETENTION_DAYS: int = Field(default=180, env="SENTIMENT_DATA_RETENTION_DAYS")
    AUDIT_LOG_RETENTION_DAYS: int = Field(default=90, env="AUDIT_LOG_RETENTION_DAYS")
    
    # Performance Tuning
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    REDIS_POOL_SIZE: int = Field(default=10, env="REDIS_POOL_SIZE")
    
    # Security
    ALLOWED_HOSTS: List[str] = Field(default=["localhost", "127.0.0.1"], env="ALLOWED_HOSTS")
    TRUSTED_PROXIES: List[str] = Field(default=[], env="TRUSTED_PROXIES")
    DATA_INTEGRITY_SECRET: str = Field(default="quantum_market_intelligence_secret_key_2025", env="DATA_INTEGRITY_SECRET")
    FERNET_KEY: str = Field(default="", env="FERNET_KEY")
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("TRUSTED_PROXIES", pre=True)
    def parse_trusted_proxies(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str):
            return [proxy.strip() for proxy in v.split(",")]
        return v
    
    # Validation
    @validator("ENVIRONMENT")
    def validate_environment(cls, v: str) -> str:
        allowed_envs = ["development", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of {allowed_envs}")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v: str) -> str:
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of {allowed_levels}")
        return v.upper()
    
    @validator("MUTUAL_INFORMATION_THRESHOLD")
    def validate_mi_threshold(cls, v: float) -> float:
        if not 0.0 <= v <= 1.0:
            raise ValueError("Mutual information threshold must be between 0.0 and 1.0")
        return v
    
    @validator("QUANTUM_TUNNELING_THRESHOLD")
    def validate_quantum_threshold(cls, v: float) -> float:
        if not 0.0 <= v <= 1.0:
            raise ValueError("Quantum tunneling threshold must be between 0.0 and 1.0")
        return v
    
    # Feature Flags
    ENABLE_SOCIAL_SENTIMENT: bool = Field(default=True, env="ENABLE_SOCIAL_SENTIMENT")
    ENABLE_ONCHAIN_ANALYSIS: bool = Field(default=True, env="ENABLE_ONCHAIN_ANALYSIS")
    ENABLE_QUANTUM_MODELS: bool = Field(default=True, env="ENABLE_QUANTUM_MODELS")
    ENABLE_REAL_TIME_ALERTS: bool = Field(default=True, env="ENABLE_REAL_TIME_ALERTS")
    ENABLE_BACKTESTING: bool = Field(default=True, env="ENABLE_BACKTESTING")
    
    # Testing Configuration
    TEST_DATABASE_URL: Optional[str] = Field(default=None, env="TEST_DATABASE_URL")
    TEST_REDIS_URL: Optional[str] = Field(default=None, env="TEST_REDIS_URL")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT == "development"
    
    def get_database_url(self, for_testing: bool = False) -> str:
        """Get database URL for current environment."""
        if for_testing and self.TEST_DATABASE_URL:
            return self.TEST_DATABASE_URL
        return str(self.DATABASE_URL)
    
    def get_redis_url(self, for_testing: bool = False) -> str:
        """Get Redis URL for current environment."""
        if for_testing and self.TEST_REDIS_URL:
            return self.TEST_REDIS_URL
        return str(self.REDIS_URL)


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
