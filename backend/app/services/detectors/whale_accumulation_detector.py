"""
Whale Accumulation Pattern Detector for Quantum Market Intelligence.
Detects whale accumulation patterns using volume analysis, price stability,
and large transaction indicators.
"""

import logging
import statistics
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from uuid import uuid4

from app.models.pattern_detection import (
    PatternType, PatternStatus, PatternDetectionResult,
    WhaleAccumulationPatternData, PatternDetectorConfig
)
from app.services.pattern_detection import BasePatternDetector

logger = logging.getLogger(__name__)


class WhaleAccumulationDetector(BasePatternDetector):
    """
    Detector for whale accumulation patterns.
    
    Detection Criteria:
    1. Large Volume Transactions: Consistent large volume trades
    2. Price Stability: Price remains relatively stable during accumulation
    3. Volume Pattern: Sustained elevated volume over time
    4. Accumulation Duration: Pattern sustained over 6-48 hours
    5. Wallet Concentration: Increasing concentration in large wallets (if available)
    """
    
    def __init__(self, config: PatternDetectorConfig):
        super().__init__(config)
        
        # Detection thresholds (adjusted based on validation testing)
        self.large_volume_threshold = 1.5  # 1.5x average volume for large transactions (reduced from 2.0)
        self.price_stability_threshold = 0.10  # Max 10% price deviation during accumulation (increased from 5%)
        self.min_accumulation_hours = 6  # Minimum accumulation duration
        self.max_accumulation_hours = 48  # Maximum accumulation duration
        self.volume_consistency_threshold = 0.6  # 60% of periods should have elevated volume (reduced from 70%)
        
        # Confidence weights
        self.confidence_weights = {
            'large_transactions': 0.30,
            'price_stability': 0.25,
            'volume_consistency': 0.20,
            'accumulation_duration': 0.15,
            'wallet_concentration': 0.10
        }
    
    def get_pattern_type(self) -> PatternType:
        """Return the pattern type this detector handles."""
        return PatternType.WHALE_ACCUMULATION
    
    async def detect_patterns(
        self, 
        symbol: str, 
        market_data: List[Dict[str, Any]], 
        context_data: Optional[Dict[str, Any]] = None
    ) -> List[PatternDetectionResult]:
        """
        Detect whale accumulation patterns in market data.
        
        Args:
            symbol: The symbol to analyze
            market_data: List of market data points (sorted by timestamp DESC)
            context_data: Additional context data (on-chain data, etc.)
            
        Returns:
            List of detected whale accumulation patterns
        """
        if len(market_data) < 24:  # Need at least 24 data points (6+ hours)
            logger.debug(f"Insufficient data for {symbol}: {len(market_data)} points")
            return []
        
        try:
            logger.debug(f"Analyzing whale accumulation patterns for {symbol} with {len(market_data)} data points")
            
            # Prepare data for analysis
            sorted_data = sorted(market_data, key=lambda x: x['timestamp'])
            
            # Find potential accumulation periods
            accumulation_periods = self._identify_accumulation_periods(sorted_data)
            
            if not accumulation_periods:
                return []
            
            detected_patterns = []
            
            for period in accumulation_periods:
                period_data = sorted_data[period['start_idx']:period['end_idx']]
                
                # Analyze large transaction patterns
                transaction_analysis = self._analyze_large_transactions(period_data)
                
                # Analyze price stability during accumulation
                stability_analysis = self._analyze_price_stability(period_data)
                
                # Analyze volume consistency
                volume_analysis = self._analyze_volume_consistency(period_data)
                
                # Analyze accumulation duration
                duration_analysis = self._analyze_accumulation_duration(period_data)
                
                # Analyze wallet concentration (if available)
                concentration_analysis = self._analyze_wallet_concentration(context_data, period)
                
                # Calculate overall confidence
                indicators = {
                    'large_transactions': transaction_analysis['strength'],
                    'price_stability': stability_analysis['stability_score'],
                    'volume_consistency': volume_analysis['consistency_score'],
                    'accumulation_duration': duration_analysis['duration_score'],
                    'wallet_concentration': concentration_analysis['concentration_score']
                }
                
                confidence_score = self._calculate_weighted_confidence(indicators)
                
                # Check if pattern meets minimum confidence threshold
                if confidence_score < self.config.confidence_threshold:
                    continue
                
                # Create pattern detection result
                pattern_data = WhaleAccumulationPatternData(
                    large_transaction_count=transaction_analysis['transaction_count'],
                    accumulation_volume=volume_analysis['total_volume'],
                    price_stability_score=stability_analysis['stability_score'],
                    wallet_concentration_change=concentration_analysis['concentration_change'],
                    accumulation_duration_hours=int(duration_analysis['duration_hours'])  # Convert to int
                )
                
                # Determine expiry time (accumulation patterns can last days)
                detection_time = datetime.now(timezone.utc)
                expiry_time = detection_time + timedelta(hours=72)  # 3 days
                
                pattern_result = PatternDetectionResult(
                    pattern_id=str(uuid4()),
                    pattern_type=PatternType.WHALE_ACCUMULATION,
                    symbol=symbol,
                    confidence_score=confidence_score,
                    status=PatternStatus.DETECTED,
                    detection_timestamp=detection_time,
                    expiry_timestamp=expiry_time,
                    pattern_data=pattern_data.dict(),
                    trigger_conditions={
                        'large_transactions_detected': transaction_analysis['detected'],
                        'price_stable_during_accumulation': stability_analysis['stable'],
                        'volume_consistently_elevated': volume_analysis['consistent'],
                        'accumulation_duration_adequate': duration_analysis['adequate']
                    },
                    supporting_indicators={
                        'avg_transaction_size': transaction_analysis['avg_size'],
                        'price_volatility': stability_analysis['volatility'],
                        'volume_elevation_ratio': volume_analysis['elevation_ratio'],
                        'accumulation_start_time': period_data[0]['timestamp'].isoformat(),
                        'accumulation_end_time': period_data[-1]['timestamp'].isoformat()
                    },
                    risk_factors={
                        'false_positive_risk': self._calculate_false_positive_risk(indicators),
                        'market_manipulation_risk': self._assess_manipulation_risk(transaction_analysis, stability_analysis),
                        'data_completeness': self._assess_data_completeness(period_data)
                    },
                    detector_version=self.version
                )
                
                detected_patterns.append(pattern_result)
                logger.info(f"Whale accumulation pattern detected for {symbol}: confidence={confidence_score:.3f}")
            
            return detected_patterns
            
        except Exception as e:
            logger.error(f"Error detecting whale accumulation patterns for {symbol}: {e}")
            return []
    
    def _identify_accumulation_periods(self, sorted_data: List[Dict]) -> List[Dict[str, Any]]:
        """Identify potential accumulation periods in the data."""
        periods = []
        
        try:
            # Calculate rolling volume average
            window_size = 6  # 6 data points window
            volumes = [float(d['volume']) for d in sorted_data if d.get('volume', 0) > 0]
            
            if len(volumes) < window_size * 2:
                return periods
            
            # Calculate baseline volume (first half of data)
            baseline_volume = statistics.mean(volumes[:len(volumes)//2])
            
            # Find periods of elevated volume
            elevated_periods = []
            current_period_start = None
            
            for i in range(window_size, len(sorted_data)):
                window_volumes = volumes[i-window_size:i]
                avg_volume = statistics.mean(window_volumes)
                
                if avg_volume >= baseline_volume * 1.5:  # 1.5x baseline volume
                    if current_period_start is None:
                        current_period_start = i - window_size
                else:
                    if current_period_start is not None:
                        # End of elevated period
                        period_duration = (sorted_data[i-1]['timestamp'] - sorted_data[current_period_start]['timestamp']).total_seconds() / 3600
                        
                        if self.min_accumulation_hours <= period_duration <= self.max_accumulation_hours:
                            periods.append({
                                'start_idx': current_period_start,
                                'end_idx': i,
                                'duration_hours': period_duration
                            })
                        
                        current_period_start = None
            
            # Handle case where elevated period extends to end of data
            if current_period_start is not None:
                period_duration = (sorted_data[-1]['timestamp'] - sorted_data[current_period_start]['timestamp']).total_seconds() / 3600
                if self.min_accumulation_hours <= period_duration <= self.max_accumulation_hours:
                    periods.append({
                        'start_idx': current_period_start,
                        'end_idx': len(sorted_data),
                        'duration_hours': period_duration
                    })
            
            return periods
            
        except Exception as e:
            logger.error(f"Error identifying accumulation periods: {e}")
            return []
    
    def _analyze_large_transactions(self, period_data: List[Dict]) -> Dict[str, Any]:
        """Analyze large transaction patterns during the period."""
        try:
            volumes = [float(d['volume']) for d in period_data if d.get('volume', 0) > 0]

            if not volumes:
                return {'detected': False, 'strength': 0.0, 'transaction_count': 0, 'avg_size': 0.0}

            avg_volume = statistics.mean(volumes)
            large_transactions = [v for v in volumes if v >= avg_volume * self.large_volume_threshold]

            transaction_count = len(large_transactions)
            avg_large_size = statistics.mean(large_transactions) if large_transactions else 0.0

            # Improved strength calculation for whale accumulation
            if len(volumes) == 0:
                frequency_score = 0.0
            else:
                frequency_score = min(transaction_count / len(volumes), 1.0)

            # For whale accumulation, consistent elevated volume is more important than extreme spikes
            if avg_volume > 0:
                # Check if most transactions are above baseline (whale accumulation pattern)
                baseline_volume = min(volumes) if volumes else 0
                elevated_count = sum(1 for v in volumes if v >= baseline_volume * 1.5)
                consistency_score = elevated_count / len(volumes) if volumes else 0
                size_score = min(avg_volume / (baseline_volume * 2), 1.0) if baseline_volume > 0 else 0.5
            else:
                consistency_score = 0.0
                size_score = 0.0

            # Whale accumulation strength focuses on consistency rather than individual large transactions
            strength = (frequency_score * 0.3 + consistency_score * 0.5 + size_score * 0.2)
            detected = consistency_score >= 0.6 or (transaction_count >= 2 and strength >= 0.4)

            return {
                'detected': detected,
                'strength': strength,
                'transaction_count': transaction_count,
                'avg_size': avg_large_size
            }
            
        except Exception as e:
            logger.error(f"Error analyzing large transactions: {e}")
            return {'detected': False, 'strength': 0.0, 'transaction_count': 0, 'avg_size': 0.0}
    
    def _analyze_price_stability(self, period_data: List[Dict]) -> Dict[str, Any]:
        """Analyze price stability during accumulation period."""
        try:
            prices = [float(d['price']) for d in period_data if d.get('price', 0) > 0]
            
            if len(prices) < 2:
                return {'stable': False, 'stability_score': 0.0, 'volatility': 0.0}
            
            # Calculate price volatility
            price_changes = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] > 0]
            volatility = statistics.stdev(price_changes) if len(price_changes) > 1 else 0.0
            
            # Calculate overall price change during period
            start_price = prices[0]
            end_price = prices[-1]
            total_change = abs(end_price - start_price) / start_price if start_price > 0 else 0.0
            
            # Stability score (higher is more stable)
            volatility_score = max(0.0, 1.0 - (volatility / 0.1))  # Normalize volatility
            change_score = max(0.0, 1.0 - (total_change / self.price_stability_threshold))
            
            stability_score = (volatility_score + change_score) / 2
            stable = total_change <= self.price_stability_threshold and volatility <= 0.05
            
            return {
                'stable': stable,
                'stability_score': stability_score,
                'volatility': volatility
            }
            
        except Exception as e:
            logger.error(f"Error analyzing price stability: {e}")
            return {'stable': False, 'stability_score': 0.0, 'volatility': 0.0}
    
    def _analyze_volume_consistency(self, period_data: List[Dict]) -> Dict[str, Any]:
        """Analyze volume consistency during accumulation."""
        try:
            volumes = [float(d['volume']) for d in period_data if d.get('volume', 0) > 0]

            if not volumes:
                return {'consistent': False, 'consistency_score': 0.0, 'total_volume': 0.0, 'elevation_ratio': 0.0}

            total_volume = sum(volumes)
            avg_volume = statistics.mean(volumes)

            # For whale accumulation, we need to compare against historical baseline
            # Since we don't have historical data in the period, use the minimum volume as baseline
            baseline_volume = min(volumes) if volumes else 1.0

            # For whale patterns, look for sustained elevation above baseline
            elevation_threshold = baseline_volume * 1.3  # 30% above minimum
            elevated_count = sum(1 for v in volumes if v >= elevation_threshold)
            consistency_ratio = elevated_count / len(volumes) if volumes else 0.0

            elevation_ratio = avg_volume / baseline_volume if baseline_volume > 0 else 0.0

            # Improved consistency scoring for whale accumulation
            if elevation_ratio >= 1.5:  # At least 1.5x elevation
                base_score = min(consistency_ratio / self.volume_consistency_threshold, 1.0)
                # Bonus for higher elevation ratios
                elevation_bonus = min((elevation_ratio - 1.5) / 1.0, 0.3)  # Up to 30% bonus
                consistency_score = min(base_score + elevation_bonus, 1.0)
            else:
                consistency_score = consistency_ratio / self.volume_consistency_threshold

            consistent = consistency_ratio >= self.volume_consistency_threshold or elevation_ratio >= 2.0

            return {
                'consistent': consistent,
                'consistency_score': consistency_score,
                'total_volume': total_volume,
                'elevation_ratio': elevation_ratio
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume consistency: {e}")
            return {'consistent': False, 'consistency_score': 0.0, 'total_volume': 0.0, 'elevation_ratio': 0.0}
    
    def _analyze_accumulation_duration(self, period_data: List[Dict]) -> Dict[str, Any]:
        """Analyze accumulation duration."""
        try:
            if len(period_data) < 2:
                return {'adequate': False, 'duration_score': 0.0, 'duration_hours': 0.0}
            
            start_time = period_data[0]['timestamp']
            end_time = period_data[-1]['timestamp']
            duration_hours = (end_time - start_time).total_seconds() / 3600
            
            # Optimal duration is 12-24 hours
            if 12 <= duration_hours <= 24:
                duration_score = 1.0
            elif 6 <= duration_hours < 12:
                duration_score = 0.7
            elif 24 < duration_hours <= 48:
                duration_score = 0.8
            else:
                duration_score = 0.3
            
            adequate = duration_hours >= self.min_accumulation_hours
            
            return {
                'adequate': adequate,
                'duration_score': duration_score,
                'duration_hours': duration_hours
            }
            
        except Exception as e:
            logger.error(f"Error analyzing accumulation duration: {e}")
            return {'adequate': False, 'duration_score': 0.0, 'duration_hours': 0.0}
    
    def _analyze_wallet_concentration(self, context_data: Optional[Dict[str, Any]], period: Dict) -> Dict[str, Any]:
        """Analyze wallet concentration changes (if on-chain data available)."""
        # Placeholder for on-chain analysis
        # This would integrate with blockchain APIs for wallet concentration data
        
        default_result = {
            'concentration_score': 0.5,  # Neutral score when data unavailable
            'concentration_change': 0.0
        }
        
        if not context_data or 'on_chain' not in context_data:
            return default_result
        
        try:
            on_chain_data = context_data['on_chain']
            
            # Analyze wallet concentration changes
            large_wallet_percentage_start = on_chain_data.get('large_wallet_percentage_start', 0.0)
            large_wallet_percentage_end = on_chain_data.get('large_wallet_percentage_end', 0.0)
            
            concentration_change = large_wallet_percentage_end - large_wallet_percentage_start
            
            # Score based on concentration increase
            if concentration_change > 0.05:  # 5% increase in large wallet concentration
                concentration_score = 1.0
            elif concentration_change > 0.02:  # 2% increase
                concentration_score = 0.7
            elif concentration_change > 0:
                concentration_score = 0.5
            else:
                concentration_score = 0.2
            
            return {
                'concentration_score': concentration_score,
                'concentration_change': concentration_change
            }
            
        except Exception as e:
            logger.error(f"Error analyzing wallet concentration: {e}")
            return default_result
    
    def _calculate_weighted_confidence(self, indicators: Dict[str, float]) -> float:
        """Calculate weighted confidence score."""
        total_score = 0.0
        
        for indicator, value in indicators.items():
            weight = self.confidence_weights.get(indicator, 0.0)
            total_score += weight * value
        
        return min(max(total_score, 0.0), 1.0)
    
    def _calculate_false_positive_risk(self, indicators: Dict[str, float]) -> float:
        """Calculate false positive risk."""
        # Lower risk if multiple indicators are strong
        strong_indicators = sum(1 for v in indicators.values() if v > 0.7)
        
        if strong_indicators >= 3:
            return 0.2  # Low risk
        elif strong_indicators >= 2:
            return 0.4  # Medium risk
        else:
            return 0.7  # High risk
    
    def _assess_manipulation_risk(self, transaction_analysis: Dict, stability_analysis: Dict) -> float:
        """Assess market manipulation risk."""
        # High transaction volume with artificial price stability might indicate manipulation
        transaction_strength = transaction_analysis.get('strength', 0.0)
        stability_score = stability_analysis.get('stability_score', 0.0)
        
        # Unusually high stability with very large transactions might be suspicious
        if transaction_strength > 0.9 and stability_score > 0.95:
            return 0.8  # High manipulation risk
        elif transaction_strength > 0.7 and stability_score > 0.8:
            return 0.5  # Medium risk
        else:
            return 0.2  # Low risk
    
    def _assess_data_completeness(self, period_data: List[Dict]) -> float:
        """Assess completeness of data for the period."""
        if not period_data:
            return 0.0
        
        # Check for missing data points
        valid_points = 0
        for data_point in period_data:
            if (data_point.get('price', 0) > 0 and 
                data_point.get('volume', 0) >= 0 and 
                data_point.get('timestamp')):
                valid_points += 1
        
        return valid_points / len(period_data)
