"""
Social Sentiment Pattern Detector for Quantum Market Intelligence.
Detects social sentiment patterns and correlations with market movements
using Reddit API data and other social media indicators.
"""

import logging
import statistics
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from uuid import uuid4

from app.models.pattern_detection import (
    <PERSON><PERSON>Type, PatternStatus, PatternDetectionResult,
    SocialSentimentPatternData, PatternDetectorConfig
)
from app.services.pattern_detection import BasePatternDetector

logger = logging.getLogger(__name__)


class SocialSentimentDetector(BasePatternDetector):
    """
    Detector for social sentiment patterns and market correlations.
    
    Detection Criteria:
    1. Sentiment Spike: Significant increase in positive/negative sentiment
    2. Mention Volume: Increased discussion volume about the asset
    3. Sentiment Velocity: Rate of sentiment change
    4. Influencer Activity: Key influencer mentions and sentiment
    5. Price Correlation: Correlation between sentiment and price movements
    """
    
    def __init__(self, config: PatternDetectorConfig):
        super().__init__(config)
        
        # Detection thresholds
        self.sentiment_spike_threshold = 0.3  # 30% sentiment change
        self.mention_volume_threshold = 2.0  # 2x normal mention volume
        self.correlation_threshold = 0.6  # 60% correlation with price
        self.min_mentions_required = 10  # Minimum mentions for analysis
        self.sentiment_velocity_threshold = 0.1  # Sentiment change rate
        
        # Confidence weights
        self.confidence_weights = {
            'sentiment_strength': 0.25,
            'mention_volume': 0.20,
            'sentiment_velocity': 0.20,
            'price_correlation': 0.25,
            'influencer_activity': 0.10
        }
    
    def get_pattern_type(self) -> PatternType:
        """Return the pattern type this detector handles."""
        return PatternType.SOCIAL_SENTIMENT_SPIKE
    
    async def detect_patterns(
        self, 
        symbol: str, 
        market_data: List[Dict[str, Any]], 
        context_data: Optional[Dict[str, Any]] = None
    ) -> List[PatternDetectionResult]:
        """
        Detect social sentiment patterns and correlations.
        
        Args:
            symbol: The symbol to analyze
            market_data: List of market data points (sorted by timestamp DESC)
            context_data: Social media data (Reddit, Twitter, etc.)
            
        Returns:
            List of detected social sentiment patterns
        """
        if not context_data or 'social' not in context_data:
            logger.debug(f"No social data available for {symbol}")
            return []
        
        if len(market_data) < 12:
            logger.debug(f"Insufficient market data for {symbol}: {len(market_data)} points")
            return []
        
        try:
            logger.debug(f"Analyzing social sentiment patterns for {symbol}")
            
            social_data = context_data['social']
            
            # Analyze sentiment strength and spikes
            sentiment_analysis = self._analyze_sentiment_patterns(social_data)
            
            # Analyze mention volume changes
            volume_analysis = self._analyze_mention_volume(social_data)
            
            # Analyze sentiment velocity (rate of change)
            velocity_analysis = self._analyze_sentiment_velocity(social_data)
            
            # Analyze correlation with price movements
            correlation_analysis = self._analyze_price_correlation(market_data, social_data)
            
            # Analyze influencer activity
            influencer_analysis = self._analyze_influencer_activity(social_data)
            
            # Calculate overall confidence
            indicators = {
                'sentiment_strength': sentiment_analysis['strength'],
                'mention_volume': volume_analysis['volume_score'],
                'sentiment_velocity': velocity_analysis['velocity_score'],
                'price_correlation': correlation_analysis['correlation_score'],
                'influencer_activity': influencer_analysis['activity_score']
            }
            
            confidence_score = self._calculate_weighted_confidence(indicators)
            
            # Check if pattern meets minimum confidence threshold
            if confidence_score < self.config.confidence_threshold:
                logger.debug(f"Social sentiment pattern for {symbol} below threshold: {confidence_score:.3f}")
                return []
            
            # Create pattern detection result
            pattern_data = SocialSentimentPatternData(
                sentiment_score=sentiment_analysis['current_sentiment'],
                mention_volume_change=volume_analysis['volume_change'],
                sentiment_velocity=velocity_analysis['velocity'],
                key_influencer_mentions=influencer_analysis['influencer_mentions'],
                correlation_with_price=correlation_analysis['correlation']
            )
            
            # Determine expiry time (sentiment patterns are typically short-lived)
            detection_time = datetime.now(timezone.utc)
            expiry_time = detection_time + timedelta(hours=24)  # 24 hours
            
            pattern_result = PatternDetectionResult(
                pattern_id=str(uuid4()),
                pattern_type=PatternType.SOCIAL_SENTIMENT_SPIKE,
                symbol=symbol,
                confidence_score=confidence_score,
                status=PatternStatus.DETECTED,
                detection_timestamp=detection_time,
                expiry_timestamp=expiry_time,
                pattern_data=pattern_data.dict(),
                trigger_conditions={
                    'sentiment_spike_detected': sentiment_analysis['spike_detected'],
                    'mention_volume_increased': volume_analysis['volume_increased'],
                    'high_sentiment_velocity': velocity_analysis['high_velocity'],
                    'price_correlation_significant': correlation_analysis['significant_correlation']
                },
                supporting_indicators={
                    'baseline_sentiment': sentiment_analysis['baseline_sentiment'],
                    'baseline_mentions': volume_analysis['baseline_mentions'],
                    'sentiment_trend': velocity_analysis['trend'],
                    'correlation_strength': correlation_analysis['correlation'],
                    'top_influencers': influencer_analysis['top_influencers']
                },
                risk_factors={
                    'sentiment_manipulation_risk': self._assess_manipulation_risk(sentiment_analysis, volume_analysis),
                    'data_quality_score': self._assess_social_data_quality(social_data),
                    'sample_size_adequacy': self._assess_sample_size(social_data)
                },
                detector_version=self.version
            )
            
            logger.info(f"Social sentiment pattern detected for {symbol}: confidence={confidence_score:.3f}")
            return [pattern_result]
            
        except Exception as e:
            logger.error(f"Error detecting social sentiment patterns for {symbol}: {e}")
            return []
    
    def _analyze_sentiment_patterns(self, social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment patterns and spikes."""
        try:
            current_sentiment = social_data.get('current_sentiment', 0.0)  # -1 to 1
            baseline_sentiment = social_data.get('baseline_sentiment', 0.0)
            sentiment_history = social_data.get('sentiment_history', [])
            
            # Calculate sentiment change
            sentiment_change = abs(current_sentiment - baseline_sentiment)
            spike_detected = sentiment_change >= self.sentiment_spike_threshold
            
            # Calculate sentiment strength (how extreme the sentiment is)
            sentiment_strength = abs(current_sentiment)
            
            # Analyze sentiment consistency
            if sentiment_history:
                recent_sentiments = sentiment_history[-6:]  # Last 6 data points
                sentiment_std = statistics.stdev(recent_sentiments) if len(recent_sentiments) > 1 else 0.0
                consistency = max(0.0, 1.0 - sentiment_std)  # Lower std = higher consistency
            else:
                consistency = 0.5
            
            # Overall strength score
            strength = (sentiment_strength + (1.0 if spike_detected else 0.0) + consistency) / 3
            
            return {
                'spike_detected': spike_detected,
                'strength': strength,
                'current_sentiment': current_sentiment,
                'baseline_sentiment': baseline_sentiment,
                'sentiment_change': sentiment_change
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment patterns: {e}")
            return {
                'spike_detected': False,
                'strength': 0.0,
                'current_sentiment': 0.0,
                'baseline_sentiment': 0.0,
                'sentiment_change': 0.0
            }
    
    def _analyze_mention_volume(self, social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze mention volume changes."""
        try:
            current_mentions = social_data.get('current_mentions', 0)
            baseline_mentions = social_data.get('baseline_mentions', 1)
            mention_history = social_data.get('mention_history', [])
            
            # Calculate volume change
            volume_change = current_mentions / baseline_mentions if baseline_mentions > 0 else 0.0
            volume_increased = volume_change >= self.mention_volume_threshold
            
            # Check if we have enough mentions for reliable analysis
            sufficient_mentions = current_mentions >= self.min_mentions_required
            
            # Calculate volume score
            if sufficient_mentions:
                volume_score = min(volume_change / self.mention_volume_threshold, 1.0)
            else:
                volume_score = 0.0
            
            # Analyze mention trend
            if len(mention_history) >= 3:
                recent_trend = mention_history[-3:]
                trend_increasing = all(recent_trend[i] <= recent_trend[i+1] for i in range(len(recent_trend)-1))
            else:
                trend_increasing = False
            
            return {
                'volume_increased': volume_increased,
                'volume_score': volume_score,
                'volume_change': volume_change,
                'baseline_mentions': baseline_mentions,
                'sufficient_mentions': sufficient_mentions,
                'trend_increasing': trend_increasing
            }
            
        except Exception as e:
            logger.error(f"Error analyzing mention volume: {e}")
            return {
                'volume_increased': False,
                'volume_score': 0.0,
                'volume_change': 0.0,
                'baseline_mentions': 0,
                'sufficient_mentions': False,
                'trend_increasing': False
            }
    
    def _analyze_sentiment_velocity(self, social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment velocity (rate of change)."""
        try:
            sentiment_history = social_data.get('sentiment_history', [])
            
            if len(sentiment_history) < 3:
                return {
                    'high_velocity': False,
                    'velocity_score': 0.0,
                    'velocity': 0.0,
                    'trend': 'unknown'
                }
            
            # Calculate sentiment velocity (change per time period)
            recent_sentiments = sentiment_history[-6:]  # Last 6 periods
            
            # Calculate average rate of change
            changes = [recent_sentiments[i] - recent_sentiments[i-1] for i in range(1, len(recent_sentiments))]
            avg_velocity = statistics.mean(changes) if changes else 0.0
            velocity_magnitude = abs(avg_velocity)
            
            # Determine trend
            if avg_velocity > 0.05:
                trend = 'positive'
            elif avg_velocity < -0.05:
                trend = 'negative'
            else:
                trend = 'stable'
            
            # Check if velocity is high
            high_velocity = velocity_magnitude >= self.sentiment_velocity_threshold
            velocity_score = min(velocity_magnitude / self.sentiment_velocity_threshold, 1.0)
            
            return {
                'high_velocity': high_velocity,
                'velocity_score': velocity_score,
                'velocity': avg_velocity,
                'trend': trend
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment velocity: {e}")
            return {
                'high_velocity': False,
                'velocity_score': 0.0,
                'velocity': 0.0,
                'trend': 'unknown'
            }
    
    def _analyze_price_correlation(self, market_data: List[Dict], social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze correlation between sentiment and price movements."""
        try:
            sentiment_history = social_data.get('sentiment_history', [])
            
            if len(sentiment_history) < 6 or len(market_data) < 6:
                return {
                    'significant_correlation': False,
                    'correlation_score': 0.0,
                    'correlation': 0.0
                }
            
            # Get price changes
            sorted_market_data = sorted(market_data, key=lambda x: x['timestamp'])
            prices = [float(d['price']) for d in sorted_market_data[-len(sentiment_history):]]
            
            if len(prices) != len(sentiment_history):
                # Align data lengths
                min_length = min(len(prices), len(sentiment_history))
                prices = prices[-min_length:]
                sentiments = sentiment_history[-min_length:]
            else:
                sentiments = sentiment_history
            
            # Calculate price changes
            price_changes = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] > 0]
            sentiment_changes = [sentiments[i] - sentiments[i-1] for i in range(1, len(sentiments))]
            
            if len(price_changes) < 3 or len(sentiment_changes) < 3:
                return {
                    'significant_correlation': False,
                    'correlation_score': 0.0,
                    'correlation': 0.0
                }
            
            # Calculate correlation coefficient
            correlation = self._calculate_correlation(price_changes, sentiment_changes)
            
            # Check if correlation is significant
            significant_correlation = abs(correlation) >= self.correlation_threshold
            correlation_score = abs(correlation)
            
            return {
                'significant_correlation': significant_correlation,
                'correlation_score': correlation_score,
                'correlation': correlation
            }
            
        except Exception as e:
            logger.error(f"Error analyzing price correlation: {e}")
            return {
                'significant_correlation': False,
                'correlation_score': 0.0,
                'correlation': 0.0
            }
    
    def _analyze_influencer_activity(self, social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze key influencer activity and mentions."""
        try:
            influencer_mentions = social_data.get('influencer_mentions', 0)
            top_influencers = social_data.get('top_influencers', [])
            influencer_sentiment = social_data.get('influencer_sentiment', 0.0)
            
            # Score based on influencer activity
            if influencer_mentions >= 3:
                activity_score = 1.0
            elif influencer_mentions >= 1:
                activity_score = 0.7
            else:
                activity_score = 0.0
            
            # Boost score if sentiment is strong
            if abs(influencer_sentiment) > 0.5:
                activity_score = min(activity_score * 1.2, 1.0)
            
            return {
                'activity_score': activity_score,
                'influencer_mentions': influencer_mentions,
                'top_influencers': top_influencers,
                'influencer_sentiment': influencer_sentiment
            }
            
        except Exception as e:
            logger.error(f"Error analyzing influencer activity: {e}")
            return {
                'activity_score': 0.0,
                'influencer_mentions': 0,
                'top_influencers': [],
                'influencer_sentiment': 0.0
            }
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """Calculate Pearson correlation coefficient."""
        try:
            if len(x) != len(y) or len(x) < 2:
                return 0.0
            
            n = len(x)
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[i] * y[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)
            sum_y2 = sum(yi * yi for yi in y)
            
            numerator = n * sum_xy - sum_x * sum_y
            denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5
            
            if denominator == 0:
                return 0.0
            
            correlation = numerator / denominator
            return max(-1.0, min(1.0, correlation))  # Clamp to [-1, 1]
            
        except Exception as e:
            logger.error(f"Error calculating correlation: {e}")
            return 0.0
    
    def _calculate_weighted_confidence(self, indicators: Dict[str, float]) -> float:
        """Calculate weighted confidence score."""
        total_score = 0.0
        
        for indicator, value in indicators.items():
            weight = self.confidence_weights.get(indicator, 0.0)
            total_score += weight * value
        
        return min(max(total_score, 0.0), 1.0)
    
    def _assess_manipulation_risk(self, sentiment_analysis: Dict, volume_analysis: Dict) -> float:
        """Assess risk of sentiment manipulation."""
        # High volume with extreme sentiment might indicate manipulation
        sentiment_strength = sentiment_analysis.get('strength', 0.0)
        volume_change = volume_analysis.get('volume_change', 0.0)
        sufficient_mentions = volume_analysis.get('sufficient_mentions', False)
        
        if not sufficient_mentions:
            return 0.9  # High risk if sample size is too small
        
        if sentiment_strength > 0.9 and volume_change > 10:
            return 0.8  # High risk: extreme sentiment with massive volume spike
        elif sentiment_strength > 0.7 and volume_change > 5:
            return 0.5  # Medium risk
        else:
            return 0.2  # Low risk
    
    def _assess_social_data_quality(self, social_data: Dict[str, Any]) -> float:
        """Assess quality of social media data."""
        quality_score = 0.0
        checks = 0
        
        # Check for required fields
        required_fields = ['current_sentiment', 'current_mentions', 'baseline_sentiment', 'baseline_mentions']
        for field in required_fields:
            checks += 1
            if field in social_data and social_data[field] is not None:
                quality_score += 1
        
        # Check for historical data
        checks += 1
        if 'sentiment_history' in social_data and len(social_data['sentiment_history']) >= 3:
            quality_score += 1
        
        checks += 1
        if 'mention_history' in social_data and len(social_data['mention_history']) >= 3:
            quality_score += 1
        
        return quality_score / checks if checks > 0 else 0.0
    
    def _assess_sample_size(self, social_data: Dict[str, Any]) -> float:
        """Assess adequacy of sample size."""
        current_mentions = social_data.get('current_mentions', 0)
        
        if current_mentions >= 100:
            return 1.0  # Excellent sample size
        elif current_mentions >= 50:
            return 0.8  # Good sample size
        elif current_mentions >= 20:
            return 0.6  # Adequate sample size
        elif current_mentions >= 10:
            return 0.4  # Marginal sample size
        else:
            return 0.1  # Poor sample size
