"""
CEX Listing Pattern Detector for Quantum Market Intelligence.
Detects potential centralized exchange listing patterns using volume spikes,
price movements, and social sentiment indicators.
"""

import logging
import statistics
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from uuid import uuid4

from app.models.pattern_detection import (
    PatternType, PatternStatus, PatternDetectionResult,
    CEXListingPatternData, PatternDetectorConfig
)
from app.services.pattern_detection import BasePatternDetector

logger = logging.getLogger(__name__)


class CEXListingDetector(BasePatternDetector):
    """
    Detector for CEX (Centralized Exchange) listing patterns.
    
    Detection Criteria:
    1. Volume Spike: 3x+ increase in trading volume
    2. Price Movement: Significant price increase (10%+ in 24h)
    3. Social Sentiment: Increased mentions and positive sentiment
    4. Time Pattern: Sustained activity over 2-6 hours
    5. Exchange Announcements: Detection of official announcements
    """
    
    def __init__(self, config: PatternDetectorConfig):
        super().__init__(config)
        
        # Detection thresholds (adjusted based on validation testing)
        self.volume_spike_threshold = 2.0  # 2x normal volume (reduced from 3.0)
        self.price_movement_threshold = 0.05  # 5% price increase (reduced from 10%)
        self.social_spike_threshold = 2.0  # 2x normal social mentions
        self.min_data_points = 12  # Minimum data points for analysis
        self.pattern_duration_hours = 6  # Maximum pattern duration
        
        # Confidence weights
        self.confidence_weights = {
            'volume_spike': 0.35,
            'price_movement': 0.25,
            'social_sentiment': 0.20,
            'time_consistency': 0.15,
            'announcement_detected': 0.05
        }
    
    def get_pattern_type(self) -> PatternType:
        """Return the pattern type this detector handles."""
        return PatternType.CEX_LISTING
    
    async def detect_patterns(
        self, 
        symbol: str, 
        market_data: List[Dict[str, Any]], 
        context_data: Optional[Dict[str, Any]] = None
    ) -> List[PatternDetectionResult]:
        """
        Detect CEX listing patterns in market data.
        
        Args:
            symbol: The symbol to analyze
            market_data: List of market data points (sorted by timestamp DESC)
            context_data: Additional context data (social, news, etc.)
            
        Returns:
            List of detected CEX listing patterns
        """
        if len(market_data) < self.min_data_points:
            logger.debug(f"Insufficient data for {symbol}: {len(market_data)} points")
            return []
        
        try:
            logger.debug(f"Analyzing CEX listing patterns for {symbol} with {len(market_data)} data points")
            
            # Prepare data for analysis
            sorted_data = sorted(market_data, key=lambda x: x['timestamp'])
            
            # Calculate baseline metrics (older data)
            baseline_data = sorted_data[:-12] if len(sorted_data) > 24 else sorted_data[:len(sorted_data)//2]
            recent_data = sorted_data[-12:]  # Last 12 data points
            
            if not baseline_data or not recent_data:
                return []
            
            # Analyze volume patterns
            volume_analysis = self._analyze_volume_patterns(baseline_data, recent_data)
            
            # Analyze price movements
            price_analysis = self._analyze_price_movements(recent_data)
            
            # Analyze social sentiment (if available)
            social_analysis = self._analyze_social_sentiment(context_data)
            
            # Analyze time consistency
            time_analysis = self._analyze_time_consistency(recent_data)
            
            # Check for exchange announcements
            announcement_analysis = self._check_exchange_announcements(context_data)
            
            # Calculate overall confidence
            indicators = {
                'volume_spike': volume_analysis['spike_ratio'],
                'price_movement': price_analysis['movement_strength'],
                'social_sentiment': social_analysis['sentiment_spike'],
                'time_consistency': time_analysis['consistency_score'],
                'announcement_detected': announcement_analysis['detected']
            }
            
            confidence_score = self._calculate_weighted_confidence(indicators)
            
            # Check if pattern meets minimum confidence threshold
            if confidence_score < self.config.confidence_threshold:
                logger.debug(f"CEX listing pattern for {symbol} below threshold: {confidence_score:.3f}")
                return []
            
            # Create pattern detection result
            pattern_data = CEXListingPatternData(
                volume_spike_ratio=volume_analysis['spike_ratio'],
                price_movement_24h=price_analysis['price_change_24h'],
                social_mentions_spike=social_analysis['mention_spike'],
                exchange_announcement_detected=announcement_analysis['detected'],
                listing_probability=confidence_score
            )
            
            # Determine expiry time (patterns typically resolve within 24-48 hours)
            detection_time = datetime.now(timezone.utc)
            expiry_time = detection_time + timedelta(hours=48)
            
            pattern_result = PatternDetectionResult(
                pattern_id=str(uuid4()),
                pattern_type=PatternType.CEX_LISTING,
                symbol=symbol,
                confidence_score=confidence_score,
                status=PatternStatus.DETECTED,
                detection_timestamp=detection_time,
                expiry_timestamp=expiry_time,
                pattern_data=pattern_data.dict(),
                trigger_conditions={
                    'volume_spike_detected': volume_analysis['spike_detected'],
                    'price_movement_significant': price_analysis['significant_movement'],
                    'social_activity_increased': social_analysis['activity_increased'],
                    'time_pattern_consistent': time_analysis['consistent']
                },
                supporting_indicators={
                    'avg_volume_baseline': volume_analysis['baseline_avg'],
                    'recent_volume_avg': volume_analysis['recent_avg'],
                    'price_volatility': price_analysis['volatility'],
                    'social_sentiment_score': social_analysis['sentiment_score']
                },
                risk_factors={
                    'false_positive_risk': self._calculate_false_positive_risk(indicators),
                    'market_manipulation_risk': self._assess_manipulation_risk(volume_analysis, price_analysis),
                    'data_quality_score': self._assess_data_quality(market_data)
                },
                detector_version=self.version
            )
            
            logger.info(f"CEX listing pattern detected for {symbol}: confidence={confidence_score:.3f}")
            return [pattern_result]
            
        except Exception as e:
            logger.error(f"Error detecting CEX listing patterns for {symbol}: {e}")
            return []
    
    def _analyze_volume_patterns(self, baseline_data: List[Dict], recent_data: List[Dict]) -> Dict[str, Any]:
        """Analyze volume patterns for spike detection."""
        try:
            # Calculate baseline volume statistics
            baseline_volumes = [float(d['volume']) for d in baseline_data if d.get('volume', 0) > 0]
            recent_volumes = [float(d['volume']) for d in recent_data if d.get('volume', 0) > 0]
            
            if not baseline_volumes or not recent_volumes:
                return {
                    'spike_detected': False,
                    'spike_ratio': 0.0,
                    'baseline_avg': 0.0,
                    'recent_avg': 0.0
                }
            
            baseline_avg = statistics.mean(baseline_volumes)
            recent_avg = statistics.mean(recent_volumes)
            
            # Calculate spike ratio
            spike_ratio = recent_avg / baseline_avg if baseline_avg > 0 else 0.0
            spike_detected = spike_ratio >= self.volume_spike_threshold
            
            return {
                'spike_detected': spike_detected,
                'spike_ratio': min(spike_ratio / self.volume_spike_threshold, 1.0),  # Normalize to 0-1
                'baseline_avg': baseline_avg,
                'recent_avg': recent_avg
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume patterns: {e}")
            return {'spike_detected': False, 'spike_ratio': 0.0, 'baseline_avg': 0.0, 'recent_avg': 0.0}
    
    def _analyze_price_movements(self, recent_data: List[Dict]) -> Dict[str, Any]:
        """Analyze price movements for significant changes."""
        try:
            if len(recent_data) < 2:
                return {
                    'significant_movement': False,
                    'movement_strength': 0.0,
                    'price_change_24h': 0.0,
                    'volatility': 0.0
                }
            
            prices = [float(d['price']) for d in recent_data]
            
            # Calculate 24h price change
            start_price = prices[0]
            end_price = prices[-1]
            price_change_24h = (end_price - start_price) / start_price if start_price > 0 else 0.0
            
            # Calculate volatility
            price_changes = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] > 0]
            volatility = statistics.stdev(price_changes) if len(price_changes) > 1 else 0.0
            
            # Determine if movement is significant
            significant_movement = abs(price_change_24h) >= self.price_movement_threshold
            movement_strength = min(abs(price_change_24h) / self.price_movement_threshold, 1.0)
            
            return {
                'significant_movement': significant_movement,
                'movement_strength': movement_strength,
                'price_change_24h': price_change_24h,
                'volatility': volatility
            }
            
        except Exception as e:
            logger.error(f"Error analyzing price movements: {e}")
            return {'significant_movement': False, 'movement_strength': 0.0, 'price_change_24h': 0.0, 'volatility': 0.0}
    
    def _analyze_social_sentiment(self, context_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze social sentiment indicators."""
        # Placeholder for social sentiment analysis
        # This would integrate with Reddit API, Twitter API, etc.
        
        default_result = {
            'activity_increased': False,
            'sentiment_spike': 0.0,
            'mention_spike': 0.0,
            'sentiment_score': 0.0
        }
        
        if not context_data or 'social' not in context_data:
            return default_result
        
        try:
            social_data = context_data['social']
            
            # Analyze mention volume
            current_mentions = social_data.get('current_mentions', 0)
            baseline_mentions = social_data.get('baseline_mentions', 1)
            mention_spike = current_mentions / baseline_mentions if baseline_mentions > 0 else 0.0
            
            # Analyze sentiment
            sentiment_score = social_data.get('sentiment_score', 0.0)  # -1 to 1
            
            # Determine if activity increased
            activity_increased = mention_spike >= self.social_spike_threshold
            sentiment_spike_normalized = min(mention_spike / self.social_spike_threshold, 1.0)
            
            return {
                'activity_increased': activity_increased,
                'sentiment_spike': sentiment_spike_normalized,
                'mention_spike': mention_spike,
                'sentiment_score': sentiment_score
            }
            
        except Exception as e:
            logger.error(f"Error analyzing social sentiment: {e}")
            return default_result
    
    def _analyze_time_consistency(self, recent_data: List[Dict]) -> Dict[str, Any]:
        """Analyze time consistency of the pattern."""
        try:
            if len(recent_data) < 3:
                return {'consistent': False, 'consistency_score': 0.0}
            
            # Check if activity is sustained over time
            timestamps = [d['timestamp'] for d in recent_data]
            volumes = [float(d['volume']) for d in recent_data if d.get('volume', 0) > 0]
            
            if not volumes:
                return {'consistent': False, 'consistency_score': 0.0}
            
            # Calculate time span
            time_span = (timestamps[-1] - timestamps[0]).total_seconds() / 3600  # hours
            
            # Check volume consistency (should be elevated throughout)
            avg_volume = statistics.mean(volumes)
            volume_consistency = sum(1 for v in volumes if v >= avg_volume * 0.7) / len(volumes)
            
            # Pattern should be sustained but not too long (2-6 hours ideal)
            time_score = 1.0 if 2 <= time_span <= 6 else max(0.5, 1.0 - abs(time_span - 4) / 10)
            
            consistency_score = (volume_consistency + time_score) / 2
            consistent = consistency_score >= 0.6
            
            return {
                'consistent': consistent,
                'consistency_score': consistency_score
            }
            
        except Exception as e:
            logger.error(f"Error analyzing time consistency: {e}")
            return {'consistent': False, 'consistency_score': 0.0}
    
    def _check_exchange_announcements(self, context_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Check for exchange announcement indicators."""
        # Placeholder for announcement detection
        # This would integrate with news APIs, exchange APIs, etc.
        
        if not context_data or 'announcements' not in context_data:
            return {'detected': 0.0}
        
        try:
            announcements = context_data['announcements']
            
            # Check for listing-related keywords
            listing_keywords = ['listing', 'list', 'trading', 'available', 'support']
            announcement_score = 0.0
            
            for announcement in announcements:
                text = announcement.get('text', '').lower()
                if any(keyword in text for keyword in listing_keywords):
                    announcement_score = 1.0
                    break
            
            return {'detected': announcement_score}
            
        except Exception as e:
            logger.error(f"Error checking exchange announcements: {e}")
            return {'detected': 0.0}
    
    def _calculate_weighted_confidence(self, indicators: Dict[str, float]) -> float:
        """Calculate weighted confidence score."""
        total_score = 0.0
        
        for indicator, value in indicators.items():
            weight = self.confidence_weights.get(indicator, 0.0)
            total_score += weight * value
        
        return min(max(total_score, 0.0), 1.0)
    
    def _calculate_false_positive_risk(self, indicators: Dict[str, float]) -> float:
        """Calculate false positive risk."""
        # Higher risk if only one indicator is strong
        strong_indicators = sum(1 for v in indicators.values() if v > 0.7)
        total_indicators = len(indicators)
        
        if strong_indicators <= 1:
            return 0.8  # High risk
        elif strong_indicators >= 3:
            return 0.2  # Low risk
        else:
            return 0.5  # Medium risk
    
    def _assess_manipulation_risk(self, volume_analysis: Dict, price_analysis: Dict) -> float:
        """Assess market manipulation risk."""
        # Very high volume with extreme price movement might indicate manipulation
        volume_ratio = volume_analysis.get('spike_ratio', 0.0)
        price_change = abs(price_analysis.get('price_change_24h', 0.0))
        
        if volume_ratio > 10 and price_change > 0.5:  # 10x volume + 50% price change
            return 0.9  # High manipulation risk
        elif volume_ratio > 5 and price_change > 0.3:
            return 0.6  # Medium risk
        else:
            return 0.2  # Low risk
    
    def _assess_data_quality(self, market_data: List[Dict]) -> float:
        """Assess quality of market data."""
        if not market_data:
            return 0.0
        
        # Check for missing data, outliers, etc.
        valid_points = 0
        total_points = len(market_data)
        
        for data_point in market_data:
            if (data_point.get('price', 0) > 0 and 
                data_point.get('volume', 0) >= 0 and 
                data_point.get('timestamp')):
                valid_points += 1
        
        return valid_points / total_points if total_points > 0 else 0.0
