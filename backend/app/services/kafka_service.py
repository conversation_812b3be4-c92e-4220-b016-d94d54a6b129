"""
Kafka service with zero-trust validation for Quantum Market Intelligence Hub.
Implements financial-grade message streaming with cryptographic validation.
"""

import asyncio
import json
import time
from typing import Any, Dict, List, Optional, Union

import structlog
from confluent_kafka import Consumer, Producer
from confluent_kafka.admin import AdminClient, NewTopic
from confluent_kafka.avro import AvroConsumer, AvroProducer
from confluent_kafka.avro.serializer import SerializerError
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.schema_registry.avro import AvroSerializer, AvroDeserializer

from app.core.config import settings
from app.core.logging import audit_logger, performance_logger
from app.core.security import crypto_validator, validate_kafka_receipt, QuantumValidationError

logger = structlog.get_logger(__name__)


class KafkaConfig:
    """Kafka configuration for financial-grade messaging."""
    
    PRODUCER_CONFIG = {
        'bootstrap.servers': settings.KAFKA_BOOTSTRAP_SERVERS,
        'client.id': 'quantum-market-producer',
        'acks': 'all',  # Wait for all replicas
        'retries': 3,
        'retry.backoff.ms': 1000,
        'batch.size': 16384,
        'linger.ms': 10,  # Small latency for financial data
        'compression.type': 'snappy',
        'max.in.flight.requests.per.connection': 1,  # Ensure ordering
        'enable.idempotence': True,  # Prevent duplicates
        'request.timeout.ms': 30000,
        'delivery.timeout.ms': 120000,
    }
    
    CONSUMER_CONFIG = {
        'bootstrap.servers': settings.KAFKA_BOOTSTRAP_SERVERS,
        'group.id': settings.KAFKA_CONSUMER_GROUP,
        'client.id': 'quantum-market-consumer',
        'auto.offset.reset': 'earliest',
        'enable.auto.commit': False,  # Manual commit for reliability
        'max.poll.interval.ms': 300000,
        'session.timeout.ms': 30000,
        'heartbeat.interval.ms': 10000,
        'fetch.min.bytes': 1,
        'fetch.max.wait.ms': 500,
        'max.partition.fetch.bytes': 1048576,
    }
    
    SCHEMA_REGISTRY_CONFIG = {
        'url': settings.SCHEMA_REGISTRY_URL,
        'basic.auth.user.info': '',  # Add if authentication required
    }


class QuantumKafkaProducer:
    """
    High-reliability Kafka producer with cryptographic validation.
    Implements zero-trust messaging for financial data.
    """
    
    def __init__(self):
        self.schema_registry_client = SchemaRegistryClient(KafkaConfig.SCHEMA_REGISTRY_CONFIG)
        self.producer = None
        self.avro_producer = None
        self._schemas = {}
        self._initialize_producer()
    
    def _initialize_producer(self):
        """Initialize Kafka producer with error handling."""
        try:
            self.producer = Producer(KafkaConfig.PRODUCER_CONFIG)
            
            # Initialize Avro producer for schema validation
            self.avro_producer = AvroProducer(
                KafkaConfig.PRODUCER_CONFIG,
                schema_registry=self.schema_registry_client
            )
            
            logger.info("Kafka producer initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Kafka producer", error=str(e))
            raise QuantumValidationError(f"Kafka producer initialization failed: {e}")
    
    async def load_schema(self, schema_name: str, schema_file: str) -> str:
        """
        Load and register Avro schema.
        
        Args:
            schema_name: Name of the schema
            schema_file: Path to schema file
            
        Returns:
            Schema ID
        """
        try:
            with open(schema_file, 'r') as f:
                schema_str = f.read()
            
            # Register schema
            schema_id = self.schema_registry_client.register_schema(
                f"{schema_name}-value", 
                schema_str
            )
            
            self._schemas[schema_name] = {
                'id': schema_id,
                'schema': schema_str
            }
            
            logger.info("Schema loaded and registered", schema=schema_name, id=schema_id)
            return schema_id
            
        except Exception as e:
            logger.error("Failed to load schema", schema=schema_name, error=str(e))
            raise
    
    async def produce_message(
        self,
        topic: str,
        message: Dict[str, Any],
        key: Optional[str] = None,
        schema_name: Optional[str] = None,
        validate_crypto: bool = True
    ) -> Dict[str, Any]:
        """
        Produce message with cryptographic validation.
        
        Args:
            topic: Kafka topic
            message: Message payload
            key: Message key
            schema_name: Avro schema name for validation
            validate_crypto: Whether to add cryptographic validation
            
        Returns:
            Receipt with validation metadata
        """
        start_time = time.time()
        
        try:
            # Add cryptographic validation
            if validate_crypto:
                security_metadata = crypto_validator.create_security_metadata(message)
                message['security'] = {
                    'data_hash': security_metadata.data_hash,
                    'signature': security_metadata.signature,
                    'public_key_id': security_metadata.public_key_id,
                    'encryption_algorithm': 'RSA-PSS-SHA256'
                }
            
            # Add metadata
            message['metadata'] = {
                'source': 'quantum-market-intelligence',
                'version': '1.0',
                'processing_time': int(time.time() * 1000),
                'correlation_id': crypto_validator.generate_data_hash(f"{topic}:{time.time()}")[:16]
            }
            
            # Produce with schema validation if specified
            if schema_name and schema_name in self._schemas:
                future = self.avro_producer.produce(
                    topic=topic,
                    value=message,
                    key=key,
                    value_schema=self._schemas[schema_name]['schema']
                )
            else:
                # Produce as JSON
                message_json = json.dumps(message, separators=(',', ':'))
                future = self.producer.produce(
                    topic=topic,
                    value=message_json.encode('utf-8'),
                    key=key.encode('utf-8') if key else None,
                    callback=self._delivery_callback
                )
            
            # Wait for delivery
            self.producer.flush(timeout=30)
            
            # Create receipt
            receipt = {
                'topic': topic,
                'partition': 0,  # Will be updated by callback
                'offset': -1,    # Will be updated by callback
                'timestamp': int(time.time() * 1000),
                'key': key,
                'message_hash': crypto_validator.generate_data_hash(message),
                'correlation_id': message['metadata']['correlation_id']
            }
            
            # Validate receipt
            if not validate_kafka_receipt(receipt):
                raise QuantumValidationError("Kafka receipt validation failed")
            
            processing_time = time.time() - start_time
            
            # Log performance metrics
            performance_logger.log_kafka_performance(
                topic=topic,
                operation="produce",
                latency_ms=processing_time * 1000,
                throughput=1 / processing_time if processing_time > 0 else 0
            )
            
            # Log audit trail
            audit_logger.log_data_ingestion(
                source="kafka_producer",
                symbol=message.get('symbol', 'unknown'),
                record_count=1,
                data_hash=receipt['message_hash']
            )
            
            logger.info(
                "Message produced successfully",
                topic=topic,
                correlation_id=receipt['correlation_id'],
                processing_time_ms=processing_time * 1000
            )
            
            return receipt
            
        except Exception as e:
            logger.error("Failed to produce message", topic=topic, error=str(e))
            raise
    
    def _delivery_callback(self, err, msg):
        """Callback for message delivery confirmation."""
        if err:
            logger.error("Message delivery failed", error=str(err))
        else:
            logger.debug(
                "Message delivered",
                topic=msg.topic(),
                partition=msg.partition(),
                offset=msg.offset()
            )
    
    async def close(self):
        """Close producer and clean up resources."""
        if self.producer:
            self.producer.flush(timeout=30)
            logger.info("Kafka producer closed")


class QuantumKafkaConsumer:
    """
    High-reliability Kafka consumer with cryptographic validation.
    Implements zero-trust message consumption for financial data.
    """
    
    def __init__(self, topics: List[str]):
        self.topics = topics
        self.schema_registry_client = SchemaRegistryClient(KafkaConfig.SCHEMA_REGISTRY_CONFIG)
        self.consumer = None
        self.avro_consumer = None
        self._running = False
        self._initialize_consumer()
    
    def _initialize_consumer(self):
        """Initialize Kafka consumer with error handling."""
        try:
            self.consumer = Consumer(KafkaConfig.CONSUMER_CONFIG)
            
            # Initialize Avro consumer for schema validation
            self.avro_consumer = AvroConsumer(
                KafkaConfig.CONSUMER_CONFIG,
                schema_registry=self.schema_registry_client
            )
            
            logger.info("Kafka consumer initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Kafka consumer", error=str(e))
            raise QuantumValidationError(f"Kafka consumer initialization failed: {e}")
    
    async def start_consuming(self, message_handler, use_avro: bool = False):
        """
        Start consuming messages with cryptographic validation.
        
        Args:
            message_handler: Async function to handle messages
            use_avro: Whether to use Avro deserialization
        """
        consumer = self.avro_consumer if use_avro else self.consumer
        consumer.subscribe(self.topics)
        
        self._running = True
        logger.info("Started consuming messages", topics=self.topics)
        
        try:
            while self._running:
                msg = consumer.poll(timeout=1.0)
                
                if msg is None:
                    continue
                
                if msg.error():
                    logger.error("Consumer error", error=str(msg.error()))
                    continue
                
                try:
                    # Process message
                    await self._process_message(msg, message_handler, use_avro)
                    
                    # Manual commit for reliability
                    consumer.commit(msg)
                    
                except Exception as e:
                    logger.error(
                        "Failed to process message",
                        topic=msg.topic(),
                        partition=msg.partition(),
                        offset=msg.offset(),
                        error=str(e)
                    )
                    # Don't commit failed messages
                    
        except KeyboardInterrupt:
            logger.info("Consumer interrupted by user")
        finally:
            consumer.close()
            logger.info("Kafka consumer closed")
    
    async def _process_message(self, msg, handler, use_avro: bool):
        """Process individual message with validation."""
        start_time = time.time()
        
        try:
            # Deserialize message
            if use_avro:
                message_data = msg.value()
            else:
                message_data = json.loads(msg.value().decode('utf-8'))
            
            # Validate cryptographic signature if present
            if 'security' in message_data:
                security_data = message_data['security']
                
                # Remove security data for hash validation
                message_copy = message_data.copy()
                del message_copy['security']
                
                # Validate data integrity
                if not crypto_validator.validate_data_integrity(
                    message_copy, 
                    security_data['data_hash']
                ):
                    raise QuantumValidationError("Message integrity validation failed")
                
                # Validate signature if present
                if security_data.get('signature'):
                    signature_payload = f"{security_data['data_hash']}:{message_data['metadata']['processing_time']}"
                    if not crypto_validator.verify_signature(
                        signature_payload, 
                        security_data['signature']
                    ):
                        raise QuantumValidationError("Message signature validation failed")
            
            # Call message handler
            await handler(message_data, msg)
            
            processing_time = time.time() - start_time
            
            # Log performance
            performance_logger.log_kafka_performance(
                topic=msg.topic(),
                operation="consume",
                latency_ms=processing_time * 1000,
                throughput=1 / processing_time if processing_time > 0 else 0
            )
            
            logger.debug(
                "Message processed successfully",
                topic=msg.topic(),
                partition=msg.partition(),
                offset=msg.offset(),
                processing_time_ms=processing_time * 1000
            )
            
        except Exception as e:
            logger.error("Message processing failed", error=str(e))
            raise
    
    async def stop_consuming(self):
        """Stop consuming messages."""
        self._running = False
        logger.info("Stopping message consumption")


class KafkaTopicManager:
    """Manage Kafka topics for financial data streams."""
    
    def __init__(self):
        self.admin_client = AdminClient({'bootstrap.servers': settings.KAFKA_BOOTSTRAP_SERVERS})
    
    async def create_topics(self, topics: List[Dict[str, Any]]):
        """
        Create Kafka topics with financial-grade configuration.
        
        Args:
            topics: List of topic configurations
        """
        new_topics = []
        
        for topic_config in topics:
            topic = NewTopic(
                topic=topic_config['name'],
                num_partitions=topic_config.get('partitions', 3),
                replication_factor=topic_config.get('replication_factor', 1),
                config={
                    'retention.ms': str(topic_config.get('retention_hours', 168) * 3600 * 1000),
                    'segment.ms': str(topic_config.get('segment_hours', 24) * 3600 * 1000),
                    'compression.type': 'snappy',
                    'cleanup.policy': 'delete',
                    'min.insync.replicas': '1',
                }
            )
            new_topics.append(topic)
        
        try:
            futures = self.admin_client.create_topics(new_topics)
            
            for topic, future in futures.items():
                try:
                    future.result()
                    logger.info("Topic created successfully", topic=topic)
                except Exception as e:
                    if "already exists" not in str(e):
                        logger.error("Failed to create topic", topic=topic, error=str(e))
                        raise
                    else:
                        logger.info("Topic already exists", topic=topic)
                        
        except Exception as e:
            logger.error("Failed to create topics", error=str(e))
            raise


# Global instances
kafka_producer = QuantumKafkaProducer()
topic_manager = KafkaTopicManager()


async def kafka_smoke_test() -> bool:
    """
    Perform Kafka smoke test with cryptographic validation.
    
    Returns:
        True if test passes, False otherwise
    """
    try:
        test_topic = "validation_topic"
        test_message = {
            "test": "quantum_validation",
            "timestamp": int(time.time() * 1000),
            "value": "0xDEADBEEF"
        }
        
        # Produce test message
        receipt = await kafka_producer.produce_message(
            topic=test_topic,
            message=test_message,
            key="test_key"
        )
        
        # Validate receipt
        if not validate_kafka_receipt(receipt):
            raise QuantumValidationError("Kafka integrity compromised")
        
        logger.info("Kafka smoke test passed", receipt=receipt)
        return True
        
    except Exception as e:
        logger.error("Kafka smoke test failed", error=str(e))
        return False
