"""
Pattern Detection Service for Quantum Market Intelligence.
Core service for detecting market patterns with high accuracy and real-time analysis.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Type
from uuid import uuid4

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.db.deps import get_db_session
from app.models.pattern_detection import (
    PatternType, PatternConfidence, PatternStatus,
    PatternDetectionRequest, PatternDetectionResult, PatternAnalysisResponse,
    PatternMonitoringStatus, DetectedPattern, PatternAlert,
    PatternDetectorConfig, PatternDetectionSystemConfig
)

logger = logging.getLogger(__name__)


# ============================================================================
# ABSTRACT PATTERN DETECTOR BASE CLASS
# ============================================================================

class BasePatternDetector(ABC):
    """Abstract base class for all pattern detectors."""
    
    def __init__(self, config: PatternDetectorConfig):
        self.config = config
        self.pattern_type = self.get_pattern_type()
        self.version = "1.0.0"
        
    @abstractmethod
    def get_pattern_type(self) -> PatternType:
        """Return the pattern type this detector handles."""
        pass
    
    @abstractmethod
    async def detect_patterns(
        self, 
        symbol: str, 
        market_data: List[Dict[str, Any]], 
        context_data: Optional[Dict[str, Any]] = None
    ) -> List[PatternDetectionResult]:
        """
        Detect patterns in the provided market data.
        
        Args:
            symbol: The symbol to analyze
            market_data: List of market data points
            context_data: Additional context data (social, news, etc.)
            
        Returns:
            List of detected patterns
        """
        pass
    
    def calculate_confidence(self, indicators: Dict[str, float]) -> float:
        """Calculate confidence score based on indicators."""
        # Default implementation - can be overridden
        weights = {
            'strength': 0.4,
            'consistency': 0.3,
            'volume_confirmation': 0.2,
            'time_factor': 0.1
        }
        
        score = 0.0
        for indicator, value in indicators.items():
            if indicator in weights:
                score += weights[indicator] * value
        
        return min(max(score, 0.0), 1.0)
    
    def get_confidence_level(self, score: float) -> PatternConfidence:
        """Convert confidence score to confidence level."""
        if score >= 0.85:
            return PatternConfidence.VERY_HIGH
        elif score >= 0.7:
            return PatternConfidence.HIGH
        elif score >= 0.5:
            return PatternConfidence.MEDIUM
        else:
            return PatternConfidence.LOW


# ============================================================================
# PATTERN DETECTION REGISTRY
# ============================================================================

class PatternDetectorRegistry:
    """Registry for managing pattern detectors."""
    
    def __init__(self):
        self._detectors: Dict[PatternType, BasePatternDetector] = {}
        self._detector_stats: Dict[PatternType, Dict[str, Any]] = {}
    
    def register_detector(self, detector: BasePatternDetector):
        """Register a pattern detector."""
        pattern_type = detector.get_pattern_type()
        self._detectors[pattern_type] = detector
        self._detector_stats[pattern_type] = {
            'detections': 0,
            'accuracy': 0.0,
            'last_detection': None,
            'avg_confidence': 0.0
        }
        logger.info(f"Registered detector for {pattern_type.value}")
    
    def get_detector(self, pattern_type: PatternType) -> Optional[BasePatternDetector]:
        """Get detector for specific pattern type."""
        return self._detectors.get(pattern_type)
    
    def get_all_detectors(self) -> Dict[PatternType, BasePatternDetector]:
        """Get all registered detectors."""
        return self._detectors.copy()
    
    def update_detector_stats(self, pattern_type: PatternType, confidence: float):
        """Update detector statistics."""
        if pattern_type in self._detector_stats:
            stats = self._detector_stats[pattern_type]
            stats['detections'] += 1
            stats['last_detection'] = datetime.now(timezone.utc)
            
            # Update average confidence
            current_avg = stats['avg_confidence']
            count = stats['detections']
            stats['avg_confidence'] = ((current_avg * (count - 1)) + confidence) / count
    
    def get_detector_stats(self) -> Dict[PatternType, Dict[str, Any]]:
        """Get detector statistics."""
        return self._detector_stats.copy()


# ============================================================================
# MAIN PATTERN DETECTION SERVICE
# ============================================================================

class PatternDetectionService:
    """Main service for pattern detection and analysis."""
    
    def __init__(self):
        self.registry = PatternDetectorRegistry()
        self.config = PatternDetectionSystemConfig()
        self.db_session: Optional[AsyncSession] = None
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.detection_stats = {
            'total_analyses': 0,
            'total_patterns_detected': 0,
            'avg_analysis_time_ms': 0.0,
            'accuracy_rate': 0.0
        }
    
    async def start(self):
        """Start the pattern detection service."""
        try:
            self.db_session = await get_db_session()
            logger.info("Pattern detection service started")
            
            # Initialize detectors
            await self._initialize_detectors()
            
        except Exception as e:
            logger.error(f"Failed to start pattern detection service: {e}")
            raise
    
    async def stop(self):
        """Stop the pattern detection service."""
        try:
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            if self.db_session:
                await self.db_session.close()
            
            logger.info("Pattern detection service stopped")
            
        except Exception as e:
            logger.error(f"Error stopping pattern detection service: {e}")
    
    async def _initialize_detectors(self):
        """Initialize and register pattern detectors."""
        try:
            # Import detector classes
            from app.services.detectors.cex_listing_detector import CEXListingDetector
            from app.services.detectors.whale_accumulation_detector import WhaleAccumulationDetector
            from app.services.detectors.social_sentiment_detector import SocialSentimentDetector

            # Create detector configurations (adjusted based on validation testing)
            default_config = PatternDetectorConfig(
                enabled=True,
                confidence_threshold=0.6,  # Reduced from 0.7 based on validation results
                detection_interval_seconds=300,  # 5 minutes
                lookback_hours=24
            )

            # Initialize and register detectors
            cex_detector = CEXListingDetector(default_config)
            whale_detector = WhaleAccumulationDetector(default_config)
            sentiment_detector = SocialSentimentDetector(default_config)

            self.registry.register_detector(cex_detector)
            self.registry.register_detector(whale_detector)
            self.registry.register_detector(sentiment_detector)

            logger.info(f"Initialized {len(self.registry.get_all_detectors())} pattern detectors")

        except Exception as e:
            logger.error(f"Error initializing detectors: {e}")
            raise
    
    async def analyze_patterns(self, request: PatternDetectionRequest) -> PatternAnalysisResponse:
        """
        Analyze patterns for the given request.
        
        Args:
            request: Pattern detection request
            
        Returns:
            Pattern analysis response
        """
        start_time = time.time()
        request_id = str(uuid4())
        
        try:
            logger.info(f"Starting pattern analysis {request_id} for symbols: {request.symbols}")
            
            # Get market data for analysis
            market_data = await self._get_market_data(request.symbols, request.lookback_hours)
            
            # Detect patterns
            all_patterns = []
            data_points_analyzed = 0
            
            for symbol in request.symbols:
                symbol_data = market_data.get(symbol, [])
                data_points_analyzed += len(symbol_data)
                
                # Run pattern detection for each requested pattern type
                pattern_types = request.pattern_types or list(self.registry.get_all_detectors().keys())
                
                for pattern_type in pattern_types:
                    detector = self.registry.get_detector(pattern_type)
                    if detector and detector.config.enabled:
                        try:
                            patterns = await detector.detect_patterns(symbol, symbol_data)
                            
                            # Filter by confidence threshold
                            filtered_patterns = [
                                p for p in patterns 
                                if p.confidence_score >= request.min_confidence
                            ]
                            
                            all_patterns.extend(filtered_patterns)
                            
                            # Update detector stats
                            for pattern in filtered_patterns:
                                self.registry.update_detector_stats(
                                    pattern_type, 
                                    pattern.confidence_score
                                )
                            
                        except Exception as e:
                            logger.error(f"Error in {pattern_type.value} detector for {symbol}: {e}")
            
            # Store detected patterns in database
            await self._store_patterns(all_patterns)
            
            # Calculate analysis duration
            analysis_duration_ms = (time.time() - start_time) * 1000
            
            # Update service statistics
            self.detection_stats['total_analyses'] += 1
            self.detection_stats['total_patterns_detected'] += len(all_patterns)
            
            # Update average analysis time
            current_avg = self.detection_stats['avg_analysis_time_ms']
            count = self.detection_stats['total_analyses']
            self.detection_stats['avg_analysis_time_ms'] = (
                (current_avg * (count - 1)) + analysis_duration_ms
            ) / count
            
            # Create response
            pattern_distribution = {}
            high_confidence_count = 0
            
            for pattern in all_patterns:
                pattern_type = pattern.pattern_type
                pattern_distribution[pattern_type] = pattern_distribution.get(pattern_type, 0) + 1
                
                if pattern.confidence_score >= 0.8:
                    high_confidence_count += 1
            
            response = PatternAnalysisResponse(
                request_id=request_id,
                analysis_timestamp=datetime.now(timezone.utc),
                symbols_analyzed=request.symbols,
                patterns_detected=all_patterns,
                total_patterns=len(all_patterns),
                high_confidence_patterns=high_confidence_count,
                pattern_distribution=pattern_distribution,
                analysis_duration_ms=analysis_duration_ms,
                data_points_analyzed=data_points_analyzed
            )
            
            logger.info(f"Pattern analysis {request_id} completed: {len(all_patterns)} patterns detected")
            return response
            
        except Exception as e:
            logger.error(f"Pattern analysis {request_id} failed: {e}")
            raise
    
    async def _get_market_data(self, symbols: List[str], lookback_hours: int) -> Dict[str, List[Dict[str, Any]]]:
        """Get market data for pattern analysis."""
        if not self.db_session:
            raise RuntimeError("Database session not initialized")
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=lookback_hours)
        
        market_data = {}
        
        for symbol in symbols:
            try:
                query = text("""
                    SELECT timestamp, symbol, exchange, price, volume, market_cap, source, data_hash
                    FROM market_data 
                    WHERE symbol = :symbol 
                    AND timestamp >= :cutoff_time
                    ORDER BY timestamp DESC
                    LIMIT 1000
                """)
                
                result = await self.db_session.execute(
                    query,
                    {"symbol": symbol.upper(), "cutoff_time": cutoff_time}
                )
                
                rows = result.fetchall()
                
                symbol_data = []
                for row in rows:
                    symbol_data.append({
                        'timestamp': row.timestamp,
                        'symbol': row.symbol,
                        'exchange': row.exchange,
                        'price': float(row.price),
                        'volume': float(row.volume) if row.volume else 0.0,
                        'market_cap': float(row.market_cap) if row.market_cap else 0.0,
                        'source': row.source,
                        'data_hash': row.data_hash
                    })
                
                market_data[symbol] = symbol_data
                logger.debug(f"Retrieved {len(symbol_data)} data points for {symbol}")
                
            except Exception as e:
                logger.error(f"Error retrieving market data for {symbol}: {e}")
                market_data[symbol] = []
        
        return market_data
    
    async def _store_patterns(self, patterns: List[PatternDetectionResult]):
        """Store detected patterns in database."""
        if not self.db_session or not patterns:
            return
        
        try:
            for pattern in patterns:
                db_pattern = DetectedPattern(
                    id=pattern.pattern_id,
                    pattern_type=pattern.pattern_type.value,
                    symbol=pattern.symbol,
                    confidence_score=pattern.confidence_score,
                    status=pattern.status.value,
                    pattern_data=pattern.pattern_data,
                    detection_timestamp=pattern.detection_timestamp,
                    expiry_timestamp=pattern.expiry_timestamp,
                    trigger_conditions=pattern.trigger_conditions,
                    supporting_indicators=pattern.supporting_indicators,
                    risk_factors=pattern.risk_factors,
                    detector_version=pattern.detector_version
                )
                
                self.db_session.add(db_pattern)
            
            await self.db_session.commit()
            logger.info(f"Stored {len(patterns)} patterns in database")
            
        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"Error storing patterns: {e}")
            raise
    
    async def start_monitoring(self, symbols: List[str], interval_seconds: int = 300):
        """Start real-time pattern monitoring."""
        if self.monitoring_active:
            logger.warning("Pattern monitoring already active")
            return

        self.monitoring_active = True
        self.monitored_symbols = symbols
        self.monitoring_interval = interval_seconds

        # Start monitoring task
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info(f"Started pattern monitoring for {len(symbols)} symbols with {interval_seconds}s interval")

    async def stop_monitoring(self):
        """Stop real-time pattern monitoring."""
        self.monitoring_active = False

        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.monitoring_task = None

        logger.info("Stopped pattern monitoring")

    async def _monitoring_loop(self):
        """Main monitoring loop for real-time pattern detection."""
        logger.info("Pattern monitoring loop started")

        while self.monitoring_active:
            try:
                # Create monitoring request
                request = PatternDetectionRequest(
                    symbols=self.monitored_symbols,
                    pattern_types=None,  # Monitor all patterns
                    lookback_hours=6,  # Shorter lookback for real-time
                    min_confidence=0.7,
                    real_time=True
                )

                # Run pattern analysis
                start_time = time.time()
                response = await self.analyze_patterns(request)
                analysis_time = (time.time() - start_time) * 1000

                # Log monitoring results
                if response.patterns_detected:
                    logger.info(f"Monitoring cycle: {len(response.patterns_detected)} patterns detected in {analysis_time:.1f}ms")

                    # Generate alerts for high-confidence patterns
                    await self._generate_pattern_alerts(response.patterns_detected)
                else:
                    logger.debug(f"Monitoring cycle: No patterns detected in {analysis_time:.1f}ms")

                # Wait for next cycle
                await asyncio.sleep(self.monitoring_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

        logger.info("Pattern monitoring loop stopped")

    async def _generate_pattern_alerts(self, patterns: List[PatternDetectionResult]):
        """Generate alerts for detected patterns."""
        if not self.db_session:
            return

        try:
            for pattern in patterns:
                # Determine alert priority based on confidence
                if pattern.confidence_score >= 0.9:
                    priority = "critical"
                elif pattern.confidence_score >= 0.8:
                    priority = "high"
                elif pattern.confidence_score >= 0.7:
                    priority = "medium"
                else:
                    priority = "low"

                # Create alert message
                message = f"{pattern.pattern_type.value.replace('_', ' ').title()} pattern detected for {pattern.symbol} with {pattern.confidence_score:.1%} confidence"

                # Store alert in database
                alert = PatternAlert(
                    pattern_id=pattern.pattern_id,
                    alert_type=f"{pattern.pattern_type.value}_detection",
                    priority=priority,
                    message=message,
                    alert_data={
                        'pattern_type': pattern.pattern_type.value,
                        'symbol': pattern.symbol,
                        'confidence': pattern.confidence_score,
                        'detection_time': pattern.detection_timestamp.isoformat()
                    },
                    sent_timestamp=datetime.now(timezone.utc)
                )

                self.db_session.add(alert)

            await self.db_session.commit()
            logger.info(f"Generated {len(patterns)} pattern alerts")

        except Exception as e:
            await self.db_session.rollback()
            logger.error(f"Error generating pattern alerts: {e}")

    async def get_monitoring_status(self) -> PatternMonitoringStatus:
        """Get current monitoring status."""
        detector_stats = self.registry.get_detector_stats()

        # Calculate metrics
        total_detections = sum(stats['detections'] for stats in detector_stats.values())
        avg_accuracy = sum(stats['accuracy'] for stats in detector_stats.values()) / len(detector_stats) if detector_stats else 0.0

        last_detection = None
        for stats in detector_stats.values():
            if stats['last_detection']:
                if not last_detection or stats['last_detection'] > last_detection:
                    last_detection = stats['last_detection']

        # Calculate detection rate (patterns per hour)
        detection_rate = 0.0
        if hasattr(self, 'monitored_symbols') and self.monitoring_active:
            # Estimate based on recent activity
            detection_rate = total_detections / max(1, len(detector_stats)) * 6  # Rough estimate

        return PatternMonitoringStatus(
            service_active=self.monitoring_active,
            monitored_symbols=getattr(self, 'monitored_symbols', []),
            active_patterns=total_detections,
            detection_rate_per_hour=detection_rate,
            last_detection_timestamp=last_detection,
            avg_detection_latency_ms=self.detection_stats['avg_analysis_time_ms'],
            accuracy_rate=avg_accuracy,
            false_positive_rate=0.0  # Will be calculated based on verification
        )


# Global pattern detection service instance
pattern_detection_service = PatternDetectionService()
