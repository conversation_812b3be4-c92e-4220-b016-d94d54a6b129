"""
Production Monitoring Service for Quantum Market Intelligence.
Implements real-time pattern detection monitoring with performance tracking,
alert generation, and production-grade reliability features.
"""

import asyncio
import logging
import time
try:
    import psutil
except ImportError:
    psutil = None
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import json

from app.services.pattern_detection import pattern_detection_service
from app.models.pattern_detection import (
    PatternDetectionRequest, PatternType, PatternDetectionResult
)
from app.db.deps import get_db_session
from sqlalchemy import text

logger = logging.getLogger(__name__)


@dataclass
class MonitoringMetrics:
    """Production monitoring metrics."""
    timestamp: datetime
    symbols_monitored: int
    patterns_detected: int
    high_confidence_patterns: int
    avg_analysis_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    database_connections: int
    alerts_generated: int
    uptime_seconds: float
    detection_rate_per_hour: float
    error_count: int


@dataclass
class AlertConfig:
    """Alert configuration for different confidence levels."""
    confidence_threshold: float
    webhook_url: Optional[str] = None
    email_recipients: Optional[List[str]] = None
    slack_webhook: Optional[str] = None
    enabled: bool = True
    cooldown_minutes: int = 15  # Prevent spam


class ProductionMonitoringService:
    """
    Production-grade monitoring service for pattern detection.
    
    Features:
    - Real-time pattern monitoring with configurable intervals
    - Multi-level alert system (70%, 80%, 90% confidence thresholds)
    - Performance monitoring and resource tracking
    - Database health monitoring
    - Error handling and recovery
    - Metrics collection and reporting
    """
    
    def __init__(self):
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.metrics_task: Optional[asyncio.Task] = None
        self.start_time: Optional[datetime] = None
        
        # Monitoring configuration
        self.monitored_symbols = ["bitcoin", "ethereum", "solana", "cardano", "chainlink", "polygon"]
        self.monitoring_interval = 300  # 5 minutes
        self.metrics_interval = 60  # 1 minute for metrics collection
        
        # Alert configurations
        self.alert_configs = {
            "critical": AlertConfig(confidence_threshold=0.9, cooldown_minutes=5),
            "high": AlertConfig(confidence_threshold=0.8, cooldown_minutes=10),
            "medium": AlertConfig(confidence_threshold=0.7, cooldown_minutes=15)
        }
        
        # Performance tracking
        self.metrics_history: List[MonitoringMetrics] = []
        self.max_metrics_history = 1440  # 24 hours of minute-by-minute metrics
        self.alert_cooldowns: Dict[str, datetime] = {}
        
        # Error tracking
        self.error_count = 0
        self.last_error_time: Optional[datetime] = None
        
        # Statistics
        self.total_patterns_detected = 0
        self.total_alerts_generated = 0
        self.detection_cycles_completed = 0
    
    async def start_production_monitoring(self) -> Dict[str, Any]:
        """Start production monitoring with full feature set."""
        if self.monitoring_active:
            return {"status": "already_running", "message": "Production monitoring already active"}
        
        try:
            logger.info("Starting production monitoring service...")
            
            # Initialize pattern detection service
            await pattern_detection_service.start()
            
            # Start monitoring tasks
            self.monitoring_active = True
            self.start_time = datetime.now(timezone.utc)
            
            # Start main monitoring loop
            self.monitoring_task = asyncio.create_task(self._production_monitoring_loop())
            
            # Start metrics collection
            self.metrics_task = asyncio.create_task(self._metrics_collection_loop())
            
            # Collect initial data for better pattern detection
            await self._collect_initial_data()
            
            logger.info(f"Production monitoring started for {len(self.monitored_symbols)} symbols")
            
            return {
                "status": "started",
                "message": f"Production monitoring active for {len(self.monitored_symbols)} symbols",
                "symbols": self.monitored_symbols,
                "monitoring_interval": self.monitoring_interval,
                "alert_levels": list(self.alert_configs.keys()),
                "started_at": self.start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to start production monitoring: {e}")
            self.monitoring_active = False
            raise
    
    async def stop_production_monitoring(self) -> Dict[str, Any]:
        """Stop production monitoring and cleanup resources."""
        if not self.monitoring_active:
            return {"status": "not_running", "message": "Production monitoring not active"}
        
        try:
            logger.info("Stopping production monitoring service...")
            
            self.monitoring_active = False
            
            # Cancel monitoring tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            if self.metrics_task:
                self.metrics_task.cancel()
                try:
                    await self.metrics_task
                except asyncio.CancelledError:
                    pass
            
            # Stop pattern detection service
            await pattern_detection_service.stop()
            
            # Generate final report
            uptime = (datetime.now(timezone.utc) - self.start_time).total_seconds() if self.start_time else 0
            
            final_report = {
                "status": "stopped",
                "message": "Production monitoring stopped",
                "session_summary": {
                    "uptime_seconds": uptime,
                    "uptime_hours": uptime / 3600,
                    "total_patterns_detected": self.total_patterns_detected,
                    "total_alerts_generated": self.total_alerts_generated,
                    "detection_cycles_completed": self.detection_cycles_completed,
                    "error_count": self.error_count,
                    "avg_patterns_per_hour": (self.total_patterns_detected / (uptime / 3600)) if uptime > 0 else 0
                },
                "stopped_at": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"Production monitoring stopped. Session summary: {final_report['session_summary']}")
            return final_report
            
        except Exception as e:
            logger.error(f"Error stopping production monitoring: {e}")
            raise
    
    async def _production_monitoring_loop(self):
        """Main production monitoring loop."""
        logger.info("Production monitoring loop started")
        
        while self.monitoring_active:
            try:
                cycle_start_time = time.time()
                
                # Run pattern detection cycle
                patterns_detected = await self._run_detection_cycle()
                
                # Process alerts for detected patterns
                alerts_generated = await self._process_pattern_alerts(patterns_detected)
                
                # Update statistics
                self.total_patterns_detected += len(patterns_detected)
                self.total_alerts_generated += alerts_generated
                self.detection_cycles_completed += 1
                
                cycle_duration = time.time() - cycle_start_time
                
                logger.info(
                    f"Monitoring cycle completed: {len(patterns_detected)} patterns, "
                    f"{alerts_generated} alerts, {cycle_duration:.2f}s duration"
                )
                
                # Wait for next cycle
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.error_count += 1
                self.last_error_time = datetime.now(timezone.utc)
                logger.error(f"Error in monitoring loop: {e}")
                
                # Wait before retrying (exponential backoff)
                retry_delay = min(60, 10 * (self.error_count % 5))
                await asyncio.sleep(retry_delay)
        
        logger.info("Production monitoring loop stopped")
    
    async def _run_detection_cycle(self) -> List[PatternDetectionResult]:
        """Run a single pattern detection cycle."""
        try:
            # Create detection request
            request = PatternDetectionRequest(
                symbols=self.monitored_symbols,
                pattern_types=None,  # Monitor all pattern types
                lookback_hours=6,  # Shorter lookback for real-time monitoring
                min_confidence=0.6,  # Lower threshold to catch more patterns
                real_time=True
            )
            
            # Run pattern analysis
            response = await pattern_detection_service.analyze_patterns(request)
            
            return response.patterns_detected
            
        except Exception as e:
            logger.error(f"Error in detection cycle: {e}")
            return []
    
    async def _process_pattern_alerts(self, patterns: List[PatternDetectionResult]) -> int:
        """Process patterns and generate alerts based on confidence levels."""
        alerts_generated = 0
        
        for pattern in patterns:
            try:
                # Determine alert level based on confidence
                alert_level = self._get_alert_level(pattern.confidence_score)
                
                if alert_level and self._should_send_alert(pattern, alert_level):
                    await self._send_pattern_alert(pattern, alert_level)
                    alerts_generated += 1
                    
                    # Update cooldown
                    cooldown_key = f"{pattern.symbol}_{pattern.pattern_type.value}_{alert_level}"
                    self.alert_cooldowns[cooldown_key] = datetime.now(timezone.utc)
                    
            except Exception as e:
                logger.error(f"Error processing alert for pattern {pattern.pattern_id}: {e}")
        
        return alerts_generated
    
    def _get_alert_level(self, confidence_score: float) -> Optional[str]:
        """Determine alert level based on confidence score."""
        if confidence_score >= self.alert_configs["critical"].confidence_threshold:
            return "critical"
        elif confidence_score >= self.alert_configs["high"].confidence_threshold:
            return "high"
        elif confidence_score >= self.alert_configs["medium"].confidence_threshold:
            return "medium"
        return None
    
    def _should_send_alert(self, pattern: PatternDetectionResult, alert_level: str) -> bool:
        """Check if alert should be sent based on cooldown and configuration."""
        config = self.alert_configs[alert_level]
        
        if not config.enabled:
            return False
        
        # Check cooldown
        cooldown_key = f"{pattern.symbol}_{pattern.pattern_type.value}_{alert_level}"
        if cooldown_key in self.alert_cooldowns:
            last_alert = self.alert_cooldowns[cooldown_key]
            cooldown_period = timedelta(minutes=config.cooldown_minutes)
            if datetime.now(timezone.utc) - last_alert < cooldown_period:
                return False
        
        return True
    
    async def _send_pattern_alert(self, pattern: PatternDetectionResult, alert_level: str):
        """Send pattern alert through configured channels."""
        try:
            alert_data = {
                "alert_level": alert_level,
                "pattern_id": pattern.pattern_id,
                "pattern_type": pattern.pattern_type.value,
                "symbol": pattern.symbol,
                "confidence_score": pattern.confidence_score,
                "detection_timestamp": pattern.detection_timestamp.isoformat(),
                "trigger_conditions": pattern.trigger_conditions,
                "supporting_indicators": pattern.supporting_indicators,
                "risk_factors": pattern.risk_factors
            }
            
            # Create alert message
            message = self._create_alert_message(pattern, alert_level)
            
            # Log alert (always)
            logger.warning(f"PATTERN ALERT [{alert_level.upper()}]: {message}")
            
            # Store alert in database
            await self._store_alert_in_database(alert_data, message)
            
            # Send to external systems (webhook, email, Slack)
            await self._send_external_alerts(alert_data, message, alert_level)
            
        except Exception as e:
            logger.error(f"Error sending pattern alert: {e}")
    
    def _create_alert_message(self, pattern: PatternDetectionResult, alert_level: str) -> str:
        """Create human-readable alert message."""
        pattern_name = pattern.pattern_type.value.replace('_', ' ').title()
        confidence_pct = f"{pattern.confidence_score:.1%}"
        
        message = (
            f"🚨 {alert_level.upper()} ALERT: {pattern_name} detected for {pattern.symbol} "
            f"with {confidence_pct} confidence. "
        )
        
        # Add specific details based on pattern type
        if pattern.pattern_type == PatternType.CEX_LISTING:
            message += "Potential exchange listing indicators detected."
        elif pattern.pattern_type == PatternType.WHALE_ACCUMULATION:
            message += "Large holder accumulation pattern identified."
        elif pattern.pattern_type == PatternType.SOCIAL_SENTIMENT_SPIKE:
            message += "Social sentiment surge detected."
        
        return message
    
    async def _store_alert_in_database(self, alert_data: Dict[str, Any], message: str):
        """Store alert in database for tracking and analysis."""
        try:
            db = await get_db_session()
            try:
                await db.execute(text("""
                    INSERT INTO pattern_alerts 
                    (pattern_id, alert_type, priority, message, alert_data, sent_timestamp)
                    VALUES (:pattern_id, :alert_type, :priority, :message, :alert_data, :sent_timestamp)
                """), {
                    "pattern_id": alert_data["pattern_id"],
                    "alert_type": f"{alert_data['pattern_type']}_production_alert",
                    "priority": alert_data["alert_level"],
                    "message": message,
                    "alert_data": json.dumps(alert_data),
                    "sent_timestamp": datetime.now(timezone.utc)
                })
                await db.commit()
            finally:
                await db.close()
        except Exception as e:
            logger.error(f"Error storing alert in database: {e}")
    
    async def _send_external_alerts(self, alert_data: Dict[str, Any], message: str, alert_level: str):
        """Send alerts to external systems (webhook, email, Slack)."""
        # Placeholder for external alert integrations
        # In production, this would integrate with:
        # - Webhook endpoints
        # - Email services (SendGrid, AWS SES)
        # - Slack/Discord webhooks
        # - SMS services (Twilio)
        # - Push notifications
        
        logger.info(f"External alert sent: {alert_level} - {message}")
    
    async def _collect_initial_data(self):
        """Collect initial market data for better pattern detection."""
        logger.info("Collecting initial market data...")
        
        try:
            # Trigger data collection for monitored symbols
            from app.services.data_ingestion import data_ingestion_service
            
            await data_ingestion_service.start()
            
            # Collect multiple data points for each symbol
            for i in range(3):
                result = await data_ingestion_service.ingest_coingecko_data(self.monitored_symbols)
                logger.info(f"Initial data collection {i+1}/3: {result['success']} records")
                await asyncio.sleep(2)  # Rate limiting
            
            await data_ingestion_service.stop()
            
        except Exception as e:
            logger.error(f"Error collecting initial data: {e}")
    
    async def _metrics_collection_loop(self):
        """Collect system metrics for monitoring."""
        logger.info("Metrics collection loop started")
        
        while self.monitoring_active:
            try:
                metrics = await self._collect_current_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only recent metrics
                if len(self.metrics_history) > self.max_metrics_history:
                    self.metrics_history = self.metrics_history[-self.max_metrics_history:]
                
                # Log metrics periodically
                if len(self.metrics_history) % 10 == 0:  # Every 10 minutes
                    logger.info(f"System metrics: {metrics.memory_usage_mb:.1f}MB RAM, "
                              f"{metrics.cpu_usage_percent:.1f}% CPU, "
                              f"{metrics.avg_analysis_time_ms:.1f}ms avg analysis time")
                
                await asyncio.sleep(self.metrics_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error collecting metrics: {e}")
                await asyncio.sleep(self.metrics_interval)
        
        logger.info("Metrics collection loop stopped")
    
    async def _collect_current_metrics(self) -> MonitoringMetrics:
        """Collect current system metrics."""
        try:
            # System metrics
            if psutil:
                process = psutil.Process(os.getpid())
                memory_usage_mb = process.memory_info().rss / 1024 / 1024
                cpu_usage_percent = process.cpu_percent()
            else:
                memory_usage_mb = 0.0
                cpu_usage_percent = 0.0
            
            # Pattern detection metrics
            status = await pattern_detection_service.get_monitoring_status()
            
            # Calculate uptime
            uptime_seconds = (datetime.now(timezone.utc) - self.start_time).total_seconds() if self.start_time else 0
            
            # Calculate detection rate
            detection_rate = (self.total_patterns_detected / (uptime_seconds / 3600)) if uptime_seconds > 0 else 0
            
            return MonitoringMetrics(
                timestamp=datetime.now(timezone.utc),
                symbols_monitored=len(self.monitored_symbols),
                patterns_detected=self.total_patterns_detected,
                high_confidence_patterns=0,  # Would need to track this separately
                avg_analysis_time_ms=status.avg_detection_latency_ms,
                memory_usage_mb=memory_usage_mb,
                cpu_usage_percent=cpu_usage_percent,
                database_connections=1,  # Simplified
                alerts_generated=self.total_alerts_generated,
                uptime_seconds=uptime_seconds,
                detection_rate_per_hour=detection_rate,
                error_count=self.error_count
            )
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            # Return default metrics on error
            return MonitoringMetrics(
                timestamp=datetime.now(timezone.utc),
                symbols_monitored=0, patterns_detected=0, high_confidence_patterns=0,
                avg_analysis_time_ms=0, memory_usage_mb=0, cpu_usage_percent=0,
                database_connections=0, alerts_generated=0, uptime_seconds=0,
                detection_rate_per_hour=0, error_count=self.error_count
            )
    
    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get comprehensive monitoring status."""
        if not self.monitoring_active:
            return {"status": "inactive", "message": "Production monitoring not running"}
        
        try:
            # Get current metrics
            current_metrics = await self._collect_current_metrics()
            
            # Calculate performance statistics
            recent_metrics = self.metrics_history[-60:] if len(self.metrics_history) >= 60 else self.metrics_history
            
            if recent_metrics:
                avg_memory = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
                avg_cpu = sum(m.cpu_usage_percent for m in recent_metrics) / len(recent_metrics)
                avg_analysis_time = sum(m.avg_analysis_time_ms for m in recent_metrics) / len(recent_metrics)
            else:
                avg_memory = avg_cpu = avg_analysis_time = 0
            
            return {
                "status": "active",
                "monitoring_active": self.monitoring_active,
                "uptime_seconds": current_metrics.uptime_seconds,
                "uptime_hours": current_metrics.uptime_seconds / 3600,
                "monitored_symbols": self.monitored_symbols,
                "monitoring_interval_seconds": self.monitoring_interval,
                "current_metrics": asdict(current_metrics),
                "performance_averages": {
                    "avg_memory_usage_mb": avg_memory,
                    "avg_cpu_usage_percent": avg_cpu,
                    "avg_analysis_time_ms": avg_analysis_time
                },
                "alert_configuration": {
                    level: {"threshold": config.confidence_threshold, "enabled": config.enabled}
                    for level, config in self.alert_configs.items()
                },
                "session_statistics": {
                    "total_patterns_detected": self.total_patterns_detected,
                    "total_alerts_generated": self.total_alerts_generated,
                    "detection_cycles_completed": self.detection_cycles_completed,
                    "error_count": self.error_count,
                    "detection_rate_per_hour": current_metrics.detection_rate_per_hour
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting monitoring status: {e}")
            return {"status": "error", "error": str(e)}


# Global production monitoring service instance
production_monitoring_service = ProductionMonitoringService()
