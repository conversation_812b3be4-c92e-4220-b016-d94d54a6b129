"""
Cryptographic validation service for financial-grade data integrity.
Implements zero-trust validation with audit trails and anomaly detection.
"""

import hashlib
import hmac
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class ValidationResult:
    """Result of cryptographic validation."""
    is_valid: bool
    error_message: Optional[str] = None
    confidence_score: float = 0.0
    anomaly_detected: bool = False
    validation_timestamp: datetime = None

    def __post_init__(self):
        if self.validation_timestamp is None:
            self.validation_timestamp = datetime.now(timezone.utc)


class CryptoValidator:
    """
    Financial-grade cryptographic validation with anomaly detection.
    Implements zero-trust architecture for data integrity.
    """

    def __init__(self):
        self.secret_key = settings.DATA_INTEGRITY_SECRET.encode()
        self.validation_cache: Dict[str, ValidationResult] = {}
        self.anomaly_threshold = 0.95  # 95% confidence threshold

    def generate_data_hash(self, data: Dict[str, Any]) -> str:
        """
        Generate SHA-256 hash for data integrity validation.
        Uses deterministic JSON serialization for consistent hashing.
        """
        try:
            # Normalize data for consistent hashing
            normalized_data = self._normalize_data(data)
            serialized = json.dumps(normalized_data, sort_keys=True, default=str)
            return hashlib.sha256(serialized.encode('utf-8')).hexdigest()
        except Exception as e:
            logger.error(f"Hash generation failed: {e}")
            raise ValueError(f"Failed to generate data hash: {e}")

    def generate_signature(self, data_hash: str) -> str:
        """
        Generate HMAC-SHA256 signature for data authenticity.
        """
        try:
            return hmac.new(
                self.secret_key,
                data_hash.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
        except Exception as e:
            logger.error(f"Signature generation failed: {e}")
            raise ValueError(f"Failed to generate signature: {e}")

    def validate_data_integrity(self, data: Dict[str, Any], expected_hash: str) -> ValidationResult:
        """
        Validate data integrity using cryptographic hash comparison.
        """
        try:
            # Generate hash for received data
            actual_hash = self.generate_data_hash(data)
            
            # Compare hashes
            is_valid = hmac.compare_digest(actual_hash, expected_hash)
            
            # Calculate confidence score based on hash match
            confidence_score = 1.0 if is_valid else 0.0
            
            # Check for anomalies in data patterns
            anomaly_detected = self._detect_anomalies(data)
            
            result = ValidationResult(
                is_valid=is_valid,
                confidence_score=confidence_score,
                anomaly_detected=anomaly_detected,
                error_message=None if is_valid else "Hash mismatch detected"
            )
            
            # Cache validation result
            self.validation_cache[expected_hash] = result
            
            if not is_valid:
                logger.warning(f"Data integrity validation failed: {expected_hash[:16]}...")
            
            return result
            
        except Exception as e:
            logger.error(f"Data integrity validation error: {e}")
            return ValidationResult(
                is_valid=False,
                error_message=f"Validation error: {e}",
                confidence_score=0.0
            )

    def validate_signature(self, data_hash: str, signature: str) -> ValidationResult:
        """
        Validate HMAC signature for data authenticity.
        """
        try:
            # Generate expected signature
            expected_signature = self.generate_signature(data_hash)
            
            # Compare signatures using constant-time comparison
            is_valid = hmac.compare_digest(signature, expected_signature)
            
            confidence_score = 1.0 if is_valid else 0.0
            
            result = ValidationResult(
                is_valid=is_valid,
                confidence_score=confidence_score,
                error_message=None if is_valid else "Signature verification failed"
            )
            
            if not is_valid:
                logger.warning(f"Signature validation failed: {data_hash[:16]}...")
            
            return result
            
        except Exception as e:
            logger.error(f"Signature validation error: {e}")
            return ValidationResult(
                is_valid=False,
                error_message=f"Signature validation error: {e}",
                confidence_score=0.0
            )

    def validate_financial_data(self, data: Dict[str, Any]) -> ValidationResult:
        """
        Validate financial data with business logic checks.
        """
        try:
            errors = []
            confidence_score = 1.0
            
            # Price validation
            if "price" in data:
                price = float(data["price"])
                if price <= 0:
                    errors.append("Price must be positive")
                    confidence_score -= 0.3
                elif price > 1000000:  # Sanity check for crypto prices
                    errors.append("Price exceeds reasonable bounds")
                    confidence_score -= 0.2
            
            # Volume validation
            if "volume" in data:
                volume = float(data["volume"])
                if volume < 0:
                    errors.append("Volume cannot be negative")
                    confidence_score -= 0.3
            
            # Timestamp validation
            if "timestamp" in data:
                try:
                    if isinstance(data["timestamp"], str):
                        timestamp = datetime.fromisoformat(data["timestamp"].replace('Z', '+00:00'))
                    else:
                        timestamp = data["timestamp"]
                    
                    # Check if timestamp is too far in the future or past
                    now = datetime.now(timezone.utc)
                    time_diff = abs((now - timestamp).total_seconds())
                    
                    if time_diff > 3600:  # More than 1 hour difference
                        errors.append("Timestamp is too far from current time")
                        confidence_score -= 0.2
                        
                except Exception:
                    errors.append("Invalid timestamp format")
                    confidence_score -= 0.4
            
            # Symbol validation
            if "symbol" in data:
                symbol = data["symbol"]
                if not isinstance(symbol, str) or len(symbol) < 2:
                    errors.append("Invalid symbol format")
                    confidence_score -= 0.2
            
            # Ensure confidence score doesn't go below 0
            confidence_score = max(0.0, confidence_score)
            
            is_valid = len(errors) == 0 and confidence_score >= 0.7
            
            return ValidationResult(
                is_valid=is_valid,
                confidence_score=confidence_score,
                error_message="; ".join(errors) if errors else None,
                anomaly_detected=confidence_score < self.anomaly_threshold
            )
            
        except Exception as e:
            logger.error(f"Financial data validation error: {e}")
            return ValidationResult(
                is_valid=False,
                error_message=f"Financial validation error: {e}",
                confidence_score=0.0
            )

    def _normalize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize data for consistent hashing.
        """
        normalized = {}
        
        for key, value in data.items():
            if isinstance(value, float):
                # Round floats to avoid precision issues
                normalized[key] = round(value, 8)
            elif isinstance(value, datetime):
                # Convert datetime to ISO string
                normalized[key] = value.isoformat()
            elif isinstance(value, dict):
                # Recursively normalize nested dictionaries
                normalized[key] = self._normalize_data(value)
            else:
                normalized[key] = value
        
        return normalized

    def _detect_anomalies(self, data: Dict[str, Any]) -> bool:
        """
        Detect anomalies in financial data patterns.
        Simple implementation - can be enhanced with ML models.
        """
        try:
            # Check for unusual price movements (basic implementation)
            if "price" in data:
                price = float(data["price"])
                
                # Check for extreme values
                if price > 1000000 or price < 0.000001:
                    return True
            
            # Check for unusual volume patterns
            if "volume" in data:
                volume = float(data["volume"])
                
                # Check for extreme volume
                if volume > 1000000000:  # 1 billion
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Anomaly detection error: {e}")
            return True  # Treat errors as anomalies

    def get_validation_stats(self) -> Dict[str, Any]:
        """
        Get validation statistics from cache.
        """
        if not self.validation_cache:
            return {"total_validations": 0}
        
        total = len(self.validation_cache)
        valid = sum(1 for result in self.validation_cache.values() if result.is_valid)
        anomalies = sum(1 for result in self.validation_cache.values() if result.anomaly_detected)
        
        avg_confidence = sum(result.confidence_score for result in self.validation_cache.values()) / total
        
        return {
            "total_validations": total,
            "valid_count": valid,
            "invalid_count": total - valid,
            "anomaly_count": anomalies,
            "success_rate": valid / total,
            "average_confidence": round(avg_confidence, 4)
        }
