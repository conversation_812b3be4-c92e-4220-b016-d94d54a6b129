import asyncio
import sys
sys.path.append('.')

from app.db.deps import get_db_session
from sqlalchemy import text
from datetime import datetime, timezone, timedelta

async def debug_data_query():
    print('🔍 Debugging data query...')
    
    db = await get_db_session()
    
    try:
        # Check what data we have
        result = await db.execute(text('SELECT symbol, COUNT(*) as count FROM market_data GROUP BY symbol'))
        rows = result.fetchall()
        print('Data by symbol:')
        for row in rows:
            print(f'  {row.symbol}: {row.count} records')
        
        # Check recent data
        result = await db.execute(text('SELECT symbol, timestamp FROM market_data ORDER BY timestamp DESC LIMIT 10'))
        rows = result.fetchall()
        print('\nRecent data:')
        for row in rows:
            print(f'  {row.symbol}: {row.timestamp}')
        
        # Test the exact query used by pattern detection
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        print(f'\nCutoff time: {cutoff_time}')
        
        query = text("""
            SELECT timestamp, symbol, exchange, price, volume, market_cap, source, data_hash
            FROM market_data 
            WHERE symbol = :symbol 
            AND timestamp >= :cutoff_time
            ORDER BY timestamp DESC
            LIMIT 1000
        """)
        
        result = await db.execute(query, {'symbol': 'BITCOIN', 'cutoff_time': cutoff_time})
        rows = result.fetchall()
        print(f'\nQuery result for BITCOIN: {len(rows)} rows')
        
        if rows:
            print('First few rows:')
            for i, row in enumerate(rows[:3]):
                print(f'  {i+1}: {row.symbol} at {row.timestamp}, price=${row.price}')
        
    finally:
        await db.close()

asyncio.run(debug_data_query())
