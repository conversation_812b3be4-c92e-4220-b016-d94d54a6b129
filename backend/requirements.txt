# Core FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database and ORM
asyncpg==0.29.0
psycopg2-binary==2.9.9
sqlalchemy[asyncio]==2.0.23
alembic==1.12.1

# Data validation and serialization
pydantic[email]==2.5.0
pydantic-settings==2.1.0

# Kafka and streaming
confluent-kafka[avro]==2.3.0
fastavro==1.9.0
kafka-python==2.0.2

# Redis and caching
redis[hiredis]>=4.5.2,<5.0.0
aioredis==2.0.1

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Scientific computing and ML
numpy==1.24.4
scipy==1.11.4
pandas==2.1.4
scikit-learn==1.3.2
statsmodels==0.14.0

# Financial data and analysis
yfinance==0.2.28
ccxt==4.1.64
# ta-lib==0.4.28  # Commented out - requires TA-Lib C library
quantlib==1.32

# Cryptography and security
cryptography>=42.0.0
pyjwt[crypto]>=2.8.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Monitoring and observability
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-exporter-otlp==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0
opentelemetry-instrumentation-redis==0.42b0
prometheus-client==0.19.0

# Logging and configuration
structlog==23.2.0
python-json-logger==2.0.7
python-dotenv==1.0.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# Development tools
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.1
pre-commit>=3.6.0

# API documentation
python-jose[cryptography]==3.3.0

# Time and date handling
python-dateutil==2.8.2
pytz==2023.3

# Async utilities
asyncio-mqtt==0.16.1
tenacity==8.2.3

# Data processing
orjson==3.9.10
msgpack==1.0.7

# Financial APIs
alpha-vantage==2.3.1
quandl==3.7.0

# Social media APIs
praw==7.7.1  # Reddit API
tweepy==4.14.0  # Twitter API

# Blockchain and Web3
web3==6.12.0
# thirdweb-sdk>=3.1.1  # Commented out due to dependency conflicts

# Mathematical optimization
cvxpy==1.4.1
cvxopt==1.3.2

# Signal processing (for quantum models)
PyWavelets==1.4.1

# Schema validation
jsonschema==4.20.0

# Background tasks
celery[redis]==5.3.4
flower==2.0.1

# Rate limiting
slowapi==0.1.9

# CORS and middleware
# FastAPI has built-in CORS middleware

# Environment and secrets management
keyring==24.3.0
python-decouple==3.8
