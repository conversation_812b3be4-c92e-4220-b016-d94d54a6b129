"""
Comprehensive database schema validation tests.
Tests constraint validation, index performance, and data integrity.
"""

import pytest
import asyncio
from datetime import datetime, timezone
from decimal import Decimal
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.deps import get_db_session
from app.db.models import PriceEvent, SocialSentiment, MutualInformationScore


class TestDatabaseSchema:
    """Test database schema creation and constraints."""

    @pytest.fixture
    async def db_session(self):
        """Get database session for testing."""
        session = await get_db_session()
        try:
            yield session
        finally:
            await session.close()

    async def test_schemas_exist(self, db_session: AsyncSession):
        """Test that all required schemas exist."""
        result = await db_session.execute(text("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name IN ('market_data', 'risk_analysis', 'audit_logs', 'quantum_models')
            ORDER BY schema_name
        """))
        schemas = [row[0] for row in result.fetchall()]
        
        expected_schemas = ['audit_logs', 'market_data', 'quantum_models', 'risk_analysis']
        assert schemas == expected_schemas, f"Expected {expected_schemas}, got {schemas}"

    async def test_hypertables_exist(self, db_session: AsyncSession):
        """Test that TimescaleDB hypertables are created."""
        result = await db_session.execute(text("""
            SELECT schemaname, tablename 
            FROM timescaledb_information.hypertables
            ORDER BY schemaname, tablename
        """))
        hypertables = [(row[0], row[1]) for row in result.fetchall()]
        
        expected_hypertables = [
            ('market_data', 'price_events'),
            ('market_data', 'social_sentiment'),
            ('risk_analysis', 'mutual_information_scores')
        ]
        assert hypertables == expected_hypertables, f"Expected {expected_hypertables}, got {hypertables}"

    async def test_price_events_constraints(self, db_session: AsyncSession):
        """Test price_events table constraints."""
        # Test positive price constraint
        with pytest.raises(Exception):
            await db_session.execute(text("""
                INSERT INTO market_data.price_events 
                (timestamp, symbol, exchange, price, volume, source, data_hash)
                VALUES (NOW(), 'BTC', 'binance', -100, 1000, 'test', 'hash123')
            """))
            await db_session.commit()

        # Test positive volume constraint
        with pytest.raises(Exception):
            await db_session.execute(text("""
                INSERT INTO market_data.price_events 
                (timestamp, symbol, exchange, price, volume, source, data_hash)
                VALUES (NOW(), 'BTC', 'binance', 50000, -1000, 'test', 'hash123')
            """))
            await db_session.commit()

    async def test_social_sentiment_constraints(self, db_session: AsyncSession):
        """Test social_sentiment table constraints."""
        # Test sentiment score range constraint
        with pytest.raises(Exception):
            await db_session.execute(text("""
                INSERT INTO market_data.social_sentiment 
                (timestamp, symbol, platform, sentiment_score, mention_count, data_hash)
                VALUES (NOW(), 'BTC', 'reddit', 2.0, 100, 'hash123')
            """))
            await db_session.commit()

        with pytest.raises(Exception):
            await db_session.execute(text("""
                INSERT INTO market_data.social_sentiment 
                (timestamp, symbol, platform, sentiment_score, mention_count, data_hash)
                VALUES (NOW(), 'BTC', 'reddit', -2.0, 100, 'hash123')
            """))
            await db_session.commit()

    async def test_mutual_information_constraints(self, db_session: AsyncSession):
        """Test mutual_information_scores table constraints."""
        # Test positive MI score constraint
        with pytest.raises(Exception):
            await db_session.execute(text("""
                INSERT INTO risk_analysis.mutual_information_scores 
                (timestamp, symbol_pair, mi_score, threshold_exceeded, window_size, calculation_method, data_hash)
                VALUES (NOW(), 'BTC-ETH', -0.1, false, 60, 'kraskov', 'hash123')
            """))
            await db_session.commit()

    async def test_successful_data_insertion(self, db_session: AsyncSession):
        """Test successful data insertion with valid data."""
        # Insert valid price event
        await db_session.execute(text("""
            INSERT INTO market_data.price_events 
            (timestamp, symbol, exchange, price, volume, source, data_hash)
            VALUES (NOW(), 'BTC', 'binance', 50000.12345678, 1000.5, 'coingecko', 'valid_hash_123')
        """))
        
        # Insert valid social sentiment
        await db_session.execute(text("""
            INSERT INTO market_data.social_sentiment 
            (timestamp, symbol, platform, sentiment_score, mention_count, data_hash)
            VALUES (NOW(), 'BTC', 'reddit', 0.75, 150, 'sentiment_hash_123')
        """))
        
        # Insert valid mutual information score
        await db_session.execute(text("""
            INSERT INTO risk_analysis.mutual_information_scores 
            (timestamp, symbol_pair, mi_score, threshold_exceeded, window_size, calculation_method, data_hash)
            VALUES (NOW(), 'BTC-ETH', 0.35, true, 60, 'kraskov', 'mi_hash_123')
        """))
        
        await db_session.commit()

        # Verify data was inserted
        result = await db_session.execute(text("SELECT COUNT(*) FROM market_data.price_events"))
        price_count = result.scalar()
        assert price_count >= 1

        result = await db_session.execute(text("SELECT COUNT(*) FROM market_data.social_sentiment"))
        sentiment_count = result.scalar()
        assert sentiment_count >= 1

        result = await db_session.execute(text("SELECT COUNT(*) FROM risk_analysis.mutual_information_scores"))
        mi_count = result.scalar()
        assert mi_count >= 1

    async def test_index_performance(self, db_session: AsyncSession):
        """Test that indexes are working for query performance."""
        # Test price_events indexes
        result = await db_session.execute(text("""
            EXPLAIN (FORMAT JSON) 
            SELECT * FROM market_data.price_events 
            WHERE symbol = 'BTC' AND timestamp > NOW() - INTERVAL '1 hour'
        """))
        plan = result.scalar()
        # Should use index scan, not sequential scan
        assert 'Index Scan' in str(plan) or 'Bitmap' in str(plan)

    async def test_timescaledb_chunks(self, db_session: AsyncSession):
        """Test that TimescaleDB is creating chunks properly."""
        result = await db_session.execute(text("""
            SELECT chunk_schema, chunk_name, hypertable_schema, hypertable_name
            FROM timescaledb_information.chunks
            WHERE hypertable_name IN ('price_events', 'social_sentiment', 'mutual_information_scores')
        """))
        chunks = result.fetchall()
        
        # Should have at least one chunk per hypertable
        chunk_tables = set((row[2], row[3]) for row in chunks)
        expected_tables = {
            ('market_data', 'price_events'),
            ('market_data', 'social_sentiment'),
            ('risk_analysis', 'mutual_information_scores')
        }
        assert chunk_tables == expected_tables, f"Expected chunks for {expected_tables}, got {chunk_tables}"


if __name__ == "__main__":
    # Run tests directly
    async def run_tests():
        test_instance = TestDatabaseSchema()
        session = await get_db_session()
        
        try:
            await test_instance.test_schemas_exist(session)
            await test_instance.test_hypertables_exist(session)
            await test_instance.test_successful_data_insertion(session)
            await test_instance.test_timescaledb_chunks(session)
            print("✅ All database schema tests passed!")
        except Exception as e:
            print(f"❌ Test failed: {e}")
        finally:
            await session.close()

    asyncio.run(run_tests())
