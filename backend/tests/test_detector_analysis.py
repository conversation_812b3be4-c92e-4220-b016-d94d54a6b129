"""
Detailed analysis of pattern detector performance and debugging.
Identifies specific issues with CEX Listing and Whale Accumulation detectors.
"""

import asyncio
import sys
sys.path.append('/Users/<USER>/ar/backend')

from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any
from app.services.pattern_detection import pattern_detection_service
from app.models.pattern_detection import PatternDetectorConfig


async def analyze_detector_performance():
    """Analyze detector performance and identify improvement areas."""
    print("🔍 DETAILED DETECTOR PERFORMANCE ANALYSIS")
    print("=" * 80)
    
    await pattern_detection_service.start()
    
    try:
        # Test CEX Listing Detector with detailed analysis
        print("\n📊 CEX LISTING DETECTOR ANALYSIS")
        print("-" * 50)
        
        cex_detector = pattern_detection_service.registry.get_detector(
            pattern_detection_service.registry.get_all_detectors().keys().__iter__().__next__()
        )
        
        # Create test data that should trigger CEX listing detection
        test_market_data = create_strong_cex_listing_signal()
        
        print(f"Test data points: {len(test_market_data)}")
        print(f"Volume progression: {[round(d['volume'], 0) for d in test_market_data[-6:]]}")
        print(f"Price progression: {[round(d['price'], 2) for d in test_market_data[-6:]]}")
        
        # Test each detector individually
        detectors = pattern_detection_service.registry.get_all_detectors()
        
        for pattern_type, detector in detectors.items():
            print(f"\n🔧 Testing {pattern_type.value} detector:")
            
            try:
                patterns = await detector.detect_patterns(
                    symbol="TEST",
                    market_data=test_market_data,
                    context_data=create_test_context_data()
                )
                
                if patterns:
                    for pattern in patterns:
                        print(f"   ✅ Pattern detected: {pattern.confidence_score:.3f} confidence")
                        print(f"   📋 Trigger conditions: {pattern.trigger_conditions}")
                        print(f"   📈 Supporting indicators: {pattern.supporting_indicators}")
                else:
                    print(f"   ❌ No patterns detected")
                    
                    # Debug detector thresholds
                    if hasattr(detector, 'volume_spike_threshold'):
                        print(f"   🔧 Volume spike threshold: {detector.volume_spike_threshold}")
                    if hasattr(detector, 'price_movement_threshold'):
                        print(f"   🔧 Price movement threshold: {detector.price_movement_threshold}")
                    if hasattr(detector, 'confidence_threshold'):
                        print(f"   🔧 Confidence threshold: {detector.config.confidence_threshold}")
                        
            except Exception as e:
                print(f"   ❌ Error testing detector: {e}")
        
        # Test with different threshold configurations
        print(f"\n🎛️ TESTING WITH ADJUSTED THRESHOLDS")
        print("-" * 50)
        
        # Create more sensitive configuration
        sensitive_config = PatternDetectorConfig(
            enabled=True,
            confidence_threshold=0.5,  # Lower threshold
            detection_interval_seconds=300,
            lookback_hours=24
        )
        
        # Re-import and test with sensitive config
        from app.services.detectors.cex_listing_detector import CEXListingDetector
        from app.services.detectors.whale_accumulation_detector import WhaleAccumulationDetector
        
        sensitive_cex_detector = CEXListingDetector(sensitive_config)
        sensitive_whale_detector = WhaleAccumulationDetector(sensitive_config)
        
        # Lower internal thresholds for testing
        sensitive_cex_detector.volume_spike_threshold = 2.0  # Reduced from 3.0
        sensitive_cex_detector.price_movement_threshold = 0.05  # Reduced from 0.10
        
        sensitive_whale_detector.large_volume_threshold = 1.5  # Reduced from 2.0
        sensitive_whale_detector.price_stability_threshold = 0.10  # Increased from 0.05
        
        print(f"\n🔧 Testing CEX detector with sensitive thresholds:")
        patterns = await sensitive_cex_detector.detect_patterns(
            symbol="TEST",
            market_data=test_market_data,
            context_data=None
        )
        
        if patterns:
            for pattern in patterns:
                print(f"   ✅ Sensitive CEX pattern detected: {pattern.confidence_score:.3f}")
        else:
            print(f"   ❌ Still no CEX patterns detected")
        
        print(f"\n🔧 Testing Whale detector with sensitive thresholds:")
        whale_test_data = create_whale_accumulation_signal()
        patterns = await sensitive_whale_detector.detect_patterns(
            symbol="TEST",
            market_data=whale_test_data,
            context_data=None
        )
        
        if patterns:
            for pattern in patterns:
                print(f"   ✅ Sensitive whale pattern detected: {pattern.confidence_score:.3f}")
        else:
            print(f"   ❌ Still no whale patterns detected")
        
        print(f"\n📋 RECOMMENDATIONS:")
        print("1. Lower CEX listing volume spike threshold from 3.0x to 2.0x")
        print("2. Lower CEX listing price movement threshold from 10% to 5%")
        print("3. Lower whale accumulation volume threshold from 2.0x to 1.5x")
        print("4. Increase whale price stability threshold from 5% to 10%")
        print("5. Lower overall confidence threshold from 0.7 to 0.6 for initial testing")
        
    finally:
        await pattern_detection_service.stop()


def create_strong_cex_listing_signal() -> List[Dict[str, Any]]:
    """Create market data that should strongly indicate a CEX listing."""
    base_price = 100.0
    base_volume = 1000000.0
    
    market_data = []
    
    # 24 hours of data with strong CEX listing pattern
    for i in range(24):
        timestamp = datetime.now(timezone.utc) - timedelta(hours=23-i)
        
        # Strong volume spike in last 4 hours (5x increase)
        if i >= 20:  # Last 4 hours
            volume_multiplier = 5.0 + (i - 20) * 0.5  # 5x to 6.5x volume
        elif i >= 16:  # Hours 16-20: building volume
            volume_multiplier = 2.0 + (i - 16) * 0.75  # 2x to 5x volume
        else:
            volume_multiplier = 1.0  # Normal volume
        
        # Strong price increase in last 2 hours (20% total)
        if i >= 22:  # Last 2 hours
            price_multiplier = 1.0 + 0.20 * (i - 22) / 2  # Up to 20% increase
        elif i >= 18:  # Hours 18-22: gradual increase
            price_multiplier = 1.0 + 0.05 * (i - 18) / 4  # Up to 5% increase
        else:
            price_multiplier = 1.0  # Stable price
        
        price = base_price * price_multiplier
        volume = base_volume * volume_multiplier
        
        data_point = {
            'timestamp': timestamp,
            'symbol': 'TEST',
            'exchange': 'test_exchange',
            'price': price,
            'volume': volume,
            'market_cap': price * 1000000,
            'source': 'test_data',
            'data_hash': f'test_hash_{i}'
        }
        
        market_data.append(data_point)
    
    return market_data


def create_whale_accumulation_signal() -> List[Dict[str, Any]]:
    """Create market data that should indicate whale accumulation."""
    base_price = 100.0
    base_volume = 1000000.0
    
    market_data = []
    
    # 48 hours of data with whale accumulation pattern
    for i in range(48):
        timestamp = datetime.now(timezone.utc) - timedelta(hours=47-i)
        
        # Sustained elevated volume for 24 hours (2x increase)
        if i >= 24:  # Last 24 hours - accumulation period
            volume_multiplier = 2.0 + 0.1 * ((i - 24) % 6)  # 2x to 2.5x volume with variation
        else:
            volume_multiplier = 1.0  # Normal volume
        
        # Very stable price during accumulation (max 3% variation)
        if i >= 24:  # Accumulation period
            price_variation = 0.03 * ((i % 8) - 4) / 4  # ±3% variation
            price_multiplier = 1.0 + price_variation
        else:
            price_multiplier = 1.0  # Stable price
        
        price = base_price * price_multiplier
        volume = base_volume * volume_multiplier
        
        data_point = {
            'timestamp': timestamp,
            'symbol': 'TEST',
            'exchange': 'test_exchange',
            'price': price,
            'volume': volume,
            'market_cap': price * 1000000,
            'source': 'test_data',
            'data_hash': f'test_hash_{i}'
        }
        
        market_data.append(data_point)
    
    return market_data


def create_test_context_data() -> Dict[str, Any]:
    """Create test context data for social sentiment."""
    return {
        'social': {
            'current_sentiment': 0.8,
            'baseline_sentiment': 0.1,
            'current_mentions': 1000,
            'baseline_mentions': 100,
            'sentiment_history': [0.1, 0.2, 0.4, 0.6, 0.7, 0.8],
            'mention_history': [100, 150, 300, 500, 750, 1000],
            'influencer_mentions': 5,
            'top_influencers': ['test_influencer_1', 'test_influencer_2'],
            'influencer_sentiment': 0.9
        }
    }


if __name__ == "__main__":
    asyncio.run(analyze_detector_performance())
