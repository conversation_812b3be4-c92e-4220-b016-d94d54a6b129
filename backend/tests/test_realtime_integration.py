"""
Real-time Data Integration Validation Testing.
Tests that current market data produces meaningful pattern signals, not random noise.
"""

import asyncio
import sys
sys.path.append('/Users/<USER>/ar/backend')

import httpx
import time
from datetime import datetime, timezone
from typing import Dict, List, Any

from app.services.pattern_detection import pattern_detection_service
from app.models.pattern_detection import PatternDetectionRequest, PatternType


class RealTimeIntegrationTester:
    """Test real-time data integration and signal quality."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.client = httpx.AsyncClient(timeout=60.0)
        self.test_symbols = ["bitcoin", "ethereum", "solana", "cardano"]
        
    async def run_realtime_validation(self) -> Dict[str, Any]:
        """Run comprehensive real-time data integration validation."""
        print("🔄 REAL-TIME DATA INTEGRATION VALIDATION")
        print("=" * 80)
        
        try:
            # Start pattern detection service
            await pattern_detection_service.start()
            
            # Test 1: Current market data quality
            print("\n📊 Testing current market data quality...")
            data_quality = await self._test_market_data_quality()
            
            # Test 2: Pattern detection on live data
            print("\n🔍 Testing pattern detection on live data...")
            pattern_results = await self._test_live_pattern_detection()
            
            # Test 3: Signal vs noise analysis
            print("\n📈 Testing signal vs noise analysis...")
            signal_analysis = await self._test_signal_quality()
            
            # Test 4: Real-time monitoring performance
            print("\n⚡ Testing real-time monitoring performance...")
            monitoring_results = await self._test_monitoring_performance()
            
            # Generate comprehensive report
            report = self._generate_integration_report(
                data_quality, pattern_results, signal_analysis, monitoring_results
            )
            
            print("\n" + "=" * 80)
            print("🎉 REAL-TIME INTEGRATION VALIDATION COMPLETED!")
            print("=" * 80)
            
            return report
            
        except Exception as e:
            print(f"❌ Real-time validation failed: {e}")
            raise
        finally:
            await pattern_detection_service.stop()
            await self.client.aclose()
    
    async def _test_market_data_quality(self) -> Dict[str, Any]:
        """Test the quality of current market data."""
        print("   Testing market data from CoinGecko and Binance...")
        
        # Get current market data stats
        response = await self.client.get(f"{self.base_url}/api/v1/data/stats")
        stats = response.json()
        
        # Get recent data
        response = await self.client.get(f"{self.base_url}/api/v1/data/recent-data?limit=20")
        recent_data = response.json()
        
        # Analyze data quality
        total_records = stats["total_records"]
        data_sources = list(stats["records_by_source"].keys())
        symbols_covered = list(stats["records_by_symbol"].keys())
        
        # Check data freshness
        if recent_data["data"]:
            latest_timestamp = recent_data["data"][0]["timestamp"]
            latest_time = datetime.fromisoformat(latest_timestamp.replace('Z', '+00:00'))
            data_age_minutes = (datetime.now(timezone.utc) - latest_time).total_seconds() / 60
        else:
            data_age_minutes = float('inf')
        
        # Check data completeness
        required_fields = ["timestamp", "symbol", "price", "volume", "source", "data_hash"]
        complete_records = 0
        
        for record in recent_data["data"]:
            if all(field in record and record[field] is not None for field in required_fields):
                complete_records += 1
        
        completeness_rate = complete_records / len(recent_data["data"]) if recent_data["data"] else 0
        
        quality_metrics = {
            "total_records": total_records,
            "data_sources": data_sources,
            "symbols_covered": len(symbols_covered),
            "data_age_minutes": data_age_minutes,
            "completeness_rate": completeness_rate,
            "data_freshness_ok": data_age_minutes <= 10,  # Data should be < 10 minutes old
            "multi_source_ok": len(data_sources) >= 2,  # Should have multiple sources
            "sufficient_symbols": len(symbols_covered) >= 2,  # Should cover multiple symbols
            "quality_score": (
                (1.0 if data_age_minutes <= 10 else 0.5) +
                (1.0 if len(data_sources) >= 2 else 0.0) +
                (1.0 if len(symbols_covered) >= 2 else 0.0) +
                completeness_rate
            ) / 4
        }
        
        print(f"   ✅ Total records: {total_records}")
        print(f"   ✅ Data sources: {data_sources}")
        print(f"   ✅ Symbols covered: {len(symbols_covered)}")
        print(f"   ✅ Data age: {data_age_minutes:.1f} minutes")
        print(f"   ✅ Completeness: {completeness_rate:.1%}")
        print(f"   ✅ Quality score: {quality_metrics['quality_score']:.1%}")
        
        return quality_metrics
    
    async def _test_live_pattern_detection(self) -> Dict[str, Any]:
        """Test pattern detection on current live market data."""
        print("   Running pattern detection on live market data...")
        
        # Test pattern detection for each symbol
        results = {}
        
        for symbol in self.test_symbols:
            try:
                # Run pattern analysis
                request = PatternDetectionRequest(
                    symbols=[symbol],
                    pattern_types=None,  # Test all pattern types
                    lookback_hours=24,
                    min_confidence=0.5,  # Lower threshold for testing
                    real_time=False
                )
                
                response = await self.client.post(
                    f"{self.base_url}/api/v1/patterns/analyze",
                    json=request.dict()
                )
                
                if response.status_code == 200:
                    analysis = response.json()
                    
                    results[symbol] = {
                        "patterns_detected": analysis["total_patterns"],
                        "high_confidence_patterns": analysis["high_confidence_patterns"],
                        "analysis_duration_ms": analysis["analysis_duration_ms"],
                        "data_points_analyzed": analysis["data_points_analyzed"],
                        "pattern_types": list(analysis["pattern_distribution"].keys()) if analysis["pattern_distribution"] else [],
                        "success": True
                    }
                    
                    print(f"   ✅ {symbol}: {analysis['total_patterns']} patterns, {analysis['data_points_analyzed']} data points")
                else:
                    results[symbol] = {"success": False, "error": f"HTTP {response.status_code}"}
                    print(f"   ❌ {symbol}: Failed with HTTP {response.status_code}")
                    
            except Exception as e:
                results[symbol] = {"success": False, "error": str(e)}
                print(f"   ❌ {symbol}: Error - {e}")
        
        # Calculate overall metrics
        successful_analyses = sum(1 for r in results.values() if r.get("success", False))
        total_patterns = sum(r.get("patterns_detected", 0) for r in results.values())
        avg_analysis_time = sum(r.get("analysis_duration_ms", 0) for r in results.values()) / len(results)
        
        summary = {
            "symbol_results": results,
            "successful_analyses": successful_analyses,
            "success_rate": successful_analyses / len(self.test_symbols),
            "total_patterns_detected": total_patterns,
            "avg_analysis_time_ms": avg_analysis_time,
            "performance_acceptable": avg_analysis_time <= 1000,  # < 1 second
            "detection_working": successful_analyses >= len(self.test_symbols) * 0.8  # 80% success rate
        }
        
        return summary
    
    async def _test_signal_quality(self) -> Dict[str, Any]:
        """Test signal quality vs noise in pattern detection."""
        print("   Analyzing signal quality vs noise...")
        
        # Run multiple analyses to check for consistency
        consistency_tests = []
        
        for i in range(3):
            print(f"     Running consistency test {i+1}/3...")
            
            request = PatternDetectionRequest(
                symbols=self.test_symbols,
                pattern_types=None,
                lookback_hours=12,  # Shorter window for consistency testing
                min_confidence=0.6,
                real_time=False
            )
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/patterns/analyze",
                json=request.dict()
            )
            
            if response.status_code == 200:
                analysis = response.json()
                consistency_tests.append({
                    "patterns_detected": analysis["total_patterns"],
                    "high_confidence": analysis["high_confidence_patterns"],
                    "pattern_distribution": analysis["pattern_distribution"]
                })
            
            # Wait between tests
            await asyncio.sleep(2)
        
        # Analyze consistency
        if consistency_tests:
            pattern_counts = [t["patterns_detected"] for t in consistency_tests]
            high_conf_counts = [t["high_confidence"] for t in consistency_tests]
            
            # Calculate variance (low variance = consistent signal)
            pattern_variance = sum((x - sum(pattern_counts)/len(pattern_counts))**2 for x in pattern_counts) / len(pattern_counts)
            high_conf_variance = sum((x - sum(high_conf_counts)/len(high_conf_counts))**2 for x in high_conf_counts) / len(high_conf_counts)
            
            signal_quality = {
                "consistency_tests": len(consistency_tests),
                "pattern_counts": pattern_counts,
                "high_confidence_counts": high_conf_counts,
                "pattern_variance": pattern_variance,
                "high_conf_variance": high_conf_variance,
                "signal_consistent": pattern_variance <= 2.0,  # Low variance indicates consistent signal
                "high_conf_consistent": high_conf_variance <= 1.0,
                "signal_quality_score": max(0, 1.0 - (pattern_variance / 10.0))  # Normalize variance to 0-1 score
            }
            
            print(f"     Pattern counts: {pattern_counts}")
            print(f"     High confidence counts: {high_conf_counts}")
            print(f"     Pattern variance: {pattern_variance:.2f}")
            print(f"     Signal quality score: {signal_quality['signal_quality_score']:.2f}")
        else:
            signal_quality = {"error": "No successful consistency tests"}
        
        return signal_quality
    
    async def _test_monitoring_performance(self) -> Dict[str, Any]:
        """Test real-time monitoring performance."""
        print("   Testing real-time monitoring performance...")
        
        # Start monitoring
        start_response = await self.client.post(
            f"{self.base_url}/api/v1/patterns/monitoring/start",
            json={"symbols": ["bitcoin", "ethereum"], "interval_seconds": 60}
        )
        
        monitoring_started = start_response.status_code == 200
        
        if monitoring_started:
            print("     ✅ Monitoring started successfully")
            
            # Wait and check status
            await asyncio.sleep(5)
            
            status_response = await self.client.get(f"{self.base_url}/api/v1/patterns/monitoring/status")
            
            if status_response.status_code == 200:
                status = status_response.json()
                
                monitoring_results = {
                    "monitoring_started": True,
                    "service_active": status["service_active"],
                    "monitored_symbols": len(status["monitored_symbols"]),
                    "avg_detection_latency_ms": status["avg_detection_latency_ms"],
                    "performance_acceptable": status["avg_detection_latency_ms"] <= 5000,  # < 5 seconds
                    "status": status
                }
                
                print(f"     ✅ Service active: {status['service_active']}")
                print(f"     ✅ Monitored symbols: {len(status['monitored_symbols'])}")
                print(f"     ✅ Avg latency: {status['avg_detection_latency_ms']:.1f}ms")
            else:
                monitoring_results = {"monitoring_started": True, "status_check_failed": True}
            
            # Stop monitoring
            stop_response = await self.client.post(f"{self.base_url}/api/v1/patterns/monitoring/stop")
            monitoring_results["monitoring_stopped"] = stop_response.status_code == 200
            
        else:
            monitoring_results = {"monitoring_started": False, "error": "Failed to start monitoring"}
        
        return monitoring_results
    
    def _generate_integration_report(self, data_quality, pattern_results, signal_analysis, monitoring_results) -> Dict[str, Any]:
        """Generate comprehensive integration validation report."""
        
        # Calculate overall scores
        data_score = data_quality.get("quality_score", 0)
        pattern_score = 1.0 if pattern_results.get("detection_working", False) else 0.0
        signal_score = signal_analysis.get("signal_quality_score", 0)
        monitoring_score = 1.0 if monitoring_results.get("performance_acceptable", False) else 0.0
        
        overall_score = (data_score + pattern_score + signal_score + monitoring_score) / 4
        
        # Determine if validation passes
        validation_passed = (
            data_score >= 0.8 and
            pattern_score >= 0.8 and
            signal_score >= 0.6 and
            monitoring_score >= 0.8 and
            overall_score >= 0.75
        )
        
        report = {
            "validation_timestamp": datetime.now(timezone.utc).isoformat(),
            "overall_score": overall_score,
            "validation_passed": validation_passed,
            "component_scores": {
                "data_quality": data_score,
                "pattern_detection": pattern_score,
                "signal_quality": signal_score,
                "monitoring_performance": monitoring_score
            },
            "detailed_results": {
                "data_quality": data_quality,
                "pattern_results": pattern_results,
                "signal_analysis": signal_analysis,
                "monitoring_results": monitoring_results
            },
            "recommendations": self._generate_integration_recommendations(
                data_quality, pattern_results, signal_analysis, monitoring_results
            )
        }
        
        # Print summary
        print(f"\n📊 REAL-TIME INTEGRATION SUMMARY:")
        print(f"   Overall Score: {overall_score:.1%}")
        print(f"   Data Quality: {data_score:.1%}")
        print(f"   Pattern Detection: {pattern_score:.1%}")
        print(f"   Signal Quality: {signal_score:.1%}")
        print(f"   Monitoring Performance: {monitoring_score:.1%}")
        print(f"   Validation Passed: {'✅ YES' if validation_passed else '❌ NO'}")
        
        return report
    
    def _generate_integration_recommendations(self, data_quality, pattern_results, signal_analysis, monitoring_results) -> List[str]:
        """Generate recommendations based on integration test results."""
        recommendations = []
        
        if data_quality.get("quality_score", 0) < 0.8:
            recommendations.append("Improve data quality: ensure fresher data and better completeness")
        
        if not pattern_results.get("detection_working", False):
            recommendations.append("Fix pattern detection issues: check detector configurations and thresholds")
        
        if signal_analysis.get("signal_quality_score", 0) < 0.6:
            recommendations.append("Improve signal quality: reduce noise and increase pattern consistency")
        
        if not monitoring_results.get("performance_acceptable", False):
            recommendations.append("Optimize monitoring performance: reduce detection latency")
        
        if not recommendations:
            recommendations.append("All real-time integration tests passed - system ready for production monitoring")
        
        return recommendations


async def run_realtime_integration_validation():
    """Run comprehensive real-time integration validation."""
    tester = RealTimeIntegrationTester()
    return await tester.run_realtime_validation()


if __name__ == "__main__":
    asyncio.run(run_realtime_integration_validation())
