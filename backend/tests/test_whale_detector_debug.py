"""
Debug whale accumulation detector to identify specific issues.
"""

import asyncio
import sys
sys.path.append('/Users/<USER>/ar/backend')

from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any
from app.services.detectors.whale_accumulation_detector import WhaleAccumulationDetector
from app.models.pattern_detection import PatternDetectorConfig


async def debug_whale_detector():
    """Debug whale accumulation detector step by step."""
    print("🐋 WHALE ACCUMULATION DETECTOR DEBUG")
    print("=" * 60)
    
    # Create detector with very sensitive settings
    config = PatternDetectorConfig(
        enabled=True,
        confidence_threshold=0.5,  # Very low threshold
        detection_interval_seconds=300,
        lookback_hours=48  # Longer lookback for whale patterns
    )
    
    detector = WhaleAccumulationDetector(config)
    
    # Further reduce thresholds for debugging
    detector.large_volume_threshold = 1.3  # 1.3x volume
    detector.price_stability_threshold = 0.15  # 15% price deviation allowed
    detector.volume_consistency_threshold = 0.5  # 50% consistency
    detector.min_accumulation_hours = 4  # Shorter minimum duration
    
    print(f"Detector thresholds:")
    print(f"  Large volume threshold: {detector.large_volume_threshold}x")
    print(f"  Price stability threshold: {detector.price_stability_threshold:.1%}")
    print(f"  Volume consistency threshold: {detector.volume_consistency_threshold:.1%}")
    print(f"  Min accumulation hours: {detector.min_accumulation_hours}")
    
    # Create ideal whale accumulation data
    market_data = create_ideal_whale_pattern()
    
    print(f"\nTest data summary:")
    print(f"  Data points: {len(market_data)}")
    print(f"  Time span: {(market_data[-1]['timestamp'] - market_data[0]['timestamp']).total_seconds() / 3600:.1f} hours")
    
    # Show volume and price progression
    volumes = [d['volume'] for d in market_data]
    prices = [d['price'] for d in market_data]
    
    print(f"  Volume range: {min(volumes):,.0f} - {max(volumes):,.0f}")
    print(f"  Price range: ${min(prices):.2f} - ${max(prices):.2f}")
    print(f"  Volume ratio: {max(volumes)/min(volumes):.2f}x")
    print(f"  Price change: {(max(prices)-min(prices))/min(prices):.1%}")
    
    # Test the detector
    try:
        patterns = await detector.detect_patterns(
            symbol="DEBUG_WHALE",
            market_data=market_data,
            context_data=None
        )
        
        if patterns:
            for pattern in patterns:
                print(f"\n✅ WHALE PATTERN DETECTED!")
                print(f"  Confidence: {pattern.confidence_score:.3f}")
                print(f"  Trigger conditions: {pattern.trigger_conditions}")
                print(f"  Pattern data: {pattern.pattern_data}")
        else:
            print(f"\n❌ NO WHALE PATTERN DETECTED")
            
            # Debug step by step
            print(f"\n🔍 DEBUGGING STEP BY STEP:")
            
            # Test accumulation period identification
            periods = detector._identify_accumulation_periods(market_data)
            print(f"  Accumulation periods found: {len(periods)}")
            
            if periods:
                for i, period in enumerate(periods):
                    print(f"    Period {i+1}: {period}")
                    
                    # Test each analysis step
                    period_data = market_data[period['start_idx']:period['end_idx']]
                    
                    transaction_analysis = detector._analyze_large_transactions(period_data)
                    print(f"    Transaction analysis: {transaction_analysis}")
                    
                    stability_analysis = detector._analyze_price_stability(period_data)
                    print(f"    Stability analysis: {stability_analysis}")
                    
                    volume_analysis = detector._analyze_volume_consistency(period_data)
                    print(f"    Volume analysis: {volume_analysis}")

                    # Debug volume analysis in detail
                    volumes = [float(d['volume']) for d in period_data if d.get('volume', 0) > 0]
                    baseline_volume = min(volumes) if volumes else 1.0
                    elevation_threshold = baseline_volume * 1.3
                    elevated_count = sum(1 for v in volumes if v >= elevation_threshold)
                    print(f"      Volumes: {[int(v) for v in volumes[:5]]}... (showing first 5)")
                    print(f"      Baseline volume: {baseline_volume:,.0f}")
                    print(f"      Elevation threshold (1.3x): {elevation_threshold:,.0f}")
                    print(f"      Elevated count: {elevated_count}/{len(volumes)}")
                    print(f"      Consistency ratio: {elevated_count/len(volumes):.2f}")
                    print(f"      Required threshold: {detector.volume_consistency_threshold:.2f}")
                    
                    duration_analysis = detector._analyze_accumulation_duration(period_data)
                    print(f"    Duration analysis: {duration_analysis}")
                    
                    # Calculate confidence manually
                    indicators = {
                        'large_transactions': transaction_analysis['strength'],
                        'price_stability': stability_analysis['stability_score'],
                        'volume_consistency': volume_analysis['consistency_score'],
                        'accumulation_duration': duration_analysis['duration_score'],
                        'wallet_concentration': 0.5  # Default
                    }
                    
                    confidence = detector._calculate_weighted_confidence(indicators)
                    print(f"    Calculated confidence: {confidence:.3f}")
                    print(f"    Meets threshold: {confidence >= detector.config.confidence_threshold}")
            else:
                print(f"    No accumulation periods identified - this is the main issue!")
                
                # Debug period identification
                volumes = [float(d['volume']) for d in market_data if d.get('volume', 0) > 0]
                baseline_volume = sum(volumes[:len(volumes)//2]) / (len(volumes)//2)
                recent_volume = sum(volumes[len(volumes)//2:]) / (len(volumes) - len(volumes)//2)
                
                print(f"    Baseline volume: {baseline_volume:,.0f}")
                print(f"    Recent volume: {recent_volume:,.0f}")
                print(f"    Volume ratio: {recent_volume/baseline_volume:.2f}x")
                print(f"    Required ratio: 1.5x")
                
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()


def create_ideal_whale_pattern() -> List[Dict[str, Any]]:
    """Create ideal whale accumulation pattern data."""
    base_price = 50000.0  # $50k base price
    base_volume = 500000.0  # 500k base volume
    
    market_data = []
    
    # 36 hours of data with clear whale accumulation
    for i in range(36):
        timestamp = datetime.now(timezone.utc) - timedelta(hours=35-i)
        
        # First 12 hours: normal activity
        if i < 12:
            volume_multiplier = 1.0
            price_multiplier = 1.0
        
        # Next 24 hours: clear accumulation pattern
        else:
            # Sustained 2.5x volume increase (stronger signal)
            volume_multiplier = 2.5 + 0.3 * ((i - 12) % 4)  # 2.5x to 3.7x with variation

            # Very stable price (max 3% deviation)
            price_variation = 0.03 * ((i % 8) - 4) / 4  # ±3% variation
            price_multiplier = 1.0 + price_variation
        
        price = base_price * price_multiplier
        volume = base_volume * volume_multiplier
        
        data_point = {
            'timestamp': timestamp,
            'symbol': 'DEBUG_WHALE',
            'exchange': 'debug_exchange',
            'price': price,
            'volume': volume,
            'market_cap': price * 1000000,
            'source': 'debug_data',
            'data_hash': f'debug_hash_{i}'
        }
        
        market_data.append(data_point)
    
    return market_data


if __name__ == "__main__":
    asyncio.run(debug_whale_detector())
