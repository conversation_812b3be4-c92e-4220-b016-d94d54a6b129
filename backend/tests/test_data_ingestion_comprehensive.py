"""
Comprehensive test suite for data ingestion system.
Tests all components to ensure 99.9th percentile quality standards.
"""

import asyncio
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Any
import pytest
import httpx
import aiohttp
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.data_ingestion import data_ingestion_service
from app.db.deps import get_db_session
from app.core.config import settings


class TestDataIngestionComprehensive:
    """Comprehensive test suite for data ingestion system."""
    
    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """Setup and teardown for each test."""
        # Setup
        self.base_url = "http://localhost:8000"
        self.client = httpx.AsyncClient(timeout=30.0)
        
        yield
        
        # Teardown
        await self.client.aclose()
    
    # ============================================================================
    # 1.1 API ENDPOINTS VALIDATION TESTING
    # ============================================================================
    
    async def test_health_endpoint(self):
        """Test health endpoint availability and response format."""
        response = await self.client.get(f"{self.base_url}/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "service" in data
        assert "version" in data
        assert "timestamp" in data
        assert data["status"] == "healthy"
    
    async def test_data_status_endpoint(self):
        """Test data ingestion status endpoint."""
        response = await self.client.get(f"{self.base_url}/api/v1/data/status")
        assert response.status_code == 200
        
        data = response.json()
        required_fields = ["service_running", "session_active", "db_connected", "timestamp"]
        for field in required_fields:
            assert field in data
        
        assert isinstance(data["service_running"], bool)
        assert isinstance(data["session_active"], bool)
        assert isinstance(data["db_connected"], bool)
    
    async def test_data_stats_endpoint(self):
        """Test data statistics endpoint."""
        response = await self.client.get(f"{self.base_url}/api/v1/data/stats")
        assert response.status_code == 200
        
        data = response.json()
        required_fields = ["total_records", "records_by_source", "records_by_symbol", "validation_stats"]
        for field in required_fields:
            assert field in data
        
        assert isinstance(data["total_records"], int)
        assert isinstance(data["records_by_source"], dict)
        assert isinstance(data["records_by_symbol"], dict)
    
    async def test_data_ingest_endpoint_valid_input(self):
        """Test data ingestion endpoint with valid inputs."""
        test_cases = [
            {
                "symbols": ["bitcoin"],
                "sources": ["coingecko"],
                "continuous": False
            },
            {
                "symbols": ["bitcoin", "ethereum"],
                "sources": ["coingecko"],
                "continuous": False
            },
            {
                "symbols": ["bitcoin"],
                "sources": ["coingecko", "binance"],
                "continuous": False
            }
        ]
        
        for test_case in test_cases:
            response = await self.client.post(
                f"{self.base_url}/api/v1/data/ingest",
                json=test_case
            )
            assert response.status_code == 200
            
            data = response.json()
            assert data["status"] == "success"
            assert "symbols_requested" in data
            assert "sources_used" in data
            assert "results" in data
            assert "timestamp" in data
    
    async def test_data_ingest_endpoint_invalid_input(self):
        """Test data ingestion endpoint with invalid inputs."""
        invalid_cases = [
            {},  # Empty request
            {"symbols": []},  # Empty symbols
            {"symbols": ["invalid_symbol"], "sources": ["invalid_source"]},  # Invalid source
            {"symbols": ["bitcoin"], "sources": []},  # Empty sources
            {"symbols": "not_a_list"},  # Invalid type
        ]
        
        for invalid_case in invalid_cases:
            response = await self.client.post(
                f"{self.base_url}/api/v1/data/ingest",
                json=invalid_case
            )
            # Should return 422 for validation errors or handle gracefully
            assert response.status_code in [400, 422, 500]
    
    async def test_data_validation_endpoint(self):
        """Test data validation endpoint."""
        test_data = {
            "data": {
                "symbol": "BITCOIN",
                "price": 108233.0,
                "timestamp": "2025-07-05T11:45:36.361241Z",
                "volume": 20926799422.063057
            }
        }
        
        response = await self.client.post(
            f"{self.base_url}/api/v1/data/validate",
            json=test_data
        )
        assert response.status_code == 200
        
        data = response.json()
        required_fields = ["is_valid", "confidence_score", "validation_timestamp"]
        for field in required_fields:
            assert field in data
        
        assert isinstance(data["is_valid"], bool)
        assert isinstance(data["confidence_score"], (int, float))
        assert 0.0 <= data["confidence_score"] <= 1.0
    
    async def test_recent_data_endpoint(self):
        """Test recent data retrieval endpoint."""
        # Test without parameters
        response = await self.client.get(f"{self.base_url}/api/v1/data/recent-data")
        assert response.status_code == 200
        
        data = response.json()
        assert "count" in data
        assert "data" in data
        assert isinstance(data["data"], list)
        
        # Test with parameters
        response = await self.client.get(
            f"{self.base_url}/api/v1/data/recent-data?limit=5&symbol=BITCOIN"
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["count"] <= 5
        if data["data"]:
            for record in data["data"]:
                assert "timestamp" in record
                assert "symbol" in record
                assert "price" in record
                assert "data_hash" in record
    
    # ============================================================================
    # 1.2 MULTI-SOURCE DATA INTEGRATION TESTING
    # ============================================================================
    
    async def test_coingecko_api_integration(self):
        """Test CoinGecko API integration with multiple symbols."""
        symbols_to_test = ["bitcoin", "ethereum", "cardano", "solana", "chainlink"]
        
        # Start the service
        await data_ingestion_service.start()
        
        try:
            for symbol in symbols_to_test:
                result = await data_ingestion_service.ingest_coingecko_data([symbol])
                
                # Verify successful data collection
                assert result["success"] >= 0
                assert result["failed"] == 0
                assert len(result["errors"]) == 0
                
                print(f"✅ CoinGecko API test passed for {symbol}: {result}")
                
                # Rate limiting - wait between requests
                await asyncio.sleep(1)
        
        finally:
            await data_ingestion_service.stop()
    
    async def test_binance_websocket_integration(self):
        """Test Binance WebSocket integration for extended operation."""
        print("🔄 Starting Binance WebSocket extended test (60 seconds)...")
        
        # Start continuous ingestion
        response = await self.client.post(
            f"{self.base_url}/api/v1/data/ingest",
            json={
                "symbols": ["bitcoin", "ethereum"],
                "sources": ["binance"],
                "continuous": True
            }
        )
        assert response.status_code == 200
        
        # Monitor for 60 seconds
        start_time = time.time()
        initial_stats = await self._get_data_stats()
        
        while time.time() - start_time < 60:
            await asyncio.sleep(10)
            current_stats = await self._get_data_stats()
            
            # Check if new data is being collected
            print(f"📊 Records: {current_stats['total_records']} (was {initial_stats['total_records']})")
            
            # Verify service is still running
            status_response = await self.client.get(f"{self.base_url}/api/v1/data/status")
            status_data = status_response.json()
            assert status_data["service_running"] == True
            assert status_data["session_active"] == True
        
        print("✅ Binance WebSocket extended test completed successfully")
    
    async def _get_data_stats(self) -> Dict[str, Any]:
        """Helper method to get current data statistics."""
        response = await self.client.get(f"{self.base_url}/api/v1/data/stats")
        return response.json()
    
    # ============================================================================
    # 1.3 DATABASE INTEGRATION & PERFORMANCE TESTING
    # ============================================================================
    
    async def test_database_connection_and_operations(self):
        """Test database connection and basic operations."""
        db = await get_db_session()
        
        try:
            # Test basic query
            result = await db.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            assert row[0] == 1
            
            # Test TimescaleDB extension
            result = await db.execute(text("SELECT extname FROM pg_extension WHERE extname = 'timescaledb'"))
            row = result.fetchone()
            assert row is not None
            
            # Test market_data table exists
            result = await db.execute(text("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_name = 'market_data'
            """))
            row = result.fetchone()
            assert row is not None
            
            print("✅ Database connection and operations test passed")
            
        finally:
            await db.close()
    
    async def test_data_storage_and_retrieval(self):
        """Test data storage and retrieval operations."""
        # Insert test data
        test_data = {
            "symbols": ["bitcoin"],
            "sources": ["coingecko"],
            "continuous": False
        }
        
        # Get initial count
        initial_stats = await self._get_data_stats()
        initial_count = initial_stats["total_records"]
        
        # Trigger data ingestion
        response = await self.client.post(
            f"{self.base_url}/api/v1/data/ingest",
            json=test_data
        )
        assert response.status_code == 200
        
        # Wait for data to be stored
        await asyncio.sleep(2)
        
        # Verify data was stored
        final_stats = await self._get_data_stats()
        final_count = final_stats["total_records"]
        
        assert final_count > initial_count
        print(f"✅ Data storage test passed: {initial_count} -> {final_count} records")
        
        # Test data retrieval
        response = await self.client.get(f"{self.base_url}/api/v1/data/recent-data?limit=1")
        assert response.status_code == 200
        
        data = response.json()
        assert data["count"] >= 1
        assert len(data["data"]) >= 1
        
        # Verify data structure
        record = data["data"][0]
        required_fields = ["timestamp", "symbol", "price", "volume", "source", "data_hash"]
        for field in required_fields:
            assert field in record
        
        print("✅ Data retrieval test passed")


# ============================================================================
# TEST EXECUTION HELPERS
# ============================================================================

async def run_comprehensive_tests():
    """Run all comprehensive tests."""
    test_instance = TestDataIngestionComprehensive()
    await test_instance.setup_and_teardown().__anext__()
    
    print("🚀 Starting Comprehensive Data Ingestion Testing...")
    print("=" * 80)
    
    try:
        # 1.1 API Endpoints Testing
        print("\n📋 1.1 API ENDPOINTS VALIDATION TESTING")
        print("-" * 50)
        
        await test_instance.test_health_endpoint()
        print("✅ Health endpoint test passed")
        
        await test_instance.test_data_status_endpoint()
        print("✅ Data status endpoint test passed")
        
        await test_instance.test_data_stats_endpoint()
        print("✅ Data stats endpoint test passed")
        
        await test_instance.test_data_ingest_endpoint_valid_input()
        print("✅ Data ingest valid input test passed")
        
        await test_instance.test_data_ingest_endpoint_invalid_input()
        print("✅ Data ingest invalid input test passed")
        
        await test_instance.test_data_validation_endpoint()
        print("✅ Data validation endpoint test passed")
        
        await test_instance.test_recent_data_endpoint()
        print("✅ Recent data endpoint test passed")
        
        # 1.2 Multi-Source Integration Testing
        print("\n🔗 1.2 MULTI-SOURCE DATA INTEGRATION TESTING")
        print("-" * 50)
        
        await test_instance.test_coingecko_api_integration()
        print("✅ CoinGecko API integration test passed")
        
        # 1.3 Database Integration Testing
        print("\n🗄️ 1.3 DATABASE INTEGRATION TESTING")
        print("-" * 50)
        
        await test_instance.test_database_connection_and_operations()
        print("✅ Database connection test passed")
        
        await test_instance.test_data_storage_and_retrieval()
        print("✅ Data storage and retrieval test passed")
        
        print("\n" + "=" * 80)
        print("🎉 ALL COMPREHENSIVE TESTS PASSED!")
        print("✅ System ready for production workloads")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        raise
    
    finally:
        await test_instance.client.aclose()


if __name__ == "__main__":
    asyncio.run(run_comprehensive_tests())
