"""
Production Monitoring System Validation Testing.
Comprehensive testing of production monitoring features including
real-time pattern detection, alert system, and performance tracking.
"""

import asyncio
import sys
sys.path.append('/Users/<USER>/ar/backend')

import httpx
import time
import json
from datetime import datetime, timezone
from typing import Dict, List, Any


class ProductionMonitoringTester:
    """Comprehensive production monitoring system tester."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.client = httpx.AsyncClient(timeout=120.0)
        
    async def run_comprehensive_monitoring_test(self) -> Dict[str, Any]:
        """Run comprehensive production monitoring validation."""
        print("🚀 PRODUCTION MONITORING SYSTEM VALIDATION")
        print("=" * 80)
        
        try:
            # Test 1: System Health Check
            print("\n🏥 Testing system health check...")
            health_results = await self._test_system_health()
            
            # Test 2: Monitoring Lifecycle
            print("\n🔄 Testing monitoring lifecycle (start/stop)...")
            lifecycle_results = await self._test_monitoring_lifecycle()
            
            # Test 3: Alert Configuration
            print("\n🚨 Testing alert configuration...")
            alert_config_results = await self._test_alert_configuration()
            
            # Test 4: Real-time Monitoring
            print("\n⚡ Testing real-time monitoring...")
            realtime_results = await self._test_realtime_monitoring()
            
            # Test 5: Performance Monitoring
            print("\n📊 Testing performance monitoring...")
            performance_results = await self._test_performance_monitoring()
            
            # Generate comprehensive report
            report = self._generate_monitoring_report(
                health_results, lifecycle_results, alert_config_results,
                realtime_results, performance_results
            )
            
            print("\n" + "=" * 80)
            print("🎉 PRODUCTION MONITORING VALIDATION COMPLETED!")
            print("=" * 80)
            
            return report
            
        except Exception as e:
            print(f"❌ Production monitoring validation failed: {e}")
            raise
        finally:
            await self.client.aclose()
    
    async def _test_system_health(self) -> Dict[str, Any]:
        """Test system health check functionality."""
        print("   Testing health endpoint...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/monitoring/health")
            
            if response.status_code == 200:
                health_data = response.json()
                
                health_results = {
                    "endpoint_accessible": True,
                    "response_time_ms": response.elapsed.total_seconds() * 1000,
                    "health_status": health_data.get("status"),
                    "components": health_data.get("components", {}),
                    "monitoring_active": health_data.get("monitoring_active", False),
                    "test_passed": health_data.get("status") in ["healthy", "inactive"]
                }
                
                print(f"   ✅ Health endpoint accessible")
                print(f"   ✅ Response time: {health_results['response_time_ms']:.1f}ms")
                print(f"   ✅ Health status: {health_results['health_status']}")
                
                return health_results
            else:
                return {"endpoint_accessible": False, "status_code": response.status_code, "test_passed": False}
                
        except Exception as e:
            print(f"   ❌ Health check failed: {e}")
            return {"endpoint_accessible": False, "error": str(e), "test_passed": False}
    
    async def _test_monitoring_lifecycle(self) -> Dict[str, Any]:
        """Test monitoring start/stop lifecycle."""
        print("   Testing monitoring start/stop lifecycle...")
        
        lifecycle_results = {
            "start_test": {"success": False},
            "status_test": {"success": False},
            "stop_test": {"success": False},
            "test_passed": False
        }
        
        try:
            # Test start monitoring
            start_response = await self.client.post(
                f"{self.base_url}/api/v1/monitoring/start",
                json={"monitoring_interval_seconds": 60}  # Faster for testing
            )
            
            if start_response.status_code == 200:
                start_data = start_response.json()
                lifecycle_results["start_test"] = {
                    "success": True,
                    "status": start_data.get("status"),
                    "symbols_count": len(start_data.get("symbols", [])),
                    "monitoring_interval": start_data.get("monitoring_interval")
                }
                print(f"   ✅ Monitoring started successfully")
                print(f"   ✅ Monitoring {lifecycle_results['start_test']['symbols_count']} symbols")
            
            # Wait a moment for monitoring to initialize
            await asyncio.sleep(2)
            
            # Test status check
            status_response = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                lifecycle_results["status_test"] = {
                    "success": True,
                    "monitoring_active": status_data.get("monitoring_active"),
                    "uptime_seconds": status_data.get("uptime_seconds"),
                    "symbols_monitored": len(status_data.get("monitored_symbols", [])),
                    "detection_cycles": status_data.get("session_statistics", {}).get("detection_cycles_completed", 0)
                }
                print(f"   ✅ Status check successful")
                print(f"   ✅ Uptime: {lifecycle_results['status_test']['uptime_seconds']:.1f}s")
            
            # Test stop monitoring
            stop_response = await self.client.post(f"{self.base_url}/api/v1/monitoring/stop")
            
            if stop_response.status_code == 200:
                stop_data = stop_response.json()
                lifecycle_results["stop_test"] = {
                    "success": True,
                    "status": stop_data.get("status"),
                    "session_summary": stop_data.get("session_summary", {})
                }
                print(f"   ✅ Monitoring stopped successfully")
                
                # Check session summary
                summary = lifecycle_results["stop_test"]["session_summary"]
                if summary:
                    print(f"   ✅ Session uptime: {summary.get('uptime_hours', 0):.2f} hours")
                    print(f"   ✅ Detection cycles: {summary.get('detection_cycles_completed', 0)}")
            
            # Overall test success
            lifecycle_results["test_passed"] = (
                lifecycle_results["start_test"]["success"] and
                lifecycle_results["status_test"]["success"] and
                lifecycle_results["stop_test"]["success"]
            )
            
        except Exception as e:
            print(f"   ❌ Lifecycle test failed: {e}")
            lifecycle_results["error"] = str(e)
        
        return lifecycle_results
    
    async def _test_alert_configuration(self) -> Dict[str, Any]:
        """Test alert configuration functionality."""
        print("   Testing alert configuration...")
        
        alert_results = {
            "get_config_test": {"success": False},
            "update_config_test": {"success": False},
            "test_passed": False
        }
        
        try:
            # Test get alert configuration
            config_response = await self.client.get(f"{self.base_url}/api/v1/monitoring/alerts/config")
            
            if config_response.status_code == 200:
                config_data = config_response.json()
                alert_levels = config_data.get("alert_levels", {})
                
                alert_results["get_config_test"] = {
                    "success": True,
                    "alert_levels_count": len(alert_levels),
                    "critical_threshold": alert_levels.get("critical", {}).get("confidence_threshold"),
                    "high_threshold": alert_levels.get("high", {}).get("confidence_threshold"),
                    "medium_threshold": alert_levels.get("medium", {}).get("confidence_threshold")
                }
                print(f"   ✅ Alert configuration retrieved")
                print(f"   ✅ Alert levels: {list(alert_levels.keys())}")
            
            # Test update alert configuration
            update_response = await self.client.put(
                f"{self.base_url}/api/v1/monitoring/alerts/config",
                json={
                    "alert_level": "medium",
                    "confidence_threshold": 0.65,
                    "enabled": True,
                    "cooldown_minutes": 10
                }
            )
            
            if update_response.status_code == 200:
                update_data = update_response.json()
                alert_results["update_config_test"] = {
                    "success": True,
                    "updated_level": update_data.get("alert_level"),
                    "new_threshold": update_data.get("new_configuration", {}).get("confidence_threshold")
                }
                print(f"   ✅ Alert configuration updated")
                print(f"   ✅ Medium threshold: {alert_results['update_config_test']['new_threshold']}")
            
            alert_results["test_passed"] = (
                alert_results["get_config_test"]["success"] and
                alert_results["update_config_test"]["success"]
            )
            
        except Exception as e:
            print(f"   ❌ Alert configuration test failed: {e}")
            alert_results["error"] = str(e)
        
        return alert_results
    
    async def _test_realtime_monitoring(self) -> Dict[str, Any]:
        """Test real-time monitoring functionality."""
        print("   Testing real-time monitoring with data collection...")
        
        realtime_results = {
            "data_collection": {"success": False},
            "monitoring_session": {"success": False},
            "pattern_detection": {"success": False},
            "test_passed": False
        }
        
        try:
            # First, collect some market data
            data_response = await self.client.post(
                f"{self.base_url}/api/v1/data/ingest",
                json={
                    "symbols": ["bitcoin", "ethereum"],
                    "sources": ["coingecko"],
                    "continuous": False
                }
            )
            
            if data_response.status_code == 200:
                data_result = data_response.json()
                realtime_results["data_collection"] = {
                    "success": True,
                    "symbols_collected": len(data_result.get("symbols_requested", [])),
                    "coingecko_success": data_result.get("results", {}).get("coingecko", {}).get("success", 0)
                }
                print(f"   ✅ Market data collected")
            
            # Start monitoring for a short period
            start_response = await self.client.post(
                f"{self.base_url}/api/v1/monitoring/start",
                json={
                    "symbols": ["bitcoin", "ethereum"],
                    "monitoring_interval_seconds": 30  # Very fast for testing
                }
            )
            
            if start_response.status_code == 200:
                print(f"   ✅ Real-time monitoring started")
                
                # Let it run for a short period
                await asyncio.sleep(5)
                
                # Check status during monitoring
                status_response = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    realtime_results["monitoring_session"] = {
                        "success": True,
                        "monitoring_active": status_data.get("monitoring_active"),
                        "symbols_monitored": len(status_data.get("monitored_symbols", [])),
                        "detection_cycles": status_data.get("session_statistics", {}).get("detection_cycles_completed", 0),
                        "patterns_detected": status_data.get("session_statistics", {}).get("total_patterns_detected", 0)
                    }
                    print(f"   ✅ Monitoring session active")
                    print(f"   ✅ Detection cycles: {realtime_results['monitoring_session']['detection_cycles']}")
                
                # Stop monitoring
                await self.client.post(f"{self.base_url}/api/v1/monitoring/stop")
                print(f"   ✅ Monitoring stopped")
            
            # Test pattern detection capability
            pattern_response = await self.client.post(
                f"{self.base_url}/api/v1/patterns/analyze",
                json={
                    "symbols": ["bitcoin"],
                    "lookback_hours": 1,
                    "min_confidence": 0.5,
                    "real_time": False
                }
            )
            
            if pattern_response.status_code == 200:
                pattern_data = pattern_response.json()
                realtime_results["pattern_detection"] = {
                    "success": True,
                    "data_points_analyzed": pattern_data.get("data_points_analyzed", 0),
                    "patterns_detected": pattern_data.get("total_patterns", 0),
                    "analysis_time_ms": pattern_data.get("analysis_duration_ms", 0)
                }
                print(f"   ✅ Pattern detection working")
                print(f"   ✅ Data points analyzed: {realtime_results['pattern_detection']['data_points_analyzed']}")
            
            realtime_results["test_passed"] = (
                realtime_results["data_collection"]["success"] and
                realtime_results["monitoring_session"]["success"] and
                realtime_results["pattern_detection"]["success"]
            )
            
        except Exception as e:
            print(f"   ❌ Real-time monitoring test failed: {e}")
            realtime_results["error"] = str(e)
        
        return realtime_results
    
    async def _test_performance_monitoring(self) -> Dict[str, Any]:
        """Test performance monitoring functionality."""
        print("   Testing performance monitoring...")
        
        performance_results = {
            "metrics_test": {"success": False},
            "performance_summary_test": {"success": False},
            "test_passed": False
        }
        
        try:
            # Test metrics endpoint
            metrics_response = await self.client.get(
                f"{self.base_url}/api/v1/monitoring/metrics?hours_back=1"
            )
            
            if metrics_response.status_code == 200:
                metrics_data = metrics_response.json()
                performance_results["metrics_test"] = {
                    "success": True,
                    "metrics_available": metrics_data.get("metrics_count", 0) > 0,
                    "time_range_hours": metrics_data.get("time_range", {}).get("hours_back"),
                    "has_aggregated_stats": "aggregated_statistics" in metrics_data
                }
                print(f"   ✅ Metrics endpoint accessible")
            
            # Test performance summary
            perf_response = await self.client.get(f"{self.base_url}/api/v1/monitoring/performance")
            
            if perf_response.status_code == 200:
                perf_data = perf_response.json()
                performance_results["performance_summary_test"] = {
                    "success": True,
                    "status": perf_data.get("status"),
                    "health_score": perf_data.get("health_score"),
                    "has_recommendations": len(perf_data.get("recommendations", [])) > 0,
                    "performance_indicators": perf_data.get("performance_indicators", {})
                }
                print(f"   ✅ Performance summary available")
                if perf_data.get("health_score"):
                    print(f"   ✅ Health score: {perf_data['health_score']}")
            
            performance_results["test_passed"] = (
                performance_results["metrics_test"]["success"] and
                performance_results["performance_summary_test"]["success"]
            )
            
        except Exception as e:
            print(f"   ❌ Performance monitoring test failed: {e}")
            performance_results["error"] = str(e)
        
        return performance_results
    
    def _generate_monitoring_report(self, health_results, lifecycle_results, 
                                  alert_config_results, realtime_results, 
                                  performance_results) -> Dict[str, Any]:
        """Generate comprehensive monitoring validation report."""
        
        # Calculate overall scores
        test_scores = {
            "system_health": 1.0 if health_results.get("test_passed", False) else 0.0,
            "monitoring_lifecycle": 1.0 if lifecycle_results.get("test_passed", False) else 0.0,
            "alert_configuration": 1.0 if alert_config_results.get("test_passed", False) else 0.0,
            "realtime_monitoring": 1.0 if realtime_results.get("test_passed", False) else 0.0,
            "performance_monitoring": 1.0 if performance_results.get("test_passed", False) else 0.0
        }
        
        overall_score = sum(test_scores.values()) / len(test_scores)
        
        # Determine validation status
        validation_passed = overall_score >= 0.8  # 80% of tests must pass
        
        report = {
            "validation_timestamp": datetime.now(timezone.utc).isoformat(),
            "overall_score": overall_score,
            "validation_passed": validation_passed,
            "test_scores": test_scores,
            "detailed_results": {
                "system_health": health_results,
                "monitoring_lifecycle": lifecycle_results,
                "alert_configuration": alert_config_results,
                "realtime_monitoring": realtime_results,
                "performance_monitoring": performance_results
            },
            "production_readiness": {
                "monitoring_system": "ready" if overall_score >= 0.8 else "needs_work",
                "alert_system": "ready" if alert_config_results.get("test_passed", False) else "needs_work",
                "performance_tracking": "ready" if performance_results.get("test_passed", False) else "needs_work",
                "real_time_capability": "ready" if realtime_results.get("test_passed", False) else "needs_work"
            }
        }
        
        # Print summary
        print(f"\n📊 PRODUCTION MONITORING VALIDATION SUMMARY:")
        print(f"   Overall Score: {overall_score:.1%}")
        print(f"   System Health: {'✅ PASS' if test_scores['system_health'] == 1.0 else '❌ FAIL'}")
        print(f"   Monitoring Lifecycle: {'✅ PASS' if test_scores['monitoring_lifecycle'] == 1.0 else '❌ FAIL'}")
        print(f"   Alert Configuration: {'✅ PASS' if test_scores['alert_configuration'] == 1.0 else '❌ FAIL'}")
        print(f"   Real-time Monitoring: {'✅ PASS' if test_scores['realtime_monitoring'] == 1.0 else '❌ FAIL'}")
        print(f"   Performance Monitoring: {'✅ PASS' if test_scores['performance_monitoring'] == 1.0 else '❌ FAIL'}")
        print(f"   Production Ready: {'✅ YES' if validation_passed else '❌ NO'}")
        
        return report


async def run_production_monitoring_validation():
    """Run comprehensive production monitoring validation."""
    tester = ProductionMonitoringTester()
    return await tester.run_comprehensive_monitoring_test()


if __name__ == "__main__":
    asyncio.run(run_production_monitoring_validation())
