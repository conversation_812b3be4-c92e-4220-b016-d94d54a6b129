"""
Production Performance Testing and Load Validation.
Tests production monitoring system under load with performance benchmarks,
memory usage tracking, and scalability validation.
"""

import asyncio
import sys
sys.path.append('/Users/<USER>/ar/backend')

import httpx
import time
import statistics
from datetime import datetime, timezone
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor


class ProductionPerformanceTester:
    """Production performance and load testing for monitoring system."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.client = httpx.AsyncClient(timeout=120.0)
        
    async def run_performance_validation(self) -> Dict[str, Any]:
        """Run comprehensive production performance validation."""
        print("⚡ PRODUCTION PERFORMANCE VALIDATION")
        print("=" * 80)
        
        try:
            # Test 1: Baseline Performance
            print("\n📊 Testing baseline performance...")
            baseline_results = await self._test_baseline_performance()
            
            # Test 2: Load Testing
            print("\n🔥 Testing under load...")
            load_results = await self._test_load_performance()
            
            # Test 3: Memory Usage Monitoring
            print("\n💾 Testing memory usage patterns...")
            memory_results = await self._test_memory_usage()
            
            # Test 4: Concurrent Operations
            print("\n🔄 Testing concurrent operations...")
            concurrency_results = await self._test_concurrent_operations()
            
            # Test 5: Long-running Stability
            print("\n⏱️ Testing long-running stability...")
            stability_results = await self._test_long_running_stability()
            
            # Generate performance report
            report = self._generate_performance_report(
                baseline_results, load_results, memory_results,
                concurrency_results, stability_results
            )
            
            print("\n" + "=" * 80)
            print("🎉 PRODUCTION PERFORMANCE VALIDATION COMPLETED!")
            print("=" * 80)
            
            return report
            
        except Exception as e:
            print(f"❌ Performance validation failed: {e}")
            raise
        finally:
            await self.client.aclose()
    
    async def _test_baseline_performance(self) -> Dict[str, Any]:
        """Test baseline performance metrics."""
        print("   Measuring baseline API response times...")
        
        baseline_results = {
            "health_check": {"times": [], "avg_ms": 0, "success_rate": 0},
            "status_check": {"times": [], "avg_ms": 0, "success_rate": 0},
            "pattern_analysis": {"times": [], "avg_ms": 0, "success_rate": 0},
            "data_ingestion": {"times": [], "avg_ms": 0, "success_rate": 0},
            "test_passed": False
        }
        
        # Test each endpoint multiple times
        test_iterations = 10
        
        try:
            # Health check performance
            for i in range(test_iterations):
                start_time = time.time()
                response = await self.client.get(f"{self.base_url}/api/v1/monitoring/health")
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000
                baseline_results["health_check"]["times"].append(response_time)
                
                if response.status_code == 200:
                    baseline_results["health_check"]["success_rate"] += 1
            
            baseline_results["health_check"]["avg_ms"] = statistics.mean(baseline_results["health_check"]["times"])
            baseline_results["health_check"]["success_rate"] = baseline_results["health_check"]["success_rate"] / test_iterations
            
            # Status check performance (start monitoring first)
            await self.client.post(f"{self.base_url}/api/v1/monitoring/start", json={})
            
            for i in range(test_iterations):
                start_time = time.time()
                response = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000
                baseline_results["status_check"]["times"].append(response_time)
                
                if response.status_code == 200:
                    baseline_results["status_check"]["success_rate"] += 1
            
            baseline_results["status_check"]["avg_ms"] = statistics.mean(baseline_results["status_check"]["times"])
            baseline_results["status_check"]["success_rate"] = baseline_results["status_check"]["success_rate"] / test_iterations
            
            # Pattern analysis performance
            for i in range(5):  # Fewer iterations for heavier operations
                start_time = time.time()
                response = await self.client.post(
                    f"{self.base_url}/api/v1/patterns/analyze",
                    json={"symbols": ["bitcoin"], "lookback_hours": 1, "min_confidence": 0.5}
                )
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000
                baseline_results["pattern_analysis"]["times"].append(response_time)
                
                if response.status_code == 200:
                    baseline_results["pattern_analysis"]["success_rate"] += 1
            
            baseline_results["pattern_analysis"]["avg_ms"] = statistics.mean(baseline_results["pattern_analysis"]["times"])
            baseline_results["pattern_analysis"]["success_rate"] = baseline_results["pattern_analysis"]["success_rate"] / 5
            
            # Data ingestion performance
            for i in range(3):  # Even fewer for data ingestion
                start_time = time.time()
                response = await self.client.post(
                    f"{self.base_url}/api/v1/data/ingest",
                    json={"symbols": ["bitcoin"], "sources": ["coingecko"], "continuous": False}
                )
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000
                baseline_results["data_ingestion"]["times"].append(response_time)
                
                if response.status_code == 200:
                    baseline_results["data_ingestion"]["success_rate"] += 1
            
            baseline_results["data_ingestion"]["avg_ms"] = statistics.mean(baseline_results["data_ingestion"]["times"])
            baseline_results["data_ingestion"]["success_rate"] = baseline_results["data_ingestion"]["success_rate"] / 3
            
            # Stop monitoring
            await self.client.post(f"{self.base_url}/api/v1/monitoring/stop")
            
            # Evaluate performance
            baseline_results["test_passed"] = (
                baseline_results["health_check"]["avg_ms"] < 100 and  # < 100ms
                baseline_results["status_check"]["avg_ms"] < 200 and  # < 200ms
                baseline_results["pattern_analysis"]["avg_ms"] < 2000 and  # < 2s
                baseline_results["data_ingestion"]["avg_ms"] < 5000 and  # < 5s
                all(result["success_rate"] >= 0.9 for result in baseline_results.values() if isinstance(result, dict) and "success_rate" in result)
            )
            
            print(f"   ✅ Health check: {baseline_results['health_check']['avg_ms']:.1f}ms avg")
            print(f"   ✅ Status check: {baseline_results['status_check']['avg_ms']:.1f}ms avg")
            print(f"   ✅ Pattern analysis: {baseline_results['pattern_analysis']['avg_ms']:.1f}ms avg")
            print(f"   ✅ Data ingestion: {baseline_results['data_ingestion']['avg_ms']:.1f}ms avg")
            
        except Exception as e:
            print(f"   ❌ Baseline performance test failed: {e}")
            baseline_results["error"] = str(e)
        
        return baseline_results
    
    async def _test_load_performance(self) -> Dict[str, Any]:
        """Test performance under load."""
        print("   Testing API performance under concurrent load...")
        
        load_results = {
            "concurrent_requests": 0,
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_response_time_ms": 0,
            "max_response_time_ms": 0,
            "min_response_time_ms": 0,
            "requests_per_second": 0,
            "test_passed": False
        }
        
        try:
            # Start monitoring
            await self.client.post(f"{self.base_url}/api/v1/monitoring/start", json={})
            
            # Concurrent load test
            concurrent_requests = 20
            total_requests = 100
            
            async def make_request():
                start_time = time.time()
                try:
                    response = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
                    end_time = time.time()
                    return {
                        "success": response.status_code == 200,
                        "response_time": (end_time - start_time) * 1000,
                        "status_code": response.status_code
                    }
                except Exception as e:
                    end_time = time.time()
                    return {
                        "success": False,
                        "response_time": (end_time - start_time) * 1000,
                        "error": str(e)
                    }
            
            # Execute concurrent requests
            start_time = time.time()
            
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(concurrent_requests)
            
            async def limited_request():
                async with semaphore:
                    return await make_request()
            
            # Execute all requests
            tasks = [limited_request() for _ in range(total_requests)]
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_duration = end_time - start_time
            
            # Analyze results
            successful_results = [r for r in results if r["success"]]
            failed_results = [r for r in results if not r["success"]]
            
            response_times = [r["response_time"] for r in results]
            
            load_results.update({
                "concurrent_requests": concurrent_requests,
                "total_requests": total_requests,
                "successful_requests": len(successful_results),
                "failed_requests": len(failed_results),
                "avg_response_time_ms": statistics.mean(response_times) if response_times else 0,
                "max_response_time_ms": max(response_times) if response_times else 0,
                "min_response_time_ms": min(response_times) if response_times else 0,
                "requests_per_second": total_requests / total_duration,
                "success_rate": len(successful_results) / total_requests
            })
            
            # Stop monitoring
            await self.client.post(f"{self.base_url}/api/v1/monitoring/stop")
            
            # Evaluate load performance
            load_results["test_passed"] = (
                load_results["success_rate"] >= 0.95 and  # 95% success rate
                load_results["avg_response_time_ms"] < 1000 and  # < 1s average
                load_results["requests_per_second"] >= 10  # At least 10 RPS
            )
            
            print(f"   ✅ Concurrent requests: {concurrent_requests}")
            print(f"   ✅ Success rate: {load_results['success_rate']:.1%}")
            print(f"   ✅ Avg response time: {load_results['avg_response_time_ms']:.1f}ms")
            print(f"   ✅ Requests per second: {load_results['requests_per_second']:.1f}")
            
        except Exception as e:
            print(f"   ❌ Load performance test failed: {e}")
            load_results["error"] = str(e)
        
        return load_results
    
    async def _test_memory_usage(self) -> Dict[str, Any]:
        """Test memory usage patterns during monitoring."""
        print("   Testing memory usage during extended monitoring...")
        
        memory_results = {
            "initial_memory_mb": 0,
            "peak_memory_mb": 0,
            "final_memory_mb": 0,
            "memory_growth_mb": 0,
            "memory_stable": False,
            "test_passed": False
        }
        
        try:
            # Start monitoring
            await self.client.post(f"{self.base_url}/api/v1/monitoring/start", json={})
            
            # Get initial memory usage
            initial_status = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
            if initial_status.status_code == 200:
                initial_data = initial_status.json()
                memory_results["initial_memory_mb"] = initial_data.get("current_metrics", {}).get("memory_usage_mb", 0)
            
            # Run monitoring for a period and track memory
            memory_readings = []
            
            for i in range(10):  # 10 readings over time
                await asyncio.sleep(2)  # Wait 2 seconds between readings
                
                # Trigger some activity
                await self.client.post(
                    f"{self.base_url}/api/v1/data/ingest",
                    json={"symbols": ["bitcoin"], "sources": ["coingecko"], "continuous": False}
                )
                
                # Get memory reading
                status_response = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    memory_mb = status_data.get("current_metrics", {}).get("memory_usage_mb", 0)
                    memory_readings.append(memory_mb)
            
            # Get final memory usage
            final_status = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
            if final_status.status_code == 200:
                final_data = final_status.json()
                memory_results["final_memory_mb"] = final_data.get("current_metrics", {}).get("memory_usage_mb", 0)
            
            # Stop monitoring
            await self.client.post(f"{self.base_url}/api/v1/monitoring/stop")
            
            # Analyze memory usage
            if memory_readings:
                memory_results["peak_memory_mb"] = max(memory_readings)
                memory_results["memory_growth_mb"] = memory_results["final_memory_mb"] - memory_results["initial_memory_mb"]
                
                # Check if memory usage is stable (not growing excessively)
                memory_results["memory_stable"] = memory_results["memory_growth_mb"] < 100  # Less than 100MB growth
            
            memory_results["test_passed"] = (
                memory_results["peak_memory_mb"] < 1000 and  # Less than 1GB peak
                memory_results["memory_stable"]  # Stable memory usage
            )
            
            print(f"   ✅ Initial memory: {memory_results['initial_memory_mb']:.1f}MB")
            print(f"   ✅ Peak memory: {memory_results['peak_memory_mb']:.1f}MB")
            print(f"   ✅ Memory growth: {memory_results['memory_growth_mb']:.1f}MB")
            print(f"   ✅ Memory stable: {memory_results['memory_stable']}")
            
        except Exception as e:
            print(f"   ❌ Memory usage test failed: {e}")
            memory_results["error"] = str(e)
        
        return memory_results
    
    async def _test_concurrent_operations(self) -> Dict[str, Any]:
        """Test concurrent monitoring operations."""
        print("   Testing concurrent monitoring operations...")
        
        concurrency_results = {
            "concurrent_monitoring_sessions": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "operation_conflicts": 0,
            "test_passed": False
        }
        
        try:
            # Test multiple concurrent start/stop operations
            async def start_stop_cycle():
                try:
                    # Start monitoring
                    start_response = await self.client.post(f"{self.base_url}/api/v1/monitoring/start", json={})
                    await asyncio.sleep(1)
                    
                    # Check status
                    status_response = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
                    await asyncio.sleep(1)
                    
                    # Stop monitoring
                    stop_response = await self.client.post(f"{self.base_url}/api/v1/monitoring/stop")
                    
                    return {
                        "success": all(r.status_code == 200 for r in [start_response, status_response, stop_response]),
                        "start_status": start_response.status_code,
                        "status_status": status_response.status_code,
                        "stop_status": stop_response.status_code
                    }
                except Exception as e:
                    return {"success": False, "error": str(e)}
            
            # Run concurrent operations
            concurrent_operations = 5
            tasks = [start_stop_cycle() for _ in range(concurrent_operations)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analyze results
            successful_ops = sum(1 for r in results if isinstance(r, dict) and r.get("success", False))
            failed_ops = len(results) - successful_ops
            
            concurrency_results.update({
                "concurrent_monitoring_sessions": concurrent_operations,
                "successful_operations": successful_ops,
                "failed_operations": failed_ops,
                "operation_conflicts": failed_ops,  # Assume failures are due to conflicts
            })
            
            concurrency_results["test_passed"] = (
                successful_ops >= concurrent_operations * 0.8  # 80% success rate acceptable for concurrent ops
            )
            
            print(f"   ✅ Concurrent operations: {concurrent_operations}")
            print(f"   ✅ Successful: {successful_ops}")
            print(f"   ✅ Failed: {failed_ops}")
            print(f"   ✅ Success rate: {successful_ops/concurrent_operations:.1%}")
            
        except Exception as e:
            print(f"   ❌ Concurrent operations test failed: {e}")
            concurrency_results["error"] = str(e)
        
        return concurrency_results
    
    async def _test_long_running_stability(self) -> Dict[str, Any]:
        """Test long-running monitoring stability."""
        print("   Testing long-running monitoring stability...")
        
        stability_results = {
            "monitoring_duration_seconds": 0,
            "detection_cycles_completed": 0,
            "errors_encountered": 0,
            "memory_growth_mb": 0,
            "uptime_stable": False,
            "test_passed": False
        }
        
        try:
            # Start monitoring
            start_response = await self.client.post(
                f"{self.base_url}/api/v1/monitoring/start",
                json={"monitoring_interval_seconds": 10}  # Fast interval for testing
            )
            
            if start_response.status_code != 200:
                stability_results["error"] = "Failed to start monitoring"
                return stability_results
            
            # Monitor for a period
            monitoring_duration = 30  # 30 seconds
            start_time = time.time()
            
            initial_memory = 0
            final_memory = 0
            error_count = 0
            
            # Get initial state
            initial_status = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
            if initial_status.status_code == 200:
                initial_data = initial_status.json()
                initial_memory = initial_data.get("current_metrics", {}).get("memory_usage_mb", 0)
            
            # Monitor stability
            while time.time() - start_time < monitoring_duration:
                await asyncio.sleep(5)
                
                try:
                    # Check status
                    status_response = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
                    
                    if status_response.status_code != 200:
                        error_count += 1
                    else:
                        status_data = status_response.json()
                        if not status_data.get("monitoring_active", False):
                            error_count += 1
                            
                except Exception:
                    error_count += 1
            
            # Get final state
            final_status = await self.client.get(f"{self.base_url}/api/v1/monitoring/status")
            if final_status.status_code == 200:
                final_data = final_status.json()
                final_memory = final_data.get("current_metrics", {}).get("memory_usage_mb", 0)
                stability_results["detection_cycles_completed"] = final_data.get("session_statistics", {}).get("detection_cycles_completed", 0)
            
            # Stop monitoring
            await self.client.post(f"{self.base_url}/api/v1/monitoring/stop")
            
            # Analyze stability
            actual_duration = time.time() - start_time
            
            stability_results.update({
                "monitoring_duration_seconds": actual_duration,
                "errors_encountered": error_count,
                "memory_growth_mb": final_memory - initial_memory,
                "uptime_stable": error_count == 0,
            })
            
            stability_results["test_passed"] = (
                stability_results["uptime_stable"] and
                stability_results["detection_cycles_completed"] >= 2 and  # At least 2 cycles
                stability_results["memory_growth_mb"] < 50  # Less than 50MB growth
            )
            
            print(f"   ✅ Monitoring duration: {actual_duration:.1f}s")
            print(f"   ✅ Detection cycles: {stability_results['detection_cycles_completed']}")
            print(f"   ✅ Errors encountered: {error_count}")
            print(f"   ✅ Memory growth: {stability_results['memory_growth_mb']:.1f}MB")
            print(f"   ✅ Uptime stable: {stability_results['uptime_stable']}")
            
        except Exception as e:
            print(f"   ❌ Long-running stability test failed: {e}")
            stability_results["error"] = str(e)
        
        return stability_results
    
    def _generate_performance_report(self, baseline_results, load_results, 
                                   memory_results, concurrency_results, 
                                   stability_results) -> Dict[str, Any]:
        """Generate comprehensive performance validation report."""
        
        # Calculate performance scores
        performance_scores = {
            "baseline_performance": 1.0 if baseline_results.get("test_passed", False) else 0.0,
            "load_performance": 1.0 if load_results.get("test_passed", False) else 0.0,
            "memory_efficiency": 1.0 if memory_results.get("test_passed", False) else 0.0,
            "concurrency_handling": 1.0 if concurrency_results.get("test_passed", False) else 0.0,
            "long_running_stability": 1.0 if stability_results.get("test_passed", False) else 0.0
        }
        
        overall_performance_score = sum(performance_scores.values()) / len(performance_scores)
        
        # Determine production readiness
        production_ready = overall_performance_score >= 0.8  # 80% of performance tests must pass
        
        report = {
            "validation_timestamp": datetime.now(timezone.utc).isoformat(),
            "overall_performance_score": overall_performance_score,
            "production_ready": production_ready,
            "performance_scores": performance_scores,
            "performance_benchmarks": {
                "baseline_response_times": {
                    "health_check_ms": baseline_results.get("health_check", {}).get("avg_ms", 0),
                    "status_check_ms": baseline_results.get("status_check", {}).get("avg_ms", 0),
                    "pattern_analysis_ms": baseline_results.get("pattern_analysis", {}).get("avg_ms", 0),
                    "data_ingestion_ms": baseline_results.get("data_ingestion", {}).get("avg_ms", 0)
                },
                "load_performance": {
                    "requests_per_second": load_results.get("requests_per_second", 0),
                    "success_rate": load_results.get("success_rate", 0),
                    "avg_response_time_ms": load_results.get("avg_response_time_ms", 0)
                },
                "memory_efficiency": {
                    "peak_memory_mb": memory_results.get("peak_memory_mb", 0),
                    "memory_growth_mb": memory_results.get("memory_growth_mb", 0),
                    "memory_stable": memory_results.get("memory_stable", False)
                },
                "stability_metrics": {
                    "uptime_stable": stability_results.get("uptime_stable", False),
                    "detection_cycles": stability_results.get("detection_cycles_completed", 0),
                    "errors_encountered": stability_results.get("errors_encountered", 0)
                }
            },
            "detailed_results": {
                "baseline_performance": baseline_results,
                "load_performance": load_results,
                "memory_efficiency": memory_results,
                "concurrency_handling": concurrency_results,
                "long_running_stability": stability_results
            }
        }
        
        # Print performance summary
        print(f"\n⚡ PRODUCTION PERFORMANCE SUMMARY:")
        print(f"   Overall Performance Score: {overall_performance_score:.1%}")
        print(f"   Baseline Performance: {'✅ PASS' if performance_scores['baseline_performance'] == 1.0 else '❌ FAIL'}")
        print(f"   Load Performance: {'✅ PASS' if performance_scores['load_performance'] == 1.0 else '❌ FAIL'}")
        print(f"   Memory Efficiency: {'✅ PASS' if performance_scores['memory_efficiency'] == 1.0 else '❌ FAIL'}")
        print(f"   Concurrency Handling: {'✅ PASS' if performance_scores['concurrency_handling'] == 1.0 else '❌ FAIL'}")
        print(f"   Long-running Stability: {'✅ PASS' if performance_scores['long_running_stability'] == 1.0 else '❌ FAIL'}")
        print(f"   Production Ready: {'✅ YES' if production_ready else '❌ NO'}")
        
        return report


async def run_production_performance_validation():
    """Run comprehensive production performance validation."""
    tester = ProductionPerformanceTester()
    return await tester.run_performance_validation()


if __name__ == "__main__":
    asyncio.run(run_production_performance_validation())
