"""Initial schema with TimescaleDB hypertables

Revision ID: 0001
Revises: 
Create Date: 2025-07-05 04:04:03.721055

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "0001"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create schemas
    op.execute("CREATE SCHEMA IF NOT EXISTS market_data")
    op.execute("CREATE SCHEMA IF NOT EXISTS risk_analysis")
    op.execute("CREATE SCHEMA IF NOT EXISTS audit_logs")
    op.execute("CREATE SCHEMA IF NOT EXISTS quantum_models")
    
    # Create price_events table
    op.create_table("price_events",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("timestamp", sa.DateTime(timezone=True), nullable=False),
        sa.Column("symbol", sa.String(length=20), nullable=False),
        sa.Column("exchange", sa.String(length=50), nullable=False),
        sa.Column("price", sa.DECIMAL(precision=20, scale=8), nullable=False),
        sa.Column("volume", sa.DECIMAL(precision=20, scale=8), nullable=False),
        sa.Column("market_cap", sa.DECIMAL(precision=20, scale=2), nullable=True),
        sa.Column("source", sa.String(length=50), nullable=False),
        sa.Column("data_hash", sa.String(length=64), nullable=False),
        sa.Column("signature", sa.String(length=128), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.CheckConstraint("price > 0", name="price_positive"),
        sa.CheckConstraint("volume >= 0", name="volume_positive"),
        sa.PrimaryKeyConstraint("id"),
        schema="market_data"
    )
    op.create_index("idx_price_events_symbol_time", "price_events", ["symbol", "timestamp"], unique=False, schema="market_data")
    op.create_index("idx_price_events_exchange_time", "price_events", ["exchange", "timestamp"], unique=False, schema="market_data")
    op.create_index("idx_price_events_hash", "price_events", ["data_hash"], unique=False, schema="market_data")
    
    # Create TimescaleDB hypertable for price_events
    op.execute("SELECT create_hypertable(\"market_data.price_events\", \"timestamp\")")


def downgrade() -> None:
    # Drop tables (hypertables will be dropped automatically)
    op.drop_table("price_events", schema="market_data")
    
    # Drop schemas
    op.execute("DROP SCHEMA IF EXISTS quantum_models CASCADE")
    op.execute("DROP SCHEMA IF EXISTS audit_logs CASCADE")
    op.execute("DROP SCHEMA IF EXISTS risk_analysis CASCADE")
    op.execute("DROP SCHEMA IF EXISTS market_data CASCADE")
