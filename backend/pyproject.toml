[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quantum-market-intelligence"
version = "1.0.0"
description = "Quantum Market Intelligence Hub - Financial-grade real-time analytics with quantum finance models"
authors = [
    {name = "Quantum Market Intelligence Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
keywords = ["finance", "quantum", "machine-learning", "real-time", "analytics", "blockchain"]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "asyncpg>=0.29.0",
    "sqlalchemy[asyncio]>=2.0.23",
    "pydantic[email]>=2.5.0",
    "confluent-kafka[avro]>=2.3.0",
    "redis[hiredis]>=5.0.1",
    "numpy>=1.24.4",
    "scipy>=1.11.4",
    "pandas>=2.1.4",
    "scikit-learn>=1.3.2",
    "cryptography>=41.0.8",
    "structlog>=23.2.0",
    "python-dotenv>=1.0.0",
    "opentelemetry-api>=1.21.0",
    "thirdweb-sdk>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "faker>=20.1.0",
    "httpx>=0.25.2",
]
docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocstrings[python]>=0.24.0",
]

[project.urls]
Homepage = "https://github.com/quantum-market-intelligence/quantum-hub"
Documentation = "https://docs.quantum-market.ai"
Repository = "https://github.com/quantum-market-intelligence/quantum-hub"
Issues = "https://github.com/quantum-market-intelligence/quantum-hub/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "asyncpg"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "confluent_kafka.*",
    "fastavro.*",
    "ccxt.*",
    "yfinance.*",
    "ta.*",
    "quantlib.*",
    "praw.*",
    "tweepy.*",
    "web3.*",
    "thirdweb.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101", "B601"]
